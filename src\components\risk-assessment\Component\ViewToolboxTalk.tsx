import React from 'react';
import moment from 'moment';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import ImageComponent from '@/components/common/ImageComponent';

interface User {
  firstName: string;
}

interface Location {
  name: string;
}

interface WorkActivity {
  name: string;
}

interface RiskAssessment {
  workActivity: WorkActivity;
}

interface Hazard {
  id: string;
  name: string;
  image: string;
  toolbox_value: string;
  toolbox_remarks?: string;
}

interface Control {
  value: string;
  toolbox_value: string;
  toolbox_remarks?: string;
}

interface Task {
  activity?: {
    type: string;
    name: string;
  };
  hazards?: {
    selected: Hazard[];
  };
  currentControl?: {
    option: Control[];
  };
}

interface Controls {
  isAdditionalControlsIdentified: boolean;
  describeAdditionalControls?: string;
  teamBrief: string;
  teamBriefRemarks?: string;
}

interface SignStatus {
  signedBy: User;
  sign: string;
  signedDate: string;
}

interface CloseOutChallenge {
  unexpectedChallenges: string;
  remarks: string;
}

interface FormData {
  commenceDate?: string;
  conductedBy?: User;
  riskAssessment?: RiskAssessment;
  locationOne?: Location;
  locationTwo?: Location;
  locationThree?: Location;
  locationFour?: Location;
  noOfPersonsParticipated?: number;
  uploads: string[];
  tasks?: Task[];
  controls: Controls;
  toolboxSignStatuses: SignStatus[];
  isCloseOutChallenges: boolean;
  closeOutChallenges?: CloseOutChallenge[];
}

interface ViewToolboxTalkProps {
  formData: FormData;
}

const ViewToolboxTalk: React.FC<ViewToolboxTalkProps> = ({ formData }) => {
  const getLocationString = () => {
    return [
      formData?.locationOne?.name,
      formData?.locationTwo?.name,
      formData?.locationThree?.name,
      formData?.locationFour?.name
    ]
      .filter(Boolean)
      .join(' > ') || "-";
  };

  const getStatusBadge = (value: string) => {
    switch (value) {
      case "Yes":
        return <Badge variant="default" className="bg-green-600">Yes</Badge>;
      case "No":
        return <Badge variant="destructive">No</Badge>;
      case "Not Applicable":
        return <Badge variant="secondary">Not Applicable</Badge>;
      default:
        return <Badge variant="outline">{value}</Badge>;
    }
  };

  return (
    <div className="container mx-auto p-4 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Toolbox Talk Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h6 className="font-semibold">Commence TBT:</h6>
              <p className="text-muted-foreground">
                {formData.commenceDate
                  ? moment(formData.commenceDate).format("DD-MM-YYYY HH:mm")
                  : "Not started"}
              </p>
            </div>
            <div>
              <h6 className="font-semibold">Conducted By:</h6>
              <p className="text-muted-foreground">
                {formData.conductedBy?.firstName || "-"}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h6 className="font-semibold">Work Activity:</h6>
              <p className="text-muted-foreground">
                {formData.riskAssessment?.workActivity?.name || "-"}
              </p>
            </div>
            <div>
              <h6 className="font-semibold">Location:</h6>
              <p className="text-muted-foreground">{getLocationString()}</p>
            </div>
          </div>

          <div>
            <h6 className="font-semibold">Number of Persons Participated:</h6>
            <p className="text-muted-foreground">
              {formData.noOfPersonsParticipated ?? "-"}
            </p>
          </div>

          {/* Evidence */}
          <div>
            <h6 className="font-semibold mb-3">Evidence:</h6>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {formData.uploads?.map((item, index) => (
                <div key={index} className="border rounded-lg p-2">
                  <ImageComponent size={'100'} fileName={item} />
                </div>
              ))}
            </div>
          </div>

          <Separator />

          {/* Sub Activities */}
          {formData?.tasks?.map((group, groupIndex) => (
            <div key={groupIndex} className="space-y-4">
              <h6 className="text-lg font-semibold text-primary border-b pb-2">
                Sub-Activity {groupIndex + 1}
              </h6>

              {group.activity?.type === "activity" && (
                <div>
                  <span className="font-semibold">Activity:</span> {group.activity.name}
                </div>
              )}

              {/* Hazards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {group.hazards?.selected?.map((hazard) => (
                  <Card key={hazard.id} className="bg-gray-50">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-4">
                        <img
                          src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`}
                          alt={hazard.name}
                          className="w-15 h-15 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h6 className="font-semibold">{hazard.name}</h6>
                          <div className="mt-2">
                            {getStatusBadge(hazard.toolbox_value)}
                          </div>
                          {(hazard.toolbox_value === "No" || hazard.toolbox_value === "Not Applicable") && (
                            <div className="mt-2">
                              <span className="font-semibold">Remarks:</span> {hazard.toolbox_remarks}
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Controls */}
              <div className="space-y-3">
                {group.currentControl?.option?.map((control, controlIndex) => (
                  <div key={controlIndex} className="border-l-4 border-primary pl-4 py-2">
                    <div className="mb-2">{control.value || "No control description provided"}</div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">Implemented:</span>
                      {getStatusBadge(control.toolbox_value)}
                    </div>
                    {(control.toolbox_value === "No" || control.toolbox_value === "Not Applicable") && (
                      <div className="mt-2">
                        <span className="font-semibold">Remarks:</span> {control.toolbox_remarks}
                      </div>
                    )}
                  </div>
                ))}
              </div>

              <Separator />
            </div>
          ))}

          {/* Additional Controls */}
          <div className="space-y-4">
            <h5 className="text-lg font-semibold border-b pb-2">Additional Controls</h5>
            <div>
              <span className="font-semibold">Identified:</span>{" "}
              {getStatusBadge(formData.controls.isAdditionalControlsIdentified ? "Yes" : "No")}
            </div>
            {formData.controls.isAdditionalControlsIdentified && (
              <div>
                <span className="font-semibold">Description:</span>{" "}
                {formData.controls.describeAdditionalControls}
              </div>
            )}
            <div className="flex items-center gap-2">
              <span className="font-semibold">Team Briefed:</span>
              {getStatusBadge(formData.controls.teamBrief)}
            </div>
            {(formData.controls.teamBrief === "No" || formData.controls.teamBrief === "Not Applicable") && (
              <div>
                <span className="font-semibold">Remarks:</span> {formData.controls.teamBriefRemarks}
              </div>
            )}
          </div>

          <Separator />

          {/* Signatures */}
          <div className="space-y-4">
            <h5 className="text-lg font-semibold border-b pb-2">Team Member Acknowledgement</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {formData.toolboxSignStatuses?.map((item, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div>
                        <span className="font-semibold">Member:</span> {item.signedBy?.firstName || "-"}
                      </div>
                      <div>
                        <ImageComponent size={150} fileName={item.sign} />
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {moment(item.signedDate).format("DD-MM-YYYY HH:mm")}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <Separator />

          {/* Close Out */}
          <div className="space-y-4">
            <h5 className="text-lg font-semibold border-b pb-2">Close Out</h5>
            <div>
              <span className="font-semibold">Unexpected Challenges:</span>{" "}
              {getStatusBadge(formData.isCloseOutChallenges ? "Yes" : "No")}
            </div>
            {formData.isCloseOutChallenges && formData.closeOutChallenges?.map((item, index) => (
              <Card key={index} className="bg-gray-50">
                <CardContent className="p-4">
                  <div className="space-y-2">
                    <div>
                      <span className="font-semibold">Challenge:</span> {item.unexpectedChallenges}
                    </div>
                    <div>
                      <span className="font-semibold">Remarks:</span> {item.remarks}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ViewToolboxTalk;
