import React from "react";
import { Card } from "@/components/ui/card";
import { Plus } from "lucide-react";

interface DocumentDraggableItemProps {
  type: string;
  icon: React.ReactNode;
  label: string;
  description?: string;
  onDragStart: (type: string) => void;
  onComponentClick?: (type: string) => void;
}

const DocumentDraggableItem: React.FC<DocumentDraggableItemProps> = ({
  type,
  icon,
  label,
  description,
  onDragStart,
  onComponentClick,
}) => {
  const handleDragStart = (e: React.DragEvent) => {
    console.log('DocumentDraggableItem drag started:', type);
    e.dataTransfer.setData("componentType", type);
    e.dataTransfer.setData("text/plain", type); // Fallback for some browsers
    e.dataTransfer.effectAllowed = "copy";
    e.dataTransfer.dropEffect = "copy";
    console.log('Data set in dataTransfer:', type);
    console.log('DataTransfer object:', e.dataTransfer);
    onDragStart(type);
  };

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('DocumentDraggableItem clicked:', type);
    if (onComponentClick) {
      console.log('Calling onComponentClick for:', type);
      onComponentClick(type);
    } else {
      console.log('onComponentClick is not defined');
    }
  };

  const handleDragEnd = (e: React.DragEvent) => {
    e.dataTransfer.clearData();
  };

  return (
    <Card
      draggable
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      className="component-item p-3 mb-2 cursor-grab active:cursor-grabbing flex items-center space-x-3 hover:bg-slate-50 dark:hover:bg-slate-800 transition-all duration-200 hover:shadow-md border-slate-200 dark:border-slate-700 group relative"
    >
      <div className="text-primary p-2 bg-primary/10 rounded-md group-hover:bg-primary/20 transition-colors duration-200">
        {icon}
      </div>
      <div className="flex-1">
        <h4 className="font-medium text-sm">{label}</h4>
        {description && <p className="text-xs text-muted-foreground">{description}</p>}
      </div>
      
      {/* Click to add button */}
      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div
          className="w-6 h-6 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center cursor-pointer"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            handleClick(e);
          }}
          title="Click to add"
        >
          <Plus className="h-3 w-3 text-white" />
        </div>
      </div>

      {/* Drag indicator */}
      <div className="absolute left-1 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-50 transition-opacity duration-200">
        <div className="flex flex-col space-y-0.5">
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
        </div>
      </div>
    </Card>
  );
};

export default DocumentDraggableItem;
