import axios from 'axios';
import { API_BASE_URL } from '../constants/index';
import { store } from '../store';
import { setLogout } from '../store/slices/authSlice';
import { refreshAccessToken, handleTokenRefreshFailure } from '../utils/tokenRefresh';

// Create an axios instance with default configuration
const API = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include the auth token in requests
API.interceptors.request.use(
  (config) => {
    // Get the token from localStorage (using the correct key)
    const token = localStorage.getItem('access_token');

    // If token exists, add it to the headers
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors and refresh tokens
API.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 Unauthorized errors (token expired)
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const refresh_token = localStorage.getItem('refresh_token');

      if (refresh_token) {
        try {
          console.log('Attempting to refresh access token...');

          // Use the utility function to refresh the token
          const newAccessToken = await refreshAccessToken();

          console.log('Successfully refreshed access token');

          // Update the Authorization header for the failed request
          originalRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;

          // Retry the original request with the new token
          return API(originalRequest);
        } catch (refreshError) {
          console.error('Error refreshing token:', refreshError);

          // Use the utility function to handle logout
          handleTokenRefreshFailure();

          return Promise.reject(error);
        }
      } else {
        // If no refresh token is available, log the user out
        console.log('No refresh token available - Logging out user');

        handleTokenRefreshFailure();

        return Promise.reject(error);
      }
    }

    // If the error is not due to token expiration, reject the promise
    return Promise.reject(error);
  }
);

export default API;
