import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';
import { CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Check, ChevronDown, X } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import AllFilterLocation from './AllFilterLocation';
import API from '@/services/axiosAPI';
import { API_BASE_URL } from '@/constants/index';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import FileUploadComponent from '@/components/common/FileUploadComponent';

export interface ObservationSubmitData {
  id?: string;
  maskId?: string;
  observationCategory: string;
  observationType: string;
  observationActOrCondition: string;
  description: string;
  comments: string;
  rectifiedOnSpot: boolean;
  actionTaken: string;
  actionToBeTaken: string;
  isReviewerRequired: boolean;
  reviewerId: string;
  actionOwnerId?: string;
  multiActionOwnersIds?: string[];
  dueDate: string;
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
  uploads: string[];
  evidence: string[];
  isQR: boolean;
}

interface RecordObservationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: ObservationSubmitData) => void;
}

// Static data
const categories = ["Environment", "HSE", "Social"];
const types = ["Safe", "Unsafe"];
const actionConditions = ["Act", "Condition"];

// API endpoints
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const OBSERVATION_REPORT_URL = `${API_BASE_URL}/observation-reports`;

// Form schema
const formSchema = z.object({
  category: z.string().optional(),
  type: z.string().min(1, "Observation Type is required"),
  actionCondition: z.string().min(1, "Observation Act or Condition is required"),
  description: z.string().min(5, "Description must be at least 5 characters"),
  rectifiedOnSpot: z.enum(["yes", "no"]).optional(),
  actionTaken: z.string().optional(),
  needsReviewer: z.enum(["yes", "no"]).optional(),
  reviewer: z.string().optional(),
  actionToBeTaken: z.string().optional(),
  dueDate: z.date().optional(),
  actionOwner: z.array(z.string()).max(2, "You can select maximum 2 action owners").optional(),
});

type FormValues = z.infer<typeof formSchema>;

// Define interfaces for user data
interface UserOption {
  label: string;
  value: string;
}

interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role?: string;
  department?: string;
  position?: string;
}

interface LocationData {
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
  uploads: string[];
  evidence: string[];
  [key: string]: string | string[];
}

const RecordObservationModal: React.FC<RecordObservationModalProps> = ({
  open,
  onOpenChange,
  onSubmit
}) => {
  const { toast } = useToast();
  const { accessToken } = useSelector((state: RootState) => state.auth);

  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [formData, setFormData] = useState<LocationData>({
    locationOneId: '',
    locationTwoId: '',
    locationThreeId: '',
    locationFourId: '',
    locationFiveId: '',
    locationSixId: '',
    uploads: [],
    evidence: []
  });

  // State for reviewers and action owners
  const [reviewers, setReviewers] = useState<UserOption[]>([]);
  const [actionOwners, setActionOwners] = useState<UserOption[]>([]);
  const [actionOwnerDropdownOpen, setActionOwnerDropdownOpen] = useState(false);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category: undefined,
      type: undefined,
      actionCondition: undefined,
      description: "",
      rectifiedOnSpot: undefined,
      actionTaken: "",
      needsReviewer: undefined,
      reviewer: undefined,
      actionToBeTaken: "",
      actionOwner: [],
    }
  });

  // Watch values for conditional rendering
  const watchType = form.watch("type");
  const watchRectifiedOnSpot = form.watch("rectifiedOnSpot");
  const watchNeedsReviewer = form.watch("needsReviewer");

  // Handle file uploads
  const handleSupportingFilesUpload = (fileNames: string[]) => {
    setFormData(prev => ({
      ...prev,
      uploads: fileNames
    }));
  };

  const handleEvidenceFilesUpload = (fileNames: string[]) => {
    setFormData(prev => ({
      ...prev,
      evidence: fileNames
    }));
  };

  // Handle location selection
  const handleFilter = (
    locationOneId: string,
    locationTwoId?: string,
    locationThreeId?: string,
    locationFourId?: string,
    locationFiveId?: string,
    locationSixId?: string
  ) => {
    setFormData((prev: LocationData) => ({
      ...prev,
      locationOneId,
      locationTwoId: locationTwoId || '',
      locationThreeId: locationThreeId || '',
      locationFourId: locationFourId || '',
      locationFiveId: locationFiveId || '',
      locationSixId: locationSixId || '',
    }));
  };

  // Function to fetch action owners
  const getActionOwner = useCallback(async () => {
    if (!accessToken) {
      console.warn('Cannot fetch action owners: No access token available');
      return;
    }

    try {
      const response = await API.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: formData.locationOneId || "",
        locationTwoId: formData.locationTwoId || "",
        locationThreeId: formData.locationThreeId || "",
        locationFourId: formData.locationFourId || "",
        mode: 'obsactionowner'
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (response.status === 200) {
        const data = response.data.map((item: UserData) => ({
          label: item.firstName,
          value: item.id
        }));
        setActionOwners(data);
      }
    } catch (error) {
      console.error("Error fetching action owners:", error);
      toast({
        title: "Error",
        description: "Failed to fetch action owners. Please try again.",
        variant: "destructive"
      });
    }
  }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId, accessToken, toast]);

  // Function to fetch reviewers
  const getObsReviewer = useCallback(async () => {
    if (!accessToken) {
      console.warn('Cannot fetch reviewers: No access token available');
      return;
    }

    try {
      const response = await API.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: formData.locationOneId || "",
        locationTwoId: formData.locationTwoId || "",
        locationThreeId: formData.locationThreeId || "",
        locationFourId: formData.locationFourId || "",
        mode: 'obsreviewer'
      }, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (response.status === 200) {
        const data = response.data.map((item: UserData) => ({
          label: item.firstName,
          value: item.id
        }));
        setReviewers(data);
      }
    } catch (error) {
      console.error("Error fetching reviewers:", error);
      toast({
        title: "Error",
        description: "Failed to fetch reviewers. Please try again.",
        variant: "destructive"
      });
    }
  }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId, accessToken, toast]);

  // No cleanup needed for the new component approach

  // Fetch reviewers and action owners when location changes
  useEffect(() => {
    if (formData.locationOneId) {
      getActionOwner();
      getObsReviewer();
    }
  }, [formData, getActionOwner, getObsReviewer]);

  // Function to handle form submission
  const handleFormSubmit = async (values: FormValues) => {
    // We'll collect errors in an object
    const newFieldErrors: Record<string, string> = {};

    // 1. LOCATION: at least locationOneId is required
    if (!formData.locationOneId) {
      newFieldErrors.locationOneId = 'Location is required.';
    }

    // 2. observationCategory is required
    if (!values.category) {
      newFieldErrors.category = 'Observation Category is required.';
    }

    // 3. observationType is required
    if (!values.type) {
      newFieldErrors.type = 'Observation Type is required.';
    }

    // 4. observationActOrCondition is required
    if (!values.actionCondition) {
      newFieldErrors.actionCondition = 'Observation Act or Condition is required.';
    }

    // 5. description is required
    if (!values.description?.trim()) {
      newFieldErrors.description = 'Description is required.';
    }

    // 6. supporting files: min 2 attachments
    if (!formData.uploads || formData.uploads.length < 2) {
      newFieldErrors.uploads = 'Please attach at least 2 supporting documents/images.';
    }

    // 7. If it's UNSAFE...
    if (values.type === 'Unsafe') {
      // rectifiedOnSpot is required (yes/no)
      if (!values.rectifiedOnSpot) {
        newFieldErrors.rectifiedOnSpot = 'Rectified on Spot is required.';
      } else {
        // If rectifiedOnSpot = yes, actionTaken & at least 2 evidence attachments
        if (values.rectifiedOnSpot === "yes") {
          if (!values.actionTaken?.trim()) {
            newFieldErrors.actionTaken = 'Action Taken is required.';
          }
          if (!formData.evidence || formData.evidence.length < 2) {
            newFieldErrors.evidence = 'Please attach at least 2 evidence files.';
          }
        } else {
          // rectifiedOnSpot = no
          if (!values.needsReviewer) {
            newFieldErrors.needsReviewer = 'Please specify if Reviewer is required.';
          } else {
            if (values.needsReviewer === "yes" && !values.reviewer) {
              newFieldErrors.reviewer = 'Please select a Reviewer.';
            }
            if (values.needsReviewer === "no") {
              if (!values.actionToBeTaken?.trim()) {
                newFieldErrors.actionToBeTaken = 'Action to be Taken is required.';
              }
              if (!values.dueDate) {
                newFieldErrors.dueDate = 'Due Date is required.';
              }
              if (!values.actionOwner || values.actionOwner.length === 0) {
                newFieldErrors.actionOwner = 'At least one Action Owner is required.';
              }
            }
          }
        }
      }
    }

    // Now set the errors in state
    setFieldErrors(newFieldErrors);

    // If we have ANY errors, do not proceed
    if (Object.keys(newFieldErrors).length > 0) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form.",
        variant: "destructive"
      });
      return; // Stop here
    }

    // Otherwise, clear errors and proceed
    setFieldErrors({});

    // Format the data according to the required structure
    const submissionData = {
      observationCategory: values.category,
      observationType: values.type,
      observationActOrCondition: values.actionCondition,
      description: values.description,
      comments: "",

      // Handle rectifiedOnSpot as boolean
      rectifiedOnSpot: values.rectifiedOnSpot === "yes",

      // Handle action taken fields
      actionTaken: values.actionTaken || "",
      actionToBeTaken: values.actionToBeTaken || "",

      // Handle reviewer fields
      isReviewerRequired: values.needsReviewer === "yes",
      reviewerId: values.reviewer || "",

      // Handle action owner - single vs multiple logic with IDs only
      ...(values.actionOwner && values.actionOwner.length === 1
        ? { actionOwnerId: values.actionOwner[0] }
        : values.actionOwner && values.actionOwner.length === 2
        ? { multiActionOwnersIds: values.actionOwner }
        : {}),

      // Handle due date - always pass a valid ISO date-time string
      // If no date is selected, use the current date
      dueDate: values.dueDate ? values.dueDate.toISOString() : new Date().toISOString(),

      // Handle location IDs
      locationOneId: formData.locationOneId,
      locationTwoId: formData.locationTwoId || "",
      locationThreeId: formData.locationThreeId || "",
      locationFourId: formData.locationFourId || "",
      locationFiveId: formData.locationFiveId || "",
      locationSixId: formData.locationSixId || "",

      // Use the file names from formData
      uploads: formData.uploads,
      evidence: formData.evidence,

      // Additional fields
      isQR: false,
    };

    console.log('Submitting observation data:', submissionData);

    try {
      // Submit the data to the API
      const response = await API.post(OBSERVATION_REPORT_URL, submissionData, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        }
      });

      if (response.status === 200) {
        toast({
          title: "Success",
          description: "Observation report submitted successfully.",
          variant: "default"
        });

        // Close the modal
        onOpenChange(false);

        // Notify parent component
        onSubmit(submissionData);
      }
    } catch (error) {
      console.error('Error submitting observation report:', error);
      toast({
        title: "Error",
        description: "Failed to submit observation report. Please try again.",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Record Observation</DialogTitle>
          <DialogDescription>
            Fill in the details below to record a new safety observation.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
            {/* Location Selection */}
            <div className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <h3 className="text-lg font-medium mb-4">Location Details</h3>
                  <AllFilterLocation
                    handleFilter={handleFilter}
                    getLocation={formData}
                  />
                  {fieldErrors.locationOneId && (
                    <p className="text-red-500 text-xs mt-1">{fieldErrors.locationOneId}</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Category, Type, Action/Condition */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Observation Details</h3>

              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <FormControl>
                      <div className="grid grid-cols-3 gap-2">
                        {categories.map(category => (
                          <Button
                            key={category}
                            type="button"
                            variant={field.value === category ? "default" : "outline"}
                            className={
                              field.value === category
                                ? "bg-primary text-primary-foreground"
                                : category === "Environment"
                                  ? "border-red-500 text-red-500 hover:bg-red-50"
                                  : category === "HSE"
                                    ? "border-orange-500 text-orange-500 hover:bg-orange-50"
                                    : "border-blue-500 text-blue-500 hover:bg-blue-50"
                            }
                            onClick={() => field.onChange(category)}
                          >
                            {category}
                          </Button>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                    {fieldErrors.category && (
                      <p className="text-red-500 text-xs mt-1">{fieldErrors.category}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observation Type <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <div className="grid grid-cols-2 gap-2">
                        {types.map(type => (
                          <Button
                            key={type}
                            type="button"
                            variant={field.value === type ? "default" : "outline"}
                            className={
                              field.value === type
                                ? type === "Safe"
                                  ? "bg-teal-500 hover:bg-teal-600 text-white"
                                  : "bg-red-500 hover:bg-red-600 text-white"
                                : type === "Safe"
                                  ? "border-teal-500 text-teal-500 hover:bg-teal-50"
                                  : "border-red-500 text-red-500 hover:bg-red-50"
                            }
                            onClick={() => field.onChange(type)}
                          >
                            {type}
                          </Button>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                    {fieldErrors.type && (
                      <p className="text-red-500 text-xs mt-1">{fieldErrors.type}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="actionCondition"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Observation Act or Condition <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <div className="grid grid-cols-2 gap-2">
                        {actionConditions.map(item => (
                          <Button
                            key={item}
                            type="button"
                            variant={field.value === item ? "default" : "outline"}
                            className={
                              field.value === item
                                ? item === "Act"
                                  ? "bg-teal-500 hover:bg-teal-600 text-white"
                                  : "bg-teal-500 hover:bg-teal-600 text-white"
                                : item === "Act"
                                  ? "border-teal-500 text-teal-500 hover:bg-teal-50"
                                  : "border-teal-500 text-teal-500 hover:bg-teal-50"
                            }
                            onClick={() => field.onChange(item)}
                          >
                            {item}
                          </Button>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                    {fieldErrors.actionCondition && (
                      <p className="text-red-500 text-xs mt-1">{fieldErrors.actionCondition}</p>
                    )}
                  </FormItem>
                )}
              />
            </div>

            {/* Description and Attachment */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter a detailed description of the observation"
                        {...field}
                        className="min-h-[100px]"
                      />
                    </FormControl>
                    <FormMessage />
                    {fieldErrors.description && (
                      <p className="text-red-500 text-xs mt-1">{fieldErrors.description}</p>
                    )}
                  </FormItem>
                )}
              />

              <FormItem>
                <FormLabel>Attachments</FormLabel>
                <FormControl>
                  <FileUploadComponent
                    onFileUpload={handleSupportingFilesUpload}
                    fieldName="uploads"
                    description="Upload any relevant photos or documents. You can select multiple files."
                    multiple={true}
                    maxFiles={10}
                    initialFiles={formData.uploads}
                  />
                </FormControl>
                {fieldErrors.uploads && (
                  <p className="text-red-500 text-xs mt-1">{fieldErrors.uploads}</p>
                )}
              </FormItem>
            </div>

            {/* Conditional fields based on Type */}
            {watchType === 'Unsafe' && (
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="rectifiedOnSpot"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rectified on Spot?</FormLabel>
                      <FormControl>
                        <div className="grid grid-cols-2 gap-2 max-w-xs">
                          <Button
                            type="button"
                            variant={field.value === "yes" ? "default" : "outline"}
                            className={
                              field.value === "yes"
                                ? "bg-green-500 hover:bg-green-600 text-white"
                                : "border-green-500 text-green-500 hover:bg-green-50"
                            }
                            onClick={() => field.onChange("yes")}
                          >
                            Yes
                          </Button>
                          <Button
                            type="button"
                            variant={field.value === "no" ? "default" : "outline"}
                            className={
                              field.value === "no"
                                ? "bg-red-500 hover:bg-red-600 text-white"
                                : "border-red-500 text-red-500 hover:bg-red-50"
                            }
                            onClick={() => field.onChange("no")}
                          >
                            No
                          </Button>
                        </div>
                      </FormControl>
                      <FormMessage />
                      {fieldErrors.rectifiedOnSpot && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.rectifiedOnSpot}</p>
                      )}
                    </FormItem>
                  )}
                />

                {watchRectifiedOnSpot === 'yes' && (
                  <>
                    <FormField
                      control={form.control}
                      name="actionTaken"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Action Taken</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe the action that was taken to rectify the issue"
                              {...field}
                              className="min-h-[80px]"
                            />
                          </FormControl>
                          <FormMessage />
                          {fieldErrors.actionTaken && (
                            <p className="text-red-500 text-xs mt-1">{fieldErrors.actionTaken}</p>
                          )}
                        </FormItem>
                      )}
                    />

                    <FormItem>
                      <FormLabel>Evidence (Images Only)</FormLabel>
                      <FormControl>
                        <FileUploadComponent
                          onFileUpload={handleEvidenceFilesUpload}
                          fieldName="evidence"
                          description="Upload photos as evidence. Only image files are accepted."
                          accept="image/*"
                          multiple={true}
                          maxFiles={10}
                          initialFiles={formData.evidence}
                        />
                      </FormControl>
                      {fieldErrors.evidence && (
                        <p className="text-red-500 text-xs mt-1">{fieldErrors.evidence}</p>
                      )}
                    </FormItem>
                  </>
                )}

                {watchRectifiedOnSpot === 'no' && (
                  <>
                    <FormField
                      control={form.control}
                      name="needsReviewer"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Do you need to send this Observation to a Reviewer?</FormLabel>
                          <FormControl>
                            <div className="grid grid-cols-2 gap-2 max-w-xs">
                              <Button
                                type="button"
                                variant={field.value === "yes" ? "default" : "outline"}
                                className={
                                  field.value === "yes"
                                    ? "bg-green-500 hover:bg-green-600 text-white"
                                    : "border-green-500 text-green-500 hover:bg-green-50"
                                }
                                onClick={() => field.onChange("yes")}
                              >
                                Yes
                              </Button>
                              <Button
                                type="button"
                                variant={field.value === "no" ? "default" : "outline"}
                                className={
                                  field.value === "no"
                                    ? "bg-red-500 hover:bg-red-600 text-white"
                                    : "border-red-500 text-red-500 hover:bg-red-50"
                                }
                                onClick={() => field.onChange("no")}
                              >
                                No
                              </Button>
                            </div>
                          </FormControl>
                          <FormMessage />
                          {fieldErrors.needsReviewer && (
                            <p className="text-red-500 text-xs mt-1">{fieldErrors.needsReviewer}</p>
                          )}
                        </FormItem>
                      )}
                    />

                    {watchNeedsReviewer === 'yes' && (
                      <FormField
                        control={form.control}
                        name="reviewer"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Reviewer</FormLabel>
                            <Select
                              onValueChange={field.onChange}
                              value={field.value}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select reviewer" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {reviewers.map(reviewer => (
                                  <SelectItem key={reviewer.value} value={reviewer.value}>{reviewer.label}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                            {fieldErrors.reviewer && (
                              <p className="text-red-500 text-xs mt-1">{fieldErrors.reviewer}</p>
                            )}
                          </FormItem>
                        )}
                      />
                    )}

                    {watchNeedsReviewer === 'no' && (
                      <>
                        <FormField
                          control={form.control}
                          name="actionToBeTaken"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Action to be Taken</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Describe the action that needs to be taken"
                                  {...field}
                                  className="min-h-[80px]"
                                />
                              </FormControl>
                              <FormMessage />
                              {fieldErrors.actionToBeTaken && (
                                <p className="text-red-500 text-xs mt-1">{fieldErrors.actionToBeTaken}</p>
                              )}
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="dueDate"
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormLabel>Due Date</FormLabel>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant={"outline"}
                                      className={cn(
                                        "w-full pl-3 text-left font-normal",
                                        !field.value && "text-muted-foreground"
                                      )}
                                    >
                                      {field.value ? (
                                        format(field.value, "PPP")
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) =>
                                      date < new Date()
                                    }
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                              {fieldErrors.dueDate && (
                                <p className="text-red-500 text-xs mt-1">{fieldErrors.dueDate}</p>
                              )}
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="actionOwner"
                          render={({ field }) => {
                            const selectedValues = field.value || [];

                            const handleSelect = (value: string) => {
                              const currentValues = field.value || [];
                              if (currentValues.includes(value)) {
                                // Remove if already selected
                                field.onChange(currentValues.filter((id: string) => id !== value));
                              } else if (currentValues.length < 2) {
                                // Add if not selected and under limit
                                field.onChange([...currentValues, value]);
                              }
                            };

                            const removeSelection = (value: string) => {
                              const currentValues = field.value || [];
                              field.onChange(currentValues.filter((id: string) => id !== value));
                            };

                            return (
                              <FormItem>
                                <FormLabel>Action Owner (Select 1-2 users)</FormLabel>
                                <FormControl>
                                  <Popover open={actionOwnerDropdownOpen} onOpenChange={setActionOwnerDropdownOpen}>
                                    <PopoverTrigger asChild>
                                      <Button
                                        variant="outline"
                                        role="combobox"
                                        aria-expanded={actionOwnerDropdownOpen}
                                        className="w-full justify-between h-auto min-h-[40px] p-2"
                                      >
                                        <div className="flex flex-wrap gap-1 flex-1">
                                          {selectedValues.length > 0 ? (
                                            selectedValues.map((value: string) => {
                                              const owner = actionOwners.find(o => o.value === value);
                                              return (
                                                <Badge
                                                  key={value}
                                                  variant="secondary"
                                                  className="mr-1 mb-1"
                                                >
                                                  {owner?.label || value}
                                                  <X
                                                    className="ml-1 h-3 w-3 cursor-pointer"
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      removeSelection(value);
                                                    }}
                                                  />
                                                </Badge>
                                              );
                                            })
                                          ) : (
                                            <span className="text-muted-foreground">Select action owners (max 2)</span>
                                          )}
                                        </div>
                                        <ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
                                      </Button>
                                    </PopoverTrigger>
                                    <PopoverContent className="w-full p-0" align="start">
                                      <div className="max-h-60 overflow-y-auto">
                                        {actionOwners.length === 0 ? (
                                          <div className="p-4 text-sm text-muted-foreground">
                                            No action owners available
                                          </div>
                                        ) : (
                                          actionOwners.map((owner) => {
                                            const isSelected = selectedValues.includes(owner.value);
                                            const isDisabled = !isSelected && selectedValues.length >= 2;

                                            return (
                                              <div
                                                key={owner.value}
                                                className={`flex items-center px-4 py-2 cursor-pointer hover:bg-accent hover:text-accent-foreground ${
                                                  isDisabled ? 'opacity-50 cursor-not-allowed' : ''
                                                }`}
                                                onClick={() => !isDisabled && handleSelect(owner.value)}
                                              >
                                                <Check
                                                  className={`mr-2 h-4 w-4 ${
                                                    isSelected ? 'opacity-100' : 'opacity-0'
                                                  }`}
                                                />
                                                <span className="flex-1">{owner.label}</span>
                                              </div>
                                            );
                                          })
                                        )}
                                        {selectedValues.length >= 2 && (
                                          <div className="px-4 py-2 text-xs text-muted-foreground border-t">
                                            Maximum 2 action owners selected
                                          </div>
                                        )}
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                </FormControl>
                                <FormMessage />
                                {fieldErrors.actionOwner && (
                                  <p className="text-red-500 text-xs mt-1">{fieldErrors.actionOwner}</p>
                                )}
                              </FormItem>
                            );
                          }}
                        />
                      </>
                    )}
                  </>
                )}
              </div>
            )}

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit">Submit</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default RecordObservationModal;
