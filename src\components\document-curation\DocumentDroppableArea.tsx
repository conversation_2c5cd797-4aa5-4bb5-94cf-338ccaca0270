import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus, FileText } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { v4 as uuidv4 } from "uuid";
import DocumentComponentRenderer from "./DocumentComponentRenderer";

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface DocumentDroppableAreaProps {
  items: DocumentComponent[];
  onAddItem: (item: DocumentComponent) => void;
  onRemoveItem: (id: string) => void;
  onUpdateItem: (id: string, content: any) => void;
  onReorderItems?: (sourceIndex: number, targetIndex: number) => void;
}

const DocumentDroppableArea: React.FC<DocumentDroppableAreaProps> = ({
  items,
  onAddItem,
  onRemoveItem,
  onUpdateItem,
  onReorderItems,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isDragOver) setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const calculateDropIndex = (e: React.DragEvent): number => {
    const dropY = e.clientY;
    const container = e.currentTarget as HTMLElement;
    const componentElements = container.querySelectorAll('[data-component-index]');

    for (let i = 0; i < componentElements.length; i++) {
      const element = componentElements[i] as HTMLElement;
      const rect = element.getBoundingClientRect();
      const midPoint = rect.top + rect.height / 2;

      if (dropY < midPoint) {
        return i;
      }
    }

    return componentElements.length;
  };

  const handleDrop = (e: React.DragEvent) => {
    // Check if the drop occurred on a DropZone
    const target = e.target as HTMLElement;
    const isDropZone = target.closest('[data-drop-zone]');

    if (isDropZone) {
      // Let the DropZone handle this
      return;
    }

    e.preventDefault();
    setIsDragOver(false);

    // Check if it's a new component from sidebar
    const componentType = e.dataTransfer.getData("componentType");

    // Check if it's an existing component being reordered
    const componentId = e.dataTransfer.getData("componentId");

    if (componentType) {
      // Handle new component from sidebar
      handleAddComponent(componentType);
    } else if (componentId && onReorderItems) {
      // Handle component reordering by calculating drop position
      const sourceIndex = items.findIndex(item => item.id === componentId);
      if (sourceIndex !== -1) {
        const targetIndex = calculateDropIndex(e);
        const adjustedTargetIndex = targetIndex > sourceIndex ? targetIndex - 1 : targetIndex;

        if (sourceIndex !== adjustedTargetIndex) {
          onReorderItems(sourceIndex, adjustedTargetIndex);
          toast({
            title: "Component Moved",
            description: `Component moved from position ${sourceIndex + 1} to ${adjustedTargetIndex + 1}.`,
          });
        }
      }
    }
  };

  const handleAddComponent = (componentType: string) => {
    const newComponent: DocumentComponent = {
      id: uuidv4(),
      type: componentType,
      content: getDefaultContent(componentType),
      position: items.length
    };

    onAddItem(newComponent);

    toast({
      title: "Component Added",
      description: `${getComponentLabel(componentType)} has been added to your document.`,
    });
  };

  const handleDropBetween = (index: number, componentType: string) => {
    const newComponent: DocumentComponent = {
      id: uuidv4(),
      type: componentType,
      content: getDefaultContent(componentType),
      position: index
    };

    // Insert at specific position
    const newItems = [...items];
    newItems.splice(index, 0, newComponent);

    // Update positions
    const updatedItems = newItems.map((item, i) => ({ ...item, position: i }));

    onAddItem(newComponent);

    toast({
      title: "Component Added",
      description: `${getComponentLabel(componentType)} has been added at position ${index + 1}.`,
    });
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'document-header':
        return { text: 'Document Title', level: 1 };
      case 'section-header':
        return { text: 'Section Header', level: 2 };
      case 'paragraph':
        return { text: 'Enter your paragraph content here...' };
      case 'bullet-list':
        return { items: ['List item 1', 'List item 2', 'List item 3'] };
      case 'numbered-list':
        return { items: ['First item', 'Second item', 'Third item'] };
      case 'quote':
        return { text: 'Enter your quote here...', author: '' };
      case 'separator':
        return { style: 'line' };
      case 'image':
        return { filename: '', originalName: '', alt: '', caption: '', size: '', fileType: '', uploadedAt: '' };
      case 'file-attachment':
        return { filename: '', originalName: '', description: '', size: '', fileType: '', uploadedAt: '' };
      case 'link':
        return { text: 'Link text', url: '', target: '_blank' };
      default:
        return { text: 'Default content' };
    }
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'file-attachment': 'File Attachment',
      'link': 'Link'
    };
    return labels[type] || type;
  };

  // Component rendering is now handled by EditableComponentRenderer

  return (
    <div className="flex-1 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 h-full overflow-y-auto flex flex-col">
      <div
        className={`flex-1 p-6 transition-all duration-300 ${
          isDragOver ? "bg-blue-50 dark:bg-blue-900/20 border-2 border-dashed border-blue-300" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {items.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center shadow-lg">
                <FileText className="h-12 w-12 text-blue-500 dark:text-blue-400" />
              </div>
              <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-slate-700 to-slate-900 dark:from-slate-200 dark:to-slate-400 bg-clip-text text-transparent">
                Start Building Your Document
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                Drag components from the sidebar to create your document structure
              </p>
              <div className="flex gap-3 items-center justify-center">
                <Button
                  variant="outline"
                  className="px-6 py-3 h-auto hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Browse Components
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-2 max-w-4xl mx-auto">
            {/* Drop zone at the beginning */}
            <DropZone
              index={0}
              onDrop={handleDropBetween}
              isDragOver={dragOverIndex === 0}
              items={items}
              onReorderItems={onReorderItems}
              setDragOverIndex={setDragOverIndex}
              toast={toast}
            />

            {items.map((component, index) => (
              <div key={component.id}>
                <div
                  className="transform transition-all duration-300 hover:scale-[1.02] hover:shadow-lg"
                  style={{ animationDelay: `${index * 100}ms` }}
                  data-component-index={index}
                  data-component-id={component.id}
                >
                  <DocumentComponentRenderer
                    item={component}
                    index={index}
                    onRemove={onRemoveItem}
                    onUpdate={onUpdateItem}
                    onReorder={onReorderItems}
                    isDraggable={true}
                  />
                </div>

                {/* Drop zone after each component */}
                <DropZone
                  index={index + 1}
                  onDrop={handleDropBetween}
                  isDragOver={dragOverIndex === index + 1}
                  items={items}
                  onReorderItems={onReorderItems}
                  setDragOverIndex={setDragOverIndex}
                  toast={toast}
                />
              </div>
            ))}

            {/* Add Component Button */}
            <Button
              variant="outline"
              className="w-full mt-8 border-dashed border-2 py-8 h-auto hover:border-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg"
            >
              <Plus className="h-6 w-6 mr-3 text-blue-500" />
              <span className="text-lg font-medium">Add Another Component</span>
            </Button>

            {/* Extra padding at bottom for better scrolling */}
            <div className="h-32"></div>
          </div>
        )}
      </div>
    </div>
  );
};

// DropZone component for inserting components between existing ones
interface DropZoneProps {
  index: number;
  onDrop: (index: number, componentType: string) => void;
  isDragOver: boolean;
  items: DocumentComponent[];
  onReorderItems?: (sourceIndex: number, targetIndex: number) => void;
  setDragOverIndex: (index: number | null) => void;
  toast: typeof toast;
}

const DropZone: React.FC<DropZoneProps> = ({
  index,
  onDrop,
  isDragOver,
  items,
  onReorderItems,
  setDragOverIndex,
  toast
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setIsHovered(true);
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setIsHovered(false);
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsHovered(false);
    setDragOverIndex(null);

    // Check if it's a new component from sidebar
    const componentType = e.dataTransfer.getData("componentType");
    if (componentType) {
      onDrop(index, componentType);
      return;
    }

    // Check if it's a component being reordered
    const componentId = e.dataTransfer.getData("componentId");
    if (componentId && onReorderItems) {
      // Handle reordering by finding the source component and moving it
      const sourceIndex = items.findIndex(item => item.id === componentId);
      if (sourceIndex !== -1) {
        // Calculate target index (before the current position)
        const targetIndex = index > sourceIndex ? index - 1 : index;
        if (sourceIndex !== targetIndex) {
          onReorderItems(sourceIndex, targetIndex);
          toast({
            title: "Component Reordered",
            description: `Component moved from position ${sourceIndex + 1} to ${targetIndex + 1}.`,
          });
        }
      }
      return;
    }
  };

  return (
    <div
      className={`min-h-[8px] transition-all duration-200 ${
        isDragOver || isHovered
          ? "h-12 bg-blue-100 dark:bg-blue-900/30 border-2 border-dashed border-blue-400 rounded-md"
          : "h-2 bg-transparent hover:bg-slate-100 dark:hover:bg-slate-800 hover:h-4"
      }`}
      data-drop-zone="true"
      data-drop-index={index}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {(isDragOver || isHovered) && (
        <div className="flex items-center justify-center h-full">
          <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
            Drop component here to reorder
          </span>
        </div>
      )}
    </div>
  );
};

export default DocumentDroppableArea;
