import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus, FileText } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { v4 as uuidv4 } from "uuid";
import EditableComponentRenderer from "./EditableComponentRenderer";

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface DocumentDroppableAreaProps {
  items: DocumentComponent[];
  onAddItem: (item: DocumentComponent) => void;
  onRemoveItem: (id: string) => void;
  onUpdateItem: (id: string, content: any) => void;
  onReorderItems?: (sourceIndex: number, targetIndex: number) => void;
}

const DocumentDroppableArea: React.FC<DocumentDroppableAreaProps> = ({
  items,
  onAddItem,
  onRemoveItem,
  onUpdateItem,
  onReorderItems,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
    if (!isDragOver) setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    // Only set drag over to false if we're leaving the droppable area entirely
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;

    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setIsDragOver(false);
    }
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const componentType = e.dataTransfer.getData("componentType");
    if (!componentType) {
      console.log("No component type found in drag data");
      return;
    }

    console.log("Dropping component:", componentType);

    const newComponent: DocumentComponent = {
      id: uuidv4(),
      type: componentType,
      content: getDefaultContent(componentType),
      position: items.length
    };

    onAddItem(newComponent);

    toast({
      title: "Component Added",
      description: `${getComponentLabel(componentType)} has been added to your document.`,
    });
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'document-header':
        return { text: 'Document Title', level: 1 };
      case 'section-header':
        return { text: 'Section Header', level: 2 };
      case 'paragraph':
        return { text: 'Enter your paragraph content here...' };
      case 'bullet-list':
        return { items: ['List item 1', 'List item 2', 'List item 3'] };
      case 'numbered-list':
        return { items: ['First item', 'Second item', 'Third item'] };
      case 'quote':
        return { text: 'Enter your quote here...', author: '' };
      case 'separator':
        return { style: 'line' };
      case 'image':
        return { src: '', alt: '', caption: '', filename: '', size: '' };
      case 'file-attachment':
        return { filename: '', description: '', size: '', fileType: '', src: '' };
      case 'link':
        return { text: 'Link text', url: '', target: '_blank' };
      default:
        return { text: 'Default content' };
    }
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'file-attachment': 'File Attachment',
      'link': 'Link'
    };
    return labels[type] || type;
  };

  // Component rendering is now handled by EditableComponentRenderer

  return (
    <div className="flex-1 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 h-full overflow-y-auto flex flex-col">
      <div
        className={`flex-1 p-6 transition-all duration-300 ${
          isDragOver ? "bg-blue-50 dark:bg-blue-900/20 border-2 border-dashed border-blue-300" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDragEnter={handleDragEnter}
        onDrop={handleDrop}
      >
        {items.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center shadow-lg">
                <FileText className="h-12 w-12 text-blue-500 dark:text-blue-400" />
              </div>
              <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-slate-700 to-slate-900 dark:from-slate-200 dark:to-slate-400 bg-clip-text text-transparent">
                Start Building Your Document
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                Drag components from the sidebar to create your document structure
              </p>
              <div className="flex gap-3 items-center justify-center">
                <Button
                  variant="outline"
                  className="px-6 py-3 h-auto hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Browse Components
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4 max-w-4xl mx-auto">
            {items.map((component, index) => (
              <EditableComponentRenderer
                key={component.id}
                component={component}
                onUpdate={onUpdateItem}
                onRemove={onRemoveItem}
              />
            ))}

            {/* Add Component Button */}
            <Button
              variant="outline"
              className="w-full mt-8 border-dashed border-2 py-8 h-auto hover:border-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg"
            >
              <Plus className="h-6 w-6 mr-3 text-blue-500" />
              <span className="text-lg font-medium">Add Another Component</span>
            </Button>

            {/* Extra padding at bottom for better scrolling */}
            <div className="h-32"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentDroppableArea;
