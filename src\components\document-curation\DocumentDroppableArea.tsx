import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus, FileText } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { v4 as uuidv4 } from "uuid";

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface DocumentDroppableAreaProps {
  items: DocumentComponent[];
  onAddItem: (item: DocumentComponent) => void;
  onRemoveItem: (id: string) => void;
  onUpdateItem: (id: string, content: any) => void;
  onReorderItems?: (sourceIndex: number, targetIndex: number) => void;
}

const DocumentDroppableArea: React.FC<DocumentDroppableAreaProps> = ({
  items,
  onAddItem,
  onRemoveItem,
  onUpdateItem,
  onReorderItems,
}) => {
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!isDragOver) setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    const componentType = e.dataTransfer.getData("componentType");
    if (!componentType) return;

    const newComponent: DocumentComponent = {
      id: uuidv4(),
      type: componentType,
      content: getDefaultContent(componentType),
      position: items.length
    };

    onAddItem(newComponent);
    
    toast({
      title: "Component Added",
      description: `${getComponentLabel(componentType)} has been added to your document.`,
    });
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'document-header':
        return { text: 'Document Title', level: 1 };
      case 'section-header':
        return { text: 'Section Header', level: 2 };
      case 'paragraph':
        return { text: 'Enter your paragraph content here...' };
      case 'bullet-list':
        return { items: ['List item 1', 'List item 2', 'List item 3'] };
      case 'numbered-list':
        return { items: ['First item', 'Second item', 'Third item'] };
      case 'quote':
        return { text: 'Enter your quote here...', author: '' };
      case 'separator':
        return { style: 'line' };
      case 'image':
        return { src: '', alt: '', caption: '' };
      case 'video':
        return { src: '', title: '', description: '' };
      case 'file-attachment':
        return { filename: '', description: '', size: '' };
      case 'table':
        return { 
          headers: ['Column 1', 'Column 2', 'Column 3'],
          rows: [
            ['Row 1 Col 1', 'Row 1 Col 2', 'Row 1 Col 3'],
            ['Row 2 Col 1', 'Row 2 Col 2', 'Row 2 Col 3']
          ]
        };
      case 'link':
        return { text: 'Link text', url: '', target: '_blank' };
      case 'download-button':
        return { text: 'Download', filename: '', description: '' };
      case 'text-input':
        return { label: 'Text Input', placeholder: 'Enter text...', required: false };
      case 'checkbox':
        return { label: 'Checkbox option', checked: false };
      case 'date-picker':
        return { label: 'Select Date', required: false };
      case 'signature':
        return { label: 'Digital Signature', required: true };
      case 'file-upload':
        return { label: 'Upload File', acceptedTypes: '', maxSize: '10MB' };
      default:
        return { text: 'Default content' };
    }
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'video': 'Video',
      'file-attachment': 'File Attachment',
      'table': 'Table',
      'link': 'Link',
      'download-button': 'Download Button',
      'text-input': 'Text Input',
      'checkbox': 'Checkbox',
      'date-picker': 'Date Picker',
      'signature': 'Signature',
      'file-upload': 'File Upload'
    };
    return labels[type] || type;
  };

  const renderComponent = (component: DocumentComponent) => {
    const { type, content } = component;

    switch (type) {
      case 'document-header':
        return (
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">{content.text}</h1>
          </div>
        );
      case 'section-header':
        return (
          <div className="mb-4">
            <h2 className="text-2xl font-semibold text-gray-800">{content.text}</h2>
          </div>
        );
      case 'paragraph':
        return (
          <div className="mb-4">
            <p className="text-gray-700 leading-relaxed">{content.text}</p>
          </div>
        );
      case 'bullet-list':
        return (
          <div className="mb-4">
            <ul className="list-disc list-inside space-y-1">
              {content.items.map((item: string, index: number) => (
                <li key={index} className="text-gray-700">{item}</li>
              ))}
            </ul>
          </div>
        );
      case 'numbered-list':
        return (
          <div className="mb-4">
            <ol className="list-decimal list-inside space-y-1">
              {content.items.map((item: string, index: number) => (
                <li key={index} className="text-gray-700">{item}</li>
              ))}
            </ol>
          </div>
        );
      case 'quote':
        return (
          <div className="mb-4">
            <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600">
              "{content.text}"
              {content.author && <cite className="block mt-2 text-sm">— {content.author}</cite>}
            </blockquote>
          </div>
        );
      case 'separator':
        return (
          <div className="mb-4">
            <hr className="border-gray-300" />
          </div>
        );
      case 'table':
        return (
          <div className="mb-4 overflow-x-auto">
            <table className="min-w-full border border-gray-300">
              <thead>
                <tr className="bg-gray-50">
                  {content.headers.map((header: string, index: number) => (
                    <th key={index} className="border border-gray-300 px-4 py-2 text-left font-semibold">
                      {header}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {content.rows.map((row: string[], rowIndex: number) => (
                  <tr key={rowIndex}>
                    {row.map((cell: string, cellIndex: number) => (
                      <td key={cellIndex} className="border border-gray-300 px-4 py-2">
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      default:
        return (
          <div className="mb-4 p-4 bg-gray-100 border border-gray-300 rounded">
            <div className="flex items-center gap-2 text-gray-600">
              <FileText className="h-4 w-4" />
              <span className="text-sm font-medium">{getComponentLabel(type)}</span>
            </div>
            <p className="text-xs text-gray-500 mt-1">Component preview will be implemented</p>
          </div>
        );
    }
  };

  return (
    <div className="flex-1 bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 h-full overflow-y-auto">
      <div
        className={`min-h-full p-6 transition-all duration-300 ${
          isDragOver ? "bg-blue-50 dark:bg-blue-900/20" : ""
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {items.length === 0 ? (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center shadow-lg">
                <FileText className="h-12 w-12 text-blue-500 dark:text-blue-400" />
              </div>
              <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-slate-700 to-slate-900 dark:from-slate-200 dark:to-slate-400 bg-clip-text text-transparent">
                Start Building Your Document
              </h3>
              <p className="text-slate-600 dark:text-slate-400 mb-4">
                Drag components from the sidebar to create your document structure
              </p>
              <div className="flex gap-3 items-center justify-center">
                <Button
                  variant="outline"
                  className="px-6 py-3 h-auto hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-200"
                >
                  <Plus className="h-5 w-5 mr-2" />
                  Browse Components
                </Button>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4 max-w-4xl mx-auto">
            {items.map((component, index) => (
              <div
                key={component.id}
                className="relative group bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4 hover:shadow-md transition-all duration-200"
              >
                {/* Component Actions */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <div className="flex gap-1">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onRemoveItem(component.id)}
                      className="h-6 w-6 p-0 hover:bg-red-50 hover:border-red-200"
                    >
                      ×
                    </Button>
                  </div>
                </div>

                {/* Component Content */}
                {renderComponent(component)}
              </div>
            ))}

            {/* Add Component Button */}
            <Button
              variant="outline"
              className="w-full mt-8 border-dashed border-2 py-8 h-auto hover:border-blue-400 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 transform hover:scale-[1.02] shadow-md hover:shadow-lg"
            >
              <Plus className="h-6 w-6 mr-3 text-blue-500" />
              <span className="text-lg font-medium">Add Another Component</span>
            </Button>

            {/* Extra padding at bottom for better scrolling */}
            <div className="h-32"></div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentDroppableArea;
