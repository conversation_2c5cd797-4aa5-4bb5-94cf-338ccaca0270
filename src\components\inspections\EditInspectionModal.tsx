import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useToast } from '@/components/ui/use-toast';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import AllFilterLocation from '@/components/observations/AllFilterLocation';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import { InspectionResponse } from '@/services/api';

// API endpoints
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;


// Extended interface for inspection data with all possible fields
interface ExtendedInspection {
  id: string;
  title?: string;
  name?: string;
  maskId?: string;
  scheduledDate?: string;
  dueDate?: string;
  status?: string;
  inspectionCategory?: string;
  category?: string;
  checklistVersion?: string;
  inspectorId?: string;
  assignedById?: string;
  checklistId?: string;
  locationOneId?: string;
  locationTwoId?: string;
  locationThreeId?: string;
  locationFourId?: string;
  locationFiveId?: string;
  locationSixId?: string;
  checklist?: {
    id: string;
    name?: string;
    category?: string;
  };
  inspector?: {
    id: string;
    firstName?: string;
  };
  locationOne?: {
    id: string;
    name?: string;
  };
  locationTwo?: {
    id: string;
    name?: string;
  };
  locationThree?: {
    id: string;
    name?: string;
  };
  locationFour?: {
    id: string;
    name?: string;
  };
  locationFive?: {
    id: string;
    name?: string;
  };
  locationSix?: {
    id: string;
    name?: string;
  };
}

interface EditInspectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  accessToken: string;
  inspection: ExtendedInspection | null;
}

interface OptionType {
  label: string;
  value: string;
  version?: string;
  category?: string;
}

interface EditInspection {
  name: string;
  scheduledDate: Date | null;
  inspectionCategory: string;
  maskId: string;
  dueDate: Date | null;
  status: string;
  checklistVersion: string;
  inspectorId: string;
  assignedById: string;
  checklistId: string;
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
}

const EditInspectionModal: React.FC<EditInspectionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  accessToken,
  inspection
}) => {
  const { toast } = useToast();
  const { user } = useSelector((state: RootState) => state.auth);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [locationComponentKey, setLocationComponentKey] = useState(0);

  // Form state
  const [editInspection, setEditInspection] = useState<EditInspection>({
    name: '',
    scheduledDate: null,
    inspectionCategory: '',
    maskId: '',
    dueDate: null,
    status: '',
    checklistVersion: '',
    inspectorId: '',
    assignedById: '',
    checklistId: '',
    locationOneId: '',
    locationTwoId: '',
    locationThreeId: '',
    locationFourId: '',
    locationFiveId: '',
    locationSixId: '',
  });

  // Dropdown options
  const [inspectionCategories, setInspectionCategories] = useState<OptionType[]>([]);
  const [checklistOptions, setChecklistOptions] = useState<OptionType[]>([]);
  const [filteredChecklistOptions, setFilteredChecklistOptions] = useState<OptionType[]>([]);
  const [inspectorOptions, setInspectorOptions] = useState<OptionType[]>([]);

  // Populate form when inspection data is available and dropdowns are loaded
  useEffect(() => {
    if (inspection && isOpen && inspectionCategories.length > 0) {
      console.log('Populating form with inspection data:', inspection);
      console.log('Full inspection object keys:', Object.keys(inspection));
      console.log('Inspection category field:', inspection.inspectionCategory);
      console.log('Checklist object:', inspection.checklist);
      console.log('Available categories:', inspectionCategories);

      // Try to extract category from different possible sources
      let categoryValue = '';

      // Check direct field
      if (inspection.inspectionCategory) {
        categoryValue = inspection.inspectionCategory;
        console.log('Found category in direct field:', categoryValue);
      }
      // Check if it's in checklist object
      else if (inspection.checklist && inspection.checklist.category) {
        categoryValue = inspection.checklist.category;
        console.log('Found category in checklist.category:', categoryValue);
      }
      // Check if it's in a nested inspectionCategory object
      else if (inspection.category) {
        categoryValue = inspection.category;
        console.log('Found category in category field:', categoryValue);
      }
      // If no category found, try to derive from checklist name or other fields
      else {
        console.log('No category found in inspection object');
        console.log('Checklist object structure:', inspection.checklist);

        // Try to derive category from checklist name if it follows a pattern
        if (inspection.checklist && inspection.checklist.name) {
          const checklistName = inspection.checklist.name.toLowerCase();
          if (checklistName.includes('equipment') || checklistName.includes('machine') || checklistName.includes('tool')) {
            categoryValue = 'Equipment';
          } else if (checklistName.includes('safety') || checklistName.includes('hazard') || checklistName.includes('risk')) {
            categoryValue = 'Safety';
          } else if (checklistName.includes('quality') || checklistName.includes('standard') || checklistName.includes('compliance')) {
            categoryValue = 'Quality';
          }

          if (categoryValue) {
            console.log('Derived category from checklist name:', categoryValue);
          } else {
            console.log('Could not derive category from checklist name:', inspection.checklist.name);
            // Default to first available category if we can't derive it
            if (inspectionCategories.length > 0) {
              categoryValue = inspectionCategories[0].value;
              console.log('Using default category:', categoryValue);
            }
          }
        } else if (inspectionCategories.length > 0) {
          // If no checklist name available, use first available category
          categoryValue = inspectionCategories[0].value;
          console.log('No checklist name available, using default category:', categoryValue);
        }
      }

      console.log('Final category value:', categoryValue);

      // Extract location IDs from inspection object
      const locationData = {
        locationOneId: inspection.locationOneId || inspection.locationOne?.id || '',
        locationTwoId: inspection.locationTwoId || inspection.locationTwo?.id || '',
        locationThreeId: inspection.locationThreeId || inspection.locationThree?.id || '',
        locationFourId: inspection.locationFourId || inspection.locationFour?.id || '',
        locationFiveId: inspection.locationFiveId || inspection.locationFive?.id || '',
        locationSixId: inspection.locationSixId || inspection.locationSix?.id || '',
      };

      console.log('Location data extracted:', locationData);
      console.log('Location objects:', {
        locationOne: inspection.locationOne,
        locationTwo: inspection.locationTwo,
        locationThree: inspection.locationThree,
        locationFour: inspection.locationFour,
        locationFive: inspection.locationFive,
        locationSix: inspection.locationSix,
      });

      const formData = {
        name: inspection.title || inspection.name || '',
        scheduledDate: inspection.scheduledDate ? new Date(inspection.scheduledDate) : null,
        inspectionCategory: categoryValue,
        maskId: inspection.maskId || '',
        dueDate: inspection.dueDate ? new Date(inspection.dueDate) : null,
        status: inspection.status || '',
        checklistVersion: inspection.checklistVersion || '',
        inspectorId: inspection.inspector?.id || inspection.inspectorId || '',
        assignedById: inspection.assignedById || '',
        checklistId: inspection.checklist?.id || inspection.checklistId || '',
        ...locationData
      };

      console.log('Setting form data:', formData);

      // Set the form data immediately
      setEditInspection(formData);
      // Force AllFilterLocation component to re-mount with new data
      setLocationComponentKey(prev => prev + 1);
      console.log('Form data set for AllFilterLocation component');
    }
  }, [inspection, isOpen, inspectionCategories]);

  // Populate filtered checklists when inspection data and checklist options are available
  useEffect(() => {
    if (inspection && checklistOptions.length > 0) {
      const inspectionCategory = inspection.inspectionCategory || '';
      if (inspectionCategory) {
        const filtered = checklistOptions.filter(opt =>
          opt.category.toLowerCase().includes(inspectionCategory.toLowerCase())
        );
        setFilteredChecklistOptions(filtered);
      }
    }
  }, [inspection, checklistOptions]);

  // Fetch inspection categories from dropdown API
  const fetchDropdownData = useCallback(async (maskId: string, setState: React.Dispatch<React.SetStateAction<OptionType[]>>) => {
    try {
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const data = response[0]?.dropdownItems.map((item: any) => ({
        label: item.name,
        value: item.name,
      })) || [];
      setState(data);
    } catch (error) {
      console.error(`Error fetching ${maskId} list:`, error);
    }
  }, [accessToken]);

  // Fetch checklist options
  const fetchChecklistOptions = useCallback(async () => {
    try {
      const uriString = {
        where: { status: 'Published' },
      };
      const response = await apiService.get(`/checklists?filter=${encodeURIComponent(JSON.stringify(uriString))}`);
      const options = response.map((item: any) => ({
        label: `${item.name} (v${item.version})`,
        value: item.id,
        version: item.version,
        category: item.category || '',
      }));
      setChecklistOptions(options);
    } catch (error) {
      console.error('Error fetching checklist options:', error);
    }
  }, [accessToken]);

  // Fetch inspector options
  const fetchInspectorOptions = useCallback(async () => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: "",
        locationTwoId: "",
        locationThreeId: "",
        locationFourId: "",
        mode: 'ins_inspector'
      });

      const options = response.map((user: any) => ({
        label: `${user.firstName}`,
        value: user.id,
      }));
      setInspectorOptions(options);
    } catch (error) {
      console.error('Error fetching inspector options:', error);
    }
  }, [accessToken]);

  // Handle location filter
  const handleFilter = (
    locationOneId: string,
    locationTwoId?: string,
    locationThreeId?: string,
    locationFourId?: string,
    locationFiveId?: string,
    locationSixId?: string
  ) => {
    setEditInspection((prev) => ({
      ...prev,
      locationOneId,
      locationTwoId: locationTwoId || '',
      locationThreeId: locationThreeId || '',
      locationFourId: locationFourId || '',
      locationFiveId: locationFiveId || '',
      locationSixId: locationSixId || '',
    }));
  };

  // Load data when modal opens
  useEffect(() => {
    if (isOpen && accessToken) {
      setIsLoading(true);
      Promise.all([
        fetchDropdownData('ins_category', setInspectionCategories),
        fetchChecklistOptions(),
        fetchInspectorOptions(),
      ]).finally(() => {
        setIsLoading(false);
      });
    }
  }, [isOpen, accessToken, fetchDropdownData, fetchChecklistOptions, fetchInspectorOptions]);

  // Filter checklists based on selected category
  useEffect(() => {
    if (editInspection.inspectionCategory) {
      console.log('Filtering checklists for category:', editInspection.inspectionCategory);
      console.log('Available checklist options:', checklistOptions);
      const filtered = checklistOptions.filter(opt =>
        opt.category.toLowerCase().includes(editInspection.inspectionCategory.toLowerCase())
      );
      console.log('Filtered checklists:', filtered);
      setFilteredChecklistOptions(filtered);
    } else {
      setFilteredChecklistOptions(checklistOptions);
    }
  }, [editInspection.inspectionCategory, checklistOptions]);

  // Debug effect to check if inspection category is in available options
  useEffect(() => {
    if (inspection && inspectionCategories.length > 0) {
      const categoryExists = inspectionCategories.some(cat =>
        cat.value === inspection.inspectionCategory
      );
      console.log('Category exists in dropdown:', categoryExists);
      console.log('Looking for category:', inspection.inspectionCategory);
      console.log('Available categories:', inspectionCategories.map(cat => cat.value));

      if (!categoryExists && inspection.inspectionCategory) {
        console.warn('Inspection category not found in dropdown options!');
      }
    }
  }, [inspection, inspectionCategories]);

  // Effect to ensure location data is properly set for AllFilterLocation component
  useEffect(() => {
    if (editInspection.locationOneId && inspection) {
      console.log('Location data for AllFilterLocation:', {
        locationOneId: editInspection.locationOneId,
        locationTwoId: editInspection.locationTwoId,
        locationThreeId: editInspection.locationThreeId,
        locationFourId: editInspection.locationFourId,
        locationFiveId: editInspection.locationFiveId,
        locationSixId: editInspection.locationSixId,
      });

      // Check if all required location IDs are present
      const hasCompleteLocationData = editInspection.locationOneId &&
        (editInspection.locationTwoId || !inspection.locationTwo) &&
        (editInspection.locationThreeId || !inspection.locationThree) &&
        (editInspection.locationFourId || !inspection.locationFour) &&
        (editInspection.locationFiveId || !inspection.locationFive) &&
        (editInspection.locationSixId || !inspection.locationSix);

      console.log('Has complete location data:', hasCompleteLocationData);
      console.log('Location hierarchy depth:', {
        hasLocationTwo: !!inspection.locationTwo,
        hasLocationThree: !!inspection.locationThree,
        hasLocationFour: !!inspection.locationFour,
        hasLocationFive: !!inspection.locationFive,
        hasLocationSix: !!inspection.locationSix,
      });
    }
  }, [editInspection.locationOneId, editInspection.locationTwoId, editInspection.locationThreeId,
      editInspection.locationFourId, editInspection.locationFiveId, editInspection.locationSixId, inspection]);

  // Validation function
  const validateForm = () => {
    const errors = [];

    if (!editInspection.locationOneId) {
      errors.push('Location is required');
    }
    if (!editInspection.scheduledDate) {
      errors.push('Scheduled Date is required');
    }
    if (!editInspection.dueDate) {
      errors.push('Due Date is required');
    }
    if (!editInspection.inspectionCategory) {
      errors.push('Inspection Category is required');
    }
    if (!editInspection.checklistId) {
      errors.push('Checklist is required');
    }
    if (!editInspection.inspectorId) {
      errors.push('Inspector is required');
    }

    return errors;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!inspection) return;

    // Validate form
    const validationErrors = validateForm();

    if (validationErrors.length > 0) {
      setShowValidationErrors(true);
      toast({
        title: "Validation Error",
        description: validationErrors.join(', '),
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const inspectionData = {
        ...editInspection,
        scheduledDate: editInspection.scheduledDate?.toISOString(),
        dueDate: editInspection.dueDate?.toISOString(),
        title: editInspection.name,
      };

      await apiService.patch(`/inspections/${inspection.id}`, inspectionData);

      onSuccess();
      setShowValidationErrors(false);
      toast({
        title: "Success",
        description: "Inspection updated successfully",
      });
    } catch (error) {
      console.error('Error updating inspection:', error);
      toast({
        title: "Error",
        description: "Failed to update inspection. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset validation errors when modal closes
  const handleClose = () => {
    setShowValidationErrors(false);
    onClose();
  };

  if (!inspection) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Inspection - {inspection.maskId}</DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Location Selection */}
            <div className="space-y-2">
              <Card className={showValidationErrors && !editInspection.locationOneId ? "border-red-500" : ""}>
                <CardContent className="pt-6">
                  <Label className={showValidationErrors && !editInspection.locationOneId ? "text-red-500" : ""}>
                    Location *
                  </Label>
                  <AllFilterLocation
                    key={`edit-location-${inspection?.id}-${locationComponentKey}`}
                    handleFilter={handleFilter}
                    getLocation={editInspection}
                  />
                  {showValidationErrors && !editInspection.locationOneId && (
                    <p className="text-red-500 text-sm mt-2">Location is required</p>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Scheduled Date */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !editInspection.scheduledDate ? "text-red-500" : ""}>
                Scheduled Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editInspection.scheduledDate && "text-muted-foreground",
                      showValidationErrors && !editInspection.scheduledDate && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {editInspection.scheduledDate ? format(editInspection.scheduledDate, "PPP") : "Select scheduled date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={editInspection.scheduledDate || undefined}
                    onSelect={(date) => setEditInspection(prev => ({ ...prev, scheduledDate: date || null }))}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {showValidationErrors && !editInspection.scheduledDate && (
                <p className="text-red-500 text-sm">Scheduled Date is required</p>
              )}
            </div>

            {/* Due Date */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !editInspection.dueDate ? "text-red-500" : ""}>
                Due Date *
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !editInspection.dueDate && "text-muted-foreground",
                      showValidationErrors && !editInspection.dueDate && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {editInspection.dueDate ? format(editInspection.dueDate, "PPP") : "Select due date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={editInspection.dueDate || undefined}
                    onSelect={(date) => setEditInspection(prev => ({ ...prev, dueDate: date || null }))}
                    disabled={(date) => date < (editInspection.scheduledDate || new Date())}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {showValidationErrors && !editInspection.dueDate && (
                <p className="text-red-500 text-sm">Due Date is required</p>
              )}
            </div>

            {/* Inspection Category */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !editInspection.inspectionCategory ? "text-red-500" : ""}>
                Inspection Category *
              </Label>
              <Select
                value={editInspection.inspectionCategory}
                onValueChange={(value) => {
                  console.log('Inspection category changed to:', value);
                  setEditInspection(prev => ({
                    ...prev,
                    inspectionCategory: value,
                    checklistId: '',
                    checklistVersion: '',
                  }));
                }}
              >
                <SelectTrigger className={showValidationErrors && !editInspection.inspectionCategory ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select Category" />
                </SelectTrigger>
                <SelectContent>
                  {inspectionCategories.map((category) => (
                    <SelectItem key={category.value} value={category.value}>
                      {category.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && !editInspection.inspectionCategory && (
                <p className="text-red-500 text-sm">Inspection Category is required</p>
              )}
            </div>

            {/* Checklist */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !editInspection.checklistId ? "text-red-500" : ""}>
                Checklist *
              </Label>
              <Select
                value={editInspection.checklistId}
                onValueChange={(value) => {
                  console.log('Checklist changed to:', value);
                  const selectedChecklist = filteredChecklistOptions.find(opt => opt.value === value);
                  console.log('Selected checklist:', selectedChecklist);
                  setEditInspection(prev => ({
                    ...prev,
                    checklistId: value,
                    checklistVersion: selectedChecklist?.version || '',
                  }));
                }}
                disabled={!editInspection.inspectionCategory}
              >
                <SelectTrigger className={showValidationErrors && !editInspection.checklistId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select Checklist" />
                </SelectTrigger>
                <SelectContent>
                  {filteredChecklistOptions.map((checklist) => (
                    <SelectItem key={checklist.value} value={checklist.value}>
                      {checklist.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && !editInspection.checklistId && (
                <p className="text-red-500 text-sm">Checklist is required</p>
              )}
            </div>

            {/* Inspector */}
            <div className="space-y-2">
              <Label className={showValidationErrors && !editInspection.inspectorId ? "text-red-500" : ""}>
                Inspector *
              </Label>
              <Select
                value={editInspection.inspectorId}
                onValueChange={(value) => {
                  console.log('Inspector changed to:', value);
                  setEditInspection(prev => ({
                    ...prev,
                    inspectorId: value,
                  }));
                }}
              >
                <SelectTrigger className={showValidationErrors && !editInspection.inspectorId ? "border-red-500" : ""}>
                  <SelectValue placeholder="Select Inspector" />
                </SelectTrigger>
                <SelectContent>
                  {inspectorOptions.map((inspector) => (
                    <SelectItem key={inspector.value} value={inspector.value}>
                      {inspector.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && !editInspection.inspectorId && (
                <p className="text-red-500 text-sm">Inspector is required</p>
              )}
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading || isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Inspection
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditInspectionModal;
