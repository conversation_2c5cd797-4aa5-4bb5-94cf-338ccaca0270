import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Eye } from 'lucide-react';
import ImageComponent from '@/components/common/ImageComponent';

interface UpdateData {
  reasonForReview: string;
  changes: string;
  reasonForChanges: string;
  initiatedBy: string;
  approvedBy: string;
  reference: string;
  attachment: string[];
}

interface UpdateTableProps {
  data: UpdateData[];
}

const UpdateTable: React.FC<UpdateTableProps> = ({ data }) => {
  const [showModal, setShowModal] = useState<boolean>(false);
  const [viewAttachment, setViewAttachment] = useState<string[]>([]);

  const viewRisk = (rowData: UpdateData) => {
    setShowModal(true);
    setViewAttachment(rowData.attachment);
  };

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <CardTitle>Reviews, Changes and Updates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Reason for Review</TableHead>
                  <TableHead>Changes</TableHead>
                  <TableHead>Reason for Changes</TableHead>
                  <TableHead>Initiated By</TableHead>
                  <TableHead>Approved By</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center text-muted-foreground">
                      No updates available
                    </TableCell>
                  </TableRow>
                ) : (
                  data.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell className="max-w-[200px] truncate" title={item.reasonForReview}>
                        {item.reasonForReview}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate" title={item.changes}>
                        {item.changes}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate" title={item.reasonForChanges}>
                        {item.reasonForChanges}
                      </TableCell>
                      <TableCell>{item.initiatedBy}</TableCell>
                      <TableCell>{item.approvedBy}</TableCell>
                      <TableCell>{item.reference}</TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewRisk(item)}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Dialog open={showModal} onOpenChange={setShowModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Attachments</DialogTitle>
          </DialogHeader>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {viewAttachment.length === 0 ? (
              <div className="col-span-full text-center text-muted-foreground py-8">
                No attachments available
              </div>
            ) : (
              viewAttachment.map((attachment, index) => (
                <div key={index} className="border rounded-lg p-2">
                  <ImageComponent fileName={attachment} size={'200'} name={true} />
                </div>
              ))
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowModal(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UpdateTable;
