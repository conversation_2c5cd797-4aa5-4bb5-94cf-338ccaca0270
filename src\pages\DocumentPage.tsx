import { useState, useEffect, useCallback } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import TabsContainer from '@/components/common/TabsContainer';
import ExpandableDataTable from '@/components/common/ExpandableDataTable';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Plus,
  Search,
  Download,
  Upload,
  File,
  Image,
  Video,
  Loader2,
  Calendar as CalendarIcon
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { format, differenceInDays } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import { ASSIGNED_ACTION_URL } from '@/services/api';

// API Configuration
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const DOCUMENTS_API = `${API_BASE_URL}/documents`;

// Function to calculate timeline status
const getTimelineStatus = (dueDate: string | undefined): string => {
  if (!dueDate) return 'No Due Date';

  try {
    const due = new Date(dueDate);
    const today = new Date();

    // Reset time to compare only dates
    today.setHours(0, 0, 0, 0);
    due.setHours(0, 0, 0, 0);

    const diffInDays = differenceInDays(due, today);

    if (diffInDays < 0) {
      return 'Overdue';
    } else if (diffInDays === 0) {
      return 'Due Now';
    } else {
      return 'Upcoming';
    }
  } catch (error) {
    console.error('Error calculating timeline status:', error);
    return 'Invalid Date';
  }
};

interface MyAction {
  id: string;
  maskId: string;
  actionType: string;
  description: string;
  submittedBy: string;
  dueDate: string;
  status: string;
  timeline?: string;
  applicationId?: string;
}

interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'doc' | 'image' | 'video' | 'other';
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
}

interface Assignment {
  id: string;
  maskId: string;
  title: string;
  assignedTo: string;
  assignedBy: string;
  assignedDate: string;
  dueDate: string;
  status: string;
  priority: string;
}

interface DocumentInitiation {
  type: string;
  documentCategoryId: string;
  docId: string;
  name: string;
  scopeApplicability: string;
  purpose: string;
  creatorId: string;
  creatorTargetDate: Date | undefined;
  reviewerId: string;
  reviewerTargetDate: Date | undefined;
  approverId: string;
  approverTargetDate: Date | undefined;
  keywords: string;
}

interface ExistingDocumentUpload {
  type: string;
  documentCategoryId: string;
  docId: string;
  name: string;
  scopeApplicability: string;
  purpose: string;
  keywords: string;
  file: File | null;
}

interface UserOption {
  label: string;
  value: string;
}

const DocumentPage = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('myaction');

  // Search states for each tab
  const [myActionsSearch, setMyActionsSearch] = useState('');
  const [documentsSearch, setDocumentsSearch] = useState('');
  const [assignmentsSearch, setAssignmentsSearch] = useState('');

  // Data states
  const [myActions, setMyActions] = useState<MyAction[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [assignments, setAssignments] = useState<Assignment[]>([]);

  // Loading states
  const [loadingActions, setLoadingActions] = useState(false);
  const [loadingDocuments, setLoadingDocuments] = useState(false);
  const [loadingAssignments, setLoadingAssignments] = useState(false);

  // Counts
  const [actionCount, setActionCount] = useState(0);
  const [documentCount, setDocumentCount] = useState(0);
  const [assignmentCount, setAssignmentCount] = useState(0);

  // Document Initiation Modal state
  const [isInitiateModalOpen, setIsInitiateModalOpen] = useState(false);
  const [documentForm, setDocumentForm] = useState<DocumentInitiation>({
    type: 'New',
    documentCategoryId: '',
    docId: '',
    name: '',
    scopeApplicability: '',
    purpose: '',
    creatorId: '',
    creatorTargetDate: undefined,
    reviewerId: '',
    reviewerTargetDate: undefined,
    approverId: '',
    approverTargetDate: undefined,
    keywords: ''
  });

  // Existing Document Upload Modal state
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);

  // Document Details Modal state
  const [isDocumentDetailsOpen, setIsDocumentDetailsOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);

  // Note: Action details are now handled by navigation to DocumentCurationPage
  const [uploadForm, setUploadForm] = useState<ExistingDocumentUpload>({
    type: 'Existing',
    documentCategoryId: '',
    docId: '',
    name: '',
    scopeApplicability: '',
    purpose: '',
    keywords: '',
    file: null
  });

  // User options for dropdowns
  const [creatorOptions, setCreatorOptions] = useState<UserOption[]>([]);
  const [reviewerOptions, setReviewerOptions] = useState<UserOption[]>([]);
  const [approverOptions, setApproverOptions] = useState<UserOption[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // Category options from API
  const [categoryOptions, setCategoryOptions] = useState<UserOption[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);

  // Fetch categories from API
  const fetchCategories = useCallback(async () => {
    try {
      setLoadingCategories(true);
      const maskId = 'doc_category';
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const data = response[0]?.dropdownItems.map((item: { name: string; id: string }) => ({
        label: item.name,
        value: item.id,
      })) || [];

      setCategoryOptions(data);
    } catch (error) {
      console.error('Error fetching categories:', error);
      toast({
        title: "Error",
        description: "Failed to load categories. Please try again.",
        variant: "destructive"
      });
      // Fallback to default categories
      setCategoryOptions([
        { label: 'Safety', value: 'safety' },
        { label: 'Training', value: 'training' },
        { label: 'Inspection', value: 'inspection' },
        { label: 'Plans', value: 'plans' },
        { label: 'Procedures', value: 'procedures' },
        { label: 'Policies', value: 'policies' },
        { label: 'Guidelines', value: 'guidelines' }
      ]);
    } finally {
      setLoadingCategories(false);
    }
  }, [toast]);

  // Fetch users by role mode
  const fetchUsersByMode = useCallback(async (mode: string) => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: '',
        locationTwoId: '',
        locationThreeId: '',
        locationFourId: '',
        mode: mode,
      });

      return response.map((item: any) => ({
        label: item.firstName,
        value: item.id
      }));
    } catch (error) {
      console.error(`Error fetching users for mode ${mode}:`, error);
      toast({
        title: "Error",
        description: `Failed to fetch users for ${mode}. Please try again.`,
        variant: "destructive"
      });
      return [];
    }
  }, [toast]);

  // Fetch all document-related users
  const fetchDocumentUsers = useCallback(async () => {
    try {
      setLoadingUsers(true);

      const [creators, reviewers, approvers] = await Promise.all([
        fetchUsersByMode('doc_creator'),
        fetchUsersByMode('doc_reviewer'),
        fetchUsersByMode('doc_approver')
      ]);

      setCreatorOptions(creators);
      setReviewerOptions(reviewers);
      setApproverOptions(approvers);
    } catch (error) {
      console.error('Error fetching document users:', error);
    } finally {
      setLoadingUsers(false);
    }
  }, [fetchUsersByMode]);

  // Fetch documents from API
  const fetchDocuments = useCallback(async () => {
    try {
      setLoadingDocuments(true);

      const uriString = {
        include: [
          { relation: "creator" },
          { relation: "documentCategory" },
          { relation: "reviewer" },
          { relation: "approver" },
          { relation: "initiator" }
        ]
      };

      const url = `${DOCUMENTS_API}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);

      // Transform API response to match our Document interface
      const transformedDocuments = response.map((doc: any) => ({
        id: doc.id,
        name: doc.name,
        type: getFileTypeFromName(doc.name),
        size: doc.size || 'Unknown',
        uploadedBy: doc.creator?.firstName || doc.uploadedBy || doc.createdBy || 'Not assigned',
        uploadedDate: doc.uploadedDate || doc.created || new Date().toISOString(),
        category: doc.documentCategory?.name || doc.category || 'Uncategorized',
        tags: doc.keywords ? doc.keywords.split(',').map((tag: string) => tag.trim()) : [],
        description: doc.purpose || doc.description || '',
        maskId: doc.maskId,
        scopeApplicability: doc.scopeApplicability,
        purpose: doc.purpose,
        keywords: doc.keywords,
        docId: doc.docId,
        created: doc.created,
        updated: doc.updated,
        creatorTargetDate: doc.creatorTargetDate,
        reviewerTargetDate: doc.reviewerTargetDate,
        approverTargetDate: doc.approverTargetDate,
        initiatorId: doc.initiatorId,
        creatorId: doc.creatorId,
        reviewerId: doc.reviewerId,
        approverId: doc.approverId,
        documentCategoryId: doc.documentCategoryId,
        initiator: doc.initiator,
        creator: doc.creator,
        reviewer: doc.reviewer,
        approver: doc.approver,
        documentCategory: doc.documentCategory
      }));

      setDocuments(transformedDocuments);
      setDocumentCount(transformedDocuments.length);
    } catch (error) {
      console.error('Error fetching documents:', error);
      toast({
        title: "Error",
        description: "Failed to load documents. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingDocuments(false);
    }
  }, [toast]);

  // Fetch MyActions from API
  const fetchMyActions = useCallback(async () => {
    try {
      setLoadingActions(true);

      const uriString = {
        include: [{ relation: "submittedBy" }]
      };
      const url = `${ASSIGNED_ACTION_URL('DOC')}?filter=${encodeURIComponent(
        JSON.stringify(uriString)
      )}`;

      const response = await apiService.get(url);

      // Transform API response to match our MyAction interface
      const transformedActions = response.map((action: any) => ({
        id: action.id,
        maskId: action.maskId,
        actionType: action.actionType,
        description: action.description || action.title || 'No description',
        submittedBy: action.submittedBy?.firstName || 'Not assigned',
        dueDate: action.dueDate || action.targetDate || '',
        status: action.status || 'Pending',
        timeline: action.timeline || action.createdAt || '',
        applicationId: action.applicationId
      }));

      setMyActions(transformedActions);
      setActionCount(transformedActions.length);
    } catch (error) {
      console.error('Error fetching my actions:', error);
      toast({
        title: "Error",
        description: "Failed to load actions. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingActions(false);
    }
  }, [toast]);

  // Helper function to determine file type from filename
  const getFileTypeFromName = (filename: string): 'pdf' | 'doc' | 'image' | 'video' | 'other' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return 'video';
      default:
        return 'other';
    }
  };

  // Load data on component mount
  useEffect(() => {
    // Fetch categories, users, documents, and actions on component mount
    fetchCategories();
    fetchDocumentUsers();
    fetchDocuments();
    fetchMyActions();

    // Mock Assignments Data (keeping for now)
    const mockAssignments: Assignment[] = [
      {
        id: '1',
        maskId: 'ASG-001',
        title: 'Monthly Safety Audit',
        assignedTo: 'John Smith',
        assignedBy: 'Manager',
        assignedDate: '2024-01-20',
        dueDate: '2024-02-20',
        status: 'Active',
        priority: 'High'
      },
      {
        id: '2',
        maskId: 'ASG-002',
        title: 'Equipment Maintenance Review',
        assignedTo: 'Sarah Johnson',
        assignedBy: 'Supervisor',
        assignedDate: '2024-01-18',
        dueDate: '2024-02-18',
        status: 'Pending',
        priority: 'Medium'
      }
    ];

    setAssignments(mockAssignments);
    setAssignmentCount(mockAssignments.length);
  }, [fetchCategories, fetchDocumentUsers, fetchDocuments, fetchMyActions]);

  // Filter functions
  const filteredMyActions = myActions.filter(action =>
    action.maskId.toLowerCase().includes(myActionsSearch.toLowerCase()) ||
    action.description.toLowerCase().includes(myActionsSearch.toLowerCase()) ||
    action.actionType.toLowerCase().includes(myActionsSearch.toLowerCase())
  );

  const filteredDocuments = documents.filter(doc =>
    doc.name.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.description?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.maskId?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.creator?.firstName?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.reviewer?.firstName?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.approver?.firstName?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.documentCategory?.name?.toLowerCase().includes(documentsSearch.toLowerCase()) ||
    doc.tags.some(tag => tag.toLowerCase().includes(documentsSearch.toLowerCase()))
  );

  const filteredAssignments = assignments.filter(assignment =>
    assignment.maskId.toLowerCase().includes(assignmentsSearch.toLowerCase()) ||
    assignment.title.toLowerCase().includes(assignmentsSearch.toLowerCase()) ||
    assignment.assignedTo.toLowerCase().includes(assignmentsSearch.toLowerCase())
  );

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-500" />;
      case 'doc':
        return <File className="h-8 w-8 text-blue-500" />;
      case 'image':
        return <Image className="h-8 w-8 text-green-500" />;
      case 'video':
        return <Video className="h-8 w-8 text-purple-500" />;
      default:
        return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  const handleActionClick = (action: MyAction) => {
    if (!action.applicationId) {
      toast({
        title: "Error",
        description: "No document associated with this action.",
        variant: "destructive"
      });
      return;
    }

    // Navigate to DocumentCurationPage with the document ID
    navigate(`/documents/${action.applicationId}/curate`);
  };

  const handleDocumentClick = (document: Document) => {
    setSelectedDocument(document);
    setIsDocumentDetailsOpen(true);
  };

  const handleAssignmentClick = (assignment: Assignment) => {
    toast({
      title: "Assignment Details",
      description: `Opening details for ${assignment.maskId}`
    });
  };

  // Handle upload existing document
  const handleUploadExisting = () => {
    setIsUploadModalOpen(true);
  };

  // Handle initiate document
  const handleInitiateDocument = () => {
    setIsInitiateModalOpen(true);
  };

  // Handle form field changes
  const handleFormChange = (field: keyof DocumentInitiation, value: string | Date | undefined) => {
    setDocumentForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle form submission
  const handleFormSubmit = async () => {
    // Basic validation
    if (!documentForm.documentCategoryId || !documentForm.name || !documentForm.purpose) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Category, Name, Purpose)",
        variant: "destructive"
      });
      return;
    }

    if (!documentForm.creatorId || !documentForm.reviewerId || !documentForm.approverId) {
      toast({
        title: "Validation Error",
        description: "Please select Creator, Reviewer, and Approver",
        variant: "destructive"
      });
      return;
    }

    try {
      // Prepare data for API submission with ISO string dates
      const submissionData = {
        ...documentForm,
        creatorTargetDate: documentForm.creatorTargetDate ? documentForm.creatorTargetDate.toISOString() : '',
        reviewerTargetDate: documentForm.reviewerTargetDate ? documentForm.reviewerTargetDate.toISOString() : '',
        approverTargetDate: documentForm.approverTargetDate ? documentForm.approverTargetDate.toISOString() : ''
      };

      // Send data to API
      await apiService.post(DOCUMENTS_API, submissionData);

      toast({
        title: "Success",
        description: "Document initiated successfully!"
      });

      // Refresh documents list
      fetchDocuments();

      // Reset form and close modal
      setDocumentForm({
        type: 'New',
        documentCategoryId: '',
        docId: '',
        name: '',
        scopeApplicability: '',
        purpose: '',
        creatorId: '',
        creatorTargetDate: undefined,
        reviewerId: '',
        reviewerTargetDate: undefined,
        approverId: '',
        approverTargetDate: undefined,
        keywords: ''
      });
      setIsInitiateModalOpen(false);
    } catch (error) {
      console.error('Error initiating document:', error);
      toast({
        title: "Error",
        description: "Failed to initiate document. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsInitiateModalOpen(false);
  };

  // Handle upload form field changes
  const handleUploadFormChange = (field: keyof ExistingDocumentUpload, value: string | File | null) => {
    setUploadForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    handleUploadFormChange('file', file);
  };

  // Handle upload form submission
  const handleUploadSubmit = async () => {
    // Basic validation
    if (!uploadForm.documentCategoryId || !uploadForm.name || !uploadForm.purpose || !uploadForm.file) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields and select a file",
        variant: "destructive"
      });
      return;
    }

    try {
      // Step 1: Upload file to /files endpoint
      const fileFormData = new FormData();
      fileFormData.append('file', uploadForm.file);

      const fileUploadResponse = await apiService.post(`${API_BASE_URL}/files`, fileFormData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      // Get the filename from the response (following the pattern used in other components)
      const uploadedFileName = fileUploadResponse.files?.[0]?.originalname ||
                              fileUploadResponse.data?.files?.[0]?.originalname;

      if (!uploadedFileName) {
        throw new Error('File upload failed - no filename returned');
      }

      // Step 2: Create document record with the uploaded filename
      const documentData = {
        type: uploadForm.type,
        documentCategoryId: uploadForm.documentCategoryId,
        docId: uploadForm.docId,
        name: uploadForm.name,
        scopeApplicability: uploadForm.scopeApplicability,
        purpose: uploadForm.purpose,
        keywords: uploadForm.keywords,
        files: uploadedFileName // Pass the filename as 'files' field
      };

      // Send document data to API
      await apiService.post(DOCUMENTS_API, documentData);

      toast({
        title: "Success",
        description: "Document uploaded successfully!"
      });

      // Refresh documents list
      fetchDocuments();

      // Reset form and close modal
      setUploadForm({
        type: 'Existing',
        documentCategoryId: '',
        docId: '',
        name: '',
        scopeApplicability: '',
        purpose: '',
        keywords: '',
        file: null
      });
      setIsUploadModalOpen(false);
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({
        title: "Error",
        description: "Failed to upload document. Please try again.",
        variant: "destructive"
      });
    }
  };

  // Handle upload modal close
  const handleUploadModalClose = () => {
    setIsUploadModalOpen(false);
  };

  // Create tabs with updated counts
  const tabs = [
    {
      value: "myaction",
      label: `My Action (${loadingActions ? '...' : actionCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search my actions..."
                value={myActionsSearch}
                onChange={(e) => setMyActionsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingActions ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading actions...
                </div>
              ) : (
                `Showing ${filteredMyActions.length} of ${actionCount} items`
              )}
            </div>
          </div>

          {loadingActions ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading action data...</p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredMyActions}
              columns={[
                {
                  key: 'timeline',
                  header: 'Timeline',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'Upcoming', value: 'Upcoming' },
                    { label: 'Due Now', value: 'Due Now' },
                    { label: 'Overdue', value: 'Overdue' },
                    { label: 'No Due Date', value: 'No Due Date' },
                  ],
                  render: (_, row) => {
                    const timelineStatus = getTimelineStatus(row.dueDate);
                    let badgeClass = '';
                    switch(timelineStatus) {
                      case 'Upcoming': badgeClass = 'bg-teal-600 hover:bg-teal-700 text-white border-teal-700'; break;
                      case 'Due Now': badgeClass = 'bg-amber-500 hover:bg-amber-600 text-black border-amber-600 font-semibold'; break;
                      case 'Overdue': badgeClass = 'bg-red-600 hover:bg-red-700 text-white border-red-700 font-semibold'; break;
                      case 'No Due Date': badgeClass = 'bg-neutral-500 hover:bg-neutral-600 text-white border-neutral-600'; break;
                      case 'Invalid Date': badgeClass = 'bg-rose-500 hover:bg-rose-600 text-white border-rose-600'; break;
                      default: badgeClass = 'bg-gray-400 hover:bg-gray-500 text-white border-gray-500';
                    }
                    return <Badge className={badgeClass}>{timelineStatus}</Badge>;
                  }
                },
                {
                  key: 'maskId',
                  header: 'ID',
                  sortable: true,
                  width: 'w-[120px]',
                  render: (value) => (
                    <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                      {value}
                    </span>
                  )
                },
                {
                  key: 'actionType',
                  header: 'Required Action',
                  width: 'w-[200px]',
                  filterable: true,
                  filterType: 'text'
                },
                {
                  key: 'dueDate',
                  header: 'Target Date',
                  width: 'w-[140px]',
                  sortable: true,
                  render: (value) => {
                    if (!value) return 'N/A';
                    try {
                      const date = new Date(value);
                      if (isNaN(date.getTime())) return 'N/A';
                      return date.toLocaleDateString();
                    } catch (error) {
                      return 'N/A';
                    }
                  }
                },
                {
                  key: 'status',
                  header: 'Status',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'In Progress', value: 'In Progress' },
                    { label: 'Pending', value: 'Pending' },
                    { label: 'Completed', value: 'Completed' },
                    { label: 'Overdue', value: 'Overdue' },
                  ],
                  render: (value) => {
                    let badgeClass = '';
                    switch(value) {
                      case 'In Progress': badgeClass = 'bg-blue-600 hover:bg-blue-700 text-white'; break;
                      case 'Pending': badgeClass = 'bg-yellow-600 hover:bg-yellow-700 text-white'; break;
                      case 'Completed': badgeClass = 'bg-green-600 hover:bg-green-700 text-white'; break;
                      case 'Overdue': badgeClass = 'bg-red-600 hover:bg-red-700 text-white'; break;
                      default: badgeClass = 'bg-gray-500 hover:bg-gray-600 text-white';
                    }
                    return <Badge className={badgeClass}>{value}</Badge>;
                  }
                }
              ]}
              onRowClick={(row) => handleActionClick(row)}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      )
    },
    {
      value: "document",
      label: `Document (${loadingDocuments ? '...' : documentCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search documents..."
                value={documentsSearch}
                onChange={(e) => setDocumentsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingDocuments ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading documents...
                </div>
              ) : (
                `Showing ${filteredDocuments.length} of ${documentCount} items`
              )}
            </div>
          </div>

          {loadingDocuments ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading document data...</p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredDocuments}
              columns={[
                {
                  key: 'maskId',
                  header: 'Document ID',
                  sortable: true,
                  width: 'w-[140px]',
                  render: (value, row) => (
                    <div>
                      <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                        {value || 'N/A'}
                      </span>
                      <p className="text-xs text-muted-foreground capitalize">{row.type}</p>
                    </div>
                  )
                },
                {
                  key: 'name',
                  header: 'Document Name',
                  sortable: true,
                  width: 'w-[250px]',
                  render: (value, row) => (
                    <div className="flex items-center gap-3">
                      {getFileIcon(row.type)}
                      <div>
                        <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                          {value}
                        </span>
                        <p className="text-xs text-muted-foreground">{row.documentCategory?.name || row.category}</p>
                      </div>
                    </div>
                  )
                },
                {
                  key: 'type',
                  header: 'Type',
                  width: 'w-[100px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => (
                    <div className="text-center">
                      <div className="inline-flex items-center gap-1 px-2 py-1 bg-gray-100 rounded-full">
                        {getFileIcon(row.type)}
                        <span className="text-xs font-medium text-gray-700 uppercase">
                          {row.type}
                        </span>
                      </div>
                    </div>
                  )
                },
                {
                  key: 'initiator',
                  header: 'Initiator',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => (
                    <div>
                      <p className="font-medium text-sm">{row.initiator?.firstName || 'Not assigned'}</p>
                      {row.initiator?.email && (
                        <p className="text-xs text-muted-foreground">{row.initiator.email}</p>
                      )}
                    </div>
                  )
                },
                {
                  key: 'creator',
                  header: 'Creator',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text',
                  render: (value, row) => {
                    const creatorName = row.creator?.firstName;
                    const uploadedBy = row.uploadedBy && row.uploadedBy !== 'Not assigned' ? row.uploadedBy : null;
                    const hasCreator = creatorName || uploadedBy;

                    if (!hasCreator) {
                      return (
                        <div>
                          <p className="font-medium text-sm">Not assigned</p>
                        </div>
                      );
                    }
                    return (
                      <div>
                        <p className="font-medium text-sm">{hasCreator}</p>
                        {row.creatorTargetDate && (
                          <div className="text-xs">
                            {(() => {
                              try {
                                const date = new Date(row.creatorTargetDate);
                                const today = new Date();
                                const isOverdue = date < today;
                                return (
                                  <span className={isOverdue ? 'text-red-600 font-medium' : 'text-muted-foreground'}>
                                    Due: {format(date, 'MMM dd, yyyy')}
                                  </span>
                                );
                              } catch (error) {
                                return <span className="text-muted-foreground">Invalid date</span>;
                              }
                            })()}
                          </div>
                        )}
                      </div>
                    );
                  }
                },
                {
                  key: 'reviewer',
                  header: 'Reviewer',
                  width: 'w-[140px]',
                  render: (value, row) => {
                    const hasReviewer = row.reviewer?.firstName;
                    if (!hasReviewer) {
                      return (
                        <div>
                          <p className="font-medium text-sm">Not assigned</p>
                        </div>
                      );
                    }
                    return (
                      <div>
                        <p className="font-medium text-sm">{hasReviewer}</p>
                        {row.reviewerTargetDate && (
                          <p className="text-xs text-amber-600">
                            Due: {format(new Date(row.reviewerTargetDate), 'MMM dd')}
                          </p>
                        )}
                      </div>
                    );
                  }
                },
                {
                  key: 'approver',
                  header: 'Approver',
                  width: 'w-[140px]',
                  render: (value, row) => {
                    const hasApprover = row.approver?.firstName;
                    if (!hasApprover) {
                      return (
                        <div>
                          <p className="font-medium text-sm">Not assigned</p>
                        </div>
                      );
                    }
                    return (
                      <div>
                        <p className="font-medium text-sm">{hasApprover}</p>
                        {row.approverTargetDate && (
                          <p className="text-xs text-amber-600">
                            Due: {format(new Date(row.approverTargetDate), 'MMM dd')}
                          </p>
                        )}
                      </div>
                    );
                  }
                },
                {
                  key: 'created',
                  header: 'Created',
                  width: 'w-[120px]',
                  sortable: true,
                  render: (value) => {
                    if (!value) return 'N/A';
                    try {
                      const date = new Date(value);
                      return (
                        <div>
                          <p className="text-sm font-medium">{format(date, 'MMM dd')}</p>
                          <p className="text-xs text-muted-foreground">{format(date, 'yyyy HH:mm')}</p>
                        </div>
                      );
                    } catch (error) {
                      return 'Invalid';
                    }
                  }
                }
              ]}
              onRowClick={(row) => handleDocumentClick(row)}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      )
    },
    {
      value: "assignment",
      label: `Assignment (${loadingAssignments ? '...' : assignmentCount})`,
      content: (
        <div className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search assignments..."
                value={assignmentsSearch}
                onChange={(e) => setAssignmentsSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="text-sm text-muted-foreground">
              {loadingAssignments ? (
                <div className="flex items-center">
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Loading assignments...
                </div>
              ) : (
                `Showing ${filteredAssignments.length} of ${assignmentCount} items`
              )}
            </div>
          </div>

          {loadingAssignments ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading assignment data...</p>
              </div>
            </div>
          ) : (
            <ExpandableDataTable
              data={filteredAssignments}
              columns={[
                {
                  key: 'maskId',
                  header: 'Assignment ID',
                  sortable: true,
                  width: 'w-[120px]',
                  render: (value) => (
                    <span className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer">
                      {value}
                    </span>
                  )
                },
                {
                  key: 'title',
                  header: 'Title',
                  width: 'w-[250px]',
                  filterable: true,
                  filterType: 'text'
                },
                {
                  key: 'assignedTo',
                  header: 'Assigned To',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text'
                },
                {
                  key: 'assignedBy',
                  header: 'Assigned By',
                  width: 'w-[140px]',
                  filterable: true,
                  filterType: 'text'
                },
                {
                  key: 'dueDate',
                  header: 'Due Date',
                  width: 'w-[120px]',
                  render: (value) => {
                    try {
                      const date = new Date(value);
                      return date.toLocaleDateString();
                    } catch (error) {
                      return 'N/A';
                    }
                  }
                },
                {
                  key: 'status',
                  header: 'Status',
                  width: 'w-[120px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'Active', value: 'Active' },
                    { label: 'Pending', value: 'Pending' },
                    { label: 'Completed', value: 'Completed' },
                  ],
                  render: (value) => {
                    let badgeClass = '';
                    switch(value) {
                      case 'Active': badgeClass = 'bg-blue-600 hover:bg-blue-700 text-white'; break;
                      case 'Pending': badgeClass = 'bg-yellow-600 hover:bg-yellow-700 text-white'; break;
                      case 'Completed': badgeClass = 'bg-green-600 hover:bg-green-700 text-white'; break;
                      default: badgeClass = 'bg-gray-500 hover:bg-gray-600 text-white';
                    }
                    return <Badge className={badgeClass}>{value}</Badge>;
                  }
                },
                {
                  key: 'priority',
                  header: 'Priority',
                  width: 'w-[100px]',
                  filterable: true,
                  filterType: 'select',
                  filterOptions: [
                    { label: 'High', value: 'High' },
                    { label: 'Medium', value: 'Medium' },
                    { label: 'Low', value: 'Low' },
                  ],
                  render: (value) => {
                    let badgeClass = '';
                    switch(value) {
                      case 'High': badgeClass = 'bg-red-600 hover:bg-red-700 text-white'; break;
                      case 'Medium': badgeClass = 'bg-orange-600 hover:bg-orange-700 text-white'; break;
                      case 'Low': badgeClass = 'bg-green-600 hover:bg-green-700 text-white'; break;
                      default: badgeClass = 'bg-gray-500 hover:bg-gray-600 text-white';
                    }
                    return <Badge className={badgeClass}>{value}</Badge>;
                  }
                }
              ]}
              onRowClick={(row) => handleAssignmentClick(row)}
              highlightOnHover={true}
              striped={true}
            />
          )}
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Documents"
          description="Manage and organize your documents, files, and digital assets"
        />
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleUploadExisting}
            className="bg-blue-50 hover:bg-blue-100 border-blue-300 text-blue-700"
          >
            <Upload className="h-4 w-4 mr-2" /> Upload Existing Document
          </Button>
          <Button
            onClick={handleInitiateDocument}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" /> Initiate Document
          </Button>
        </div>
      </div>

      <TabsContainer
        key={`tabs-${actionCount}-${documentCount}-${assignmentCount}`}
        tabs={tabs}
        defaultValue="myaction"
        onValueChange={(value) => setActiveTab(value)}
      />

      {/* Document Initiation Modal */}
      <Dialog open={isInitiateModalOpen} onOpenChange={setIsInitiateModalOpen}>
        <DialogContent className="max-w-5xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <FileText className="h-6 w-6 text-green-600" />
              </div>
              Initiate New Document
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Create a new document workflow with assigned roles and target dates
            </p>
          </DialogHeader>

          <div className="py-6 space-y-8">
            {/* Document Information Section */}
            <div className="bg-blue-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Information</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Category */}
                <div className="space-y-2">
                  <Label htmlFor="category" className="text-sm font-medium text-gray-700">
                    Document Category <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={documentForm.documentCategoryId}
                    onValueChange={(value) => handleFormChange('documentCategoryId', value)}
                    disabled={loadingCategories}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCategories ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading categories...
                          </div>
                        </SelectItem>
                      ) : categoryOptions.length > 0 ? (
                        categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-categories" disabled>
                          No categories available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Document ID */}
                <div className="space-y-2">
                  <Label htmlFor="docId" className="text-sm font-medium text-gray-700">
                    Document ID
                  </Label>
                  <Input
                    id="docId"
                    value={documentForm.docId}
                    onChange={(e) => handleFormChange('docId', e.target.value)}
                    placeholder="Enter document ID"
                    className="h-11"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                {/* Name */}
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                    Document Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    value={documentForm.name}
                    onChange={(e) => handleFormChange('name', e.target.value)}
                    placeholder="Enter document name"
                    className="h-11"
                  />
                </div>
              </div>

              {/* Scope & Applicability */}
              <div className="space-y-2">
                <Label htmlFor="scope" className="text-sm font-medium text-gray-700">
                  Scope & Applicability
                </Label>
                <Textarea
                  id="scope"
                  value={documentForm.scopeApplicability}
                  onChange={(e) => handleFormChange('scopeApplicability', e.target.value)}
                  placeholder="Describe the scope and applicability of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Purpose */}
              <div className="space-y-2">
                <Label htmlFor="purpose" className="text-sm font-medium text-gray-700">
                  Purpose <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="purpose"
                  value={documentForm.purpose}
                  onChange={(e) => handleFormChange('purpose', e.target.value)}
                  placeholder="Explain the purpose and objectives of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>
            </div>

            {/* Workflow Assignment Section */}
            <div className="bg-gray-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Workflow Assignment</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Creator */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-xs">1</span>
                    </div>
                    <Label className="text-sm font-medium text-gray-700">
                      Creator <span className="text-red-500">*</span>
                    </Label>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Assigned To</Label>
                      <Select
                        value={documentForm.creatorId}
                        onValueChange={(value) => handleFormChange('creatorId', value)}
                        disabled={loadingUsers}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder={loadingUsers ? "Loading..." : "Select creator"} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading...
                              </div>
                            </SelectItem>
                          ) : creatorOptions.length > 0 ? (
                            creatorOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-creators" disabled>
                              No creators available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Target Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full h-10 justify-start text-left font-normal ${!documentForm.creatorTargetDate && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {documentForm.creatorTargetDate ? format(documentForm.creatorTargetDate, "MMM dd") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={documentForm.creatorTargetDate}
                            onSelect={(date) => handleFormChange('creatorTargetDate', date)}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>

                {/* Reviewer */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold text-xs">2</span>
                    </div>
                    <Label className="text-sm font-medium text-gray-700">
                      Reviewer <span className="text-red-500">*</span>
                    </Label>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Assigned To</Label>
                      <Select
                        value={documentForm.reviewerId}
                        onValueChange={(value) => handleFormChange('reviewerId', value)}
                        disabled={loadingUsers}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder={loadingUsers ? "Loading..." : "Select reviewer"} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading...
                              </div>
                            </SelectItem>
                          ) : reviewerOptions.length > 0 ? (
                            reviewerOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-reviewers" disabled>
                              No reviewers available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Target Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full h-10 justify-start text-left font-normal ${!documentForm.reviewerTargetDate && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {documentForm.reviewerTargetDate ? format(documentForm.reviewerTargetDate, "MMM dd") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={documentForm.reviewerTargetDate}
                            onSelect={(date) => handleFormChange('reviewerTargetDate', date)}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>

                {/* Approver */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 font-semibold text-xs">3</span>
                    </div>
                    <Label className="text-sm font-medium text-gray-700">
                      Approver <span className="text-red-500">*</span>
                    </Label>
                  </div>
                  <div className="space-y-3">
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Assigned To</Label>
                      <Select
                        value={documentForm.approverId}
                        onValueChange={(value) => handleFormChange('approverId', value)}
                        disabled={loadingUsers}
                      >
                        <SelectTrigger className="h-10">
                          <SelectValue placeholder={loadingUsers ? "Loading..." : "Select approver"} />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              <div className="flex items-center">
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                                Loading...
                              </div>
                            </SelectItem>
                          ) : approverOptions.length > 0 ? (
                            approverOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-approvers" disabled>
                              No approvers available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-500">Target Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className={`w-full h-10 justify-start text-left font-normal ${!documentForm.approverTargetDate && "text-muted-foreground"}`}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {documentForm.approverTargetDate ? format(documentForm.approverTargetDate, "MMM dd") : "Select date"}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={documentForm.approverTargetDate}
                            onSelect={(date) => handleFormChange('approverTargetDate', date)}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Keywords Section */}
            <div className="bg-amber-50 rounded-lg p-6 space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-6 bg-amber-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>
              </div>

              <div className="space-y-2">
                <Label htmlFor="keywords" className="text-sm font-medium text-gray-700">
                  Preliminary Keywords/Tags
                  <span className="text-xs text-gray-500 font-normal ml-2">(Optional)</span>
                </Label>
                <Textarea
                  id="keywords"
                  value={documentForm.keywords}
                  onChange={(e) => handleFormChange('keywords', e.target.value)}
                  placeholder="Add basic keywords separated by commas for easy search and retrieval. These can be expanded during review and approval phases."
                  rows={3}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">
                  Example: safety, procedures, training, compliance, guidelines
                </p>
              </div>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="flex justify-between items-center pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Required fields</span> are marked with <span className="text-red-500">*</span>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleModalClose}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleFormSubmit}
                className="bg-green-600 hover:bg-green-700 text-white px-6"
              >
                <FileText className="h-4 w-4 mr-2" />
                Initiate Document
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Existing Document Upload Modal */}
      <Dialog open={isUploadModalOpen} onOpenChange={setIsUploadModalOpen}>
        <DialogContent className="max-w-4xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Upload className="h-6 w-6 text-blue-600" />
              </div>
              Upload Existing Document
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Upload and categorize an existing document with metadata
            </p>
          </DialogHeader>

          <div className="py-6 space-y-8">
            {/* Document Upload Section */}
            <div className="bg-blue-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Upload</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Category */}
                <div className="space-y-2">
                  <Label htmlFor="upload-category" className="text-sm font-medium text-gray-700">
                    Document Category <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={uploadForm.documentCategoryId}
                    onValueChange={(value) => handleUploadFormChange('documentCategoryId', value)}
                    disabled={loadingCategories}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder={loadingCategories ? "Loading categories..." : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      {loadingCategories ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center">
                            <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            Loading categories...
                          </div>
                        </SelectItem>
                      ) : categoryOptions.length > 0 ? (
                        categoryOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))
                      ) : (
                        <SelectItem value="no-categories" disabled>
                          No categories available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Document ID */}
                <div className="space-y-2">
                  <Label htmlFor="upload-docId" className="text-sm font-medium text-gray-700">
                    Document ID
                  </Label>
                  <Input
                    id="upload-docId"
                    value={uploadForm.docId}
                    onChange={(e) => handleUploadFormChange('docId', e.target.value)}
                    placeholder="Enter document ID"
                    className="h-11"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 gap-6">
                {/* Document Name */}
                <div className="space-y-2">
                  <Label htmlFor="upload-name" className="text-sm font-medium text-gray-700">
                    Document Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="upload-name"
                    value={uploadForm.name}
                    onChange={(e) => handleUploadFormChange('name', e.target.value)}
                    placeholder="Enter document name"
                    className="h-11"
                  />
                </div>
              </div>

              {/* File Upload */}
              <div className="space-y-2">
                <Label htmlFor="file-upload" className="text-sm font-medium text-gray-700">
                  Document File <span className="text-red-500">*</span>
                </Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                  <Input
                    id="file-upload"
                    type="file"
                    onChange={handleFileSelect}
                    accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.jpg,.jpeg,.png,.gif"
                    className="hidden"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <div className="flex flex-col items-center gap-2">
                      <div className="p-3 bg-blue-100 rounded-full">
                        <Upload className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-700">
                          Click to upload or drag and drop
                        </p>
                        <p className="text-xs text-gray-500">
                          PDF, DOC, XLS, PPT, TXT, Images (Max 50MB)
                        </p>
                      </div>
                    </div>
                  </label>
                </div>
                {uploadForm.file && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3 mt-3">
                    <div className="flex items-center gap-2">
                      <div className="p-1 bg-green-100 rounded">
                        <FileText className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium text-green-800">
                          {uploadForm.file.name}
                        </p>
                        <p className="text-xs text-green-600">
                          {(uploadForm.file.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Document Information Section */}
            <div className="bg-gray-50 rounded-lg p-6 space-y-6">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Document Information</h3>
              </div>

              {/* Scope & Applicability */}
              <div className="space-y-2">
                <Label htmlFor="upload-scope" className="text-sm font-medium text-gray-700">
                  Scope & Applicability
                </Label>
                <Textarea
                  id="upload-scope"
                  value={uploadForm.scopeApplicability}
                  onChange={(e) => handleUploadFormChange('scopeApplicability', e.target.value)}
                  placeholder="Describe the scope and applicability of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>

              {/* Purpose */}
              <div className="space-y-2">
                <Label htmlFor="upload-purpose" className="text-sm font-medium text-gray-700">
                  Purpose <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="upload-purpose"
                  value={uploadForm.purpose}
                  onChange={(e) => handleUploadFormChange('purpose', e.target.value)}
                  placeholder="Explain the purpose and objectives of this document"
                  rows={3}
                  className="resize-none"
                />
              </div>
            </div>

            {/* Keywords Section */}
            <div className="bg-amber-50 rounded-lg p-6 space-y-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-6 bg-amber-500 rounded-full"></div>
                <h3 className="text-lg font-semibold text-gray-900">Search & Discovery</h3>
              </div>

              <div className="space-y-2">
                <Label htmlFor="upload-keywords" className="text-sm font-medium text-gray-700">
                  Keywords/Tags
                  <span className="text-xs text-gray-500 font-normal ml-2">(Optional)</span>
                </Label>
                <Textarea
                  id="upload-keywords"
                  value={uploadForm.keywords}
                  onChange={(e) => handleUploadFormChange('keywords', e.target.value)}
                  placeholder="Add keywords separated by commas for easy search and retrieval"
                  rows={2}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">
                  Example: safety, procedures, training, compliance, guidelines
                </p>
              </div>
            </div>
          </div>

          {/* Modal Footer */}
          <div className="flex justify-between items-center pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <div className="text-sm text-gray-600">
              <span className="font-medium">Required fields</span> are marked with <span className="text-red-500">*</span>
            </div>
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleUploadModalClose}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                onClick={handleUploadSubmit}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6"
              >
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Document Details Modal */}
      <Dialog open={isDocumentDetailsOpen} onOpenChange={setIsDocumentDetailsOpen}>
        <DialogContent className="max-w-6xl max-h-[95vh] overflow-y-auto">
          <DialogHeader className="pb-6 border-b">
            <DialogTitle className="text-2xl font-semibold text-gray-900 flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="h-6 w-6 text-blue-600" />
              </div>
              Document Details
            </DialogTitle>
            <p className="text-sm text-gray-600 mt-2">
              Complete information about the selected document
            </p>
          </DialogHeader>

          {selectedDocument && (
            <div className="py-6 space-y-8">
              {/* Basic Information */}
              <div className="bg-blue-50 rounded-lg p-6 space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-6 bg-blue-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Document ID</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <span className="text-blue-600 font-medium">{selectedDocument.maskId || 'N/A'}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Document Name</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <span className="font-medium">{selectedDocument.name}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Category</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <span>{selectedDocument.documentCategory?.name || selectedDocument.category}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Type</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <span className="capitalize">{selectedDocument.type}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Created Date</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <span>{selectedDocument.created ? format(new Date(selectedDocument.created), 'MMM dd, yyyy HH:mm') : 'N/A'}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Last Updated</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <span>{selectedDocument.updated ? format(new Date(selectedDocument.updated), 'MMM dd, yyyy HH:mm') : 'N/A'}</span>
                    </div>
                  </div>
                </div>

                {selectedDocument.keywords && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Keywords/Tags</Label>
                    <div className="p-3 bg-white rounded-lg border">
                      <div className="flex flex-wrap gap-2">
                        {selectedDocument.tags.map((tag, index) => (
                          <Badge key={index} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Content Information */}
              <div className="bg-green-50 rounded-lg p-6 space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-6 bg-green-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-gray-900">Content Information</h3>
                </div>

                <div className="space-y-4">
                  {selectedDocument.scopeApplicability && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Scope & Applicability</Label>
                      <div className="p-4 bg-white rounded-lg border">
                        <p className="text-gray-800">{selectedDocument.scopeApplicability}</p>
                      </div>
                    </div>
                  )}

                  {selectedDocument.purpose && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">Purpose</Label>
                      <div className="p-4 bg-white rounded-lg border">
                        <p className="text-gray-800">{selectedDocument.purpose}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Workflow Information */}
              <div className="bg-purple-50 rounded-lg p-6 space-y-6">
                <div className="flex items-center gap-2 mb-4">
                  <div className="w-2 h-6 bg-purple-500 rounded-full"></div>
                  <h3 className="text-lg font-semibold text-gray-900">Workflow Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Initiator */}
                  {selectedDocument.initiator && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-semibold text-xs">I</span>
                        </div>
                        <Label className="text-sm font-medium text-gray-700">Initiator</Label>
                      </div>
                      <div className="p-4 bg-white rounded-lg border space-y-2">
                        <p className="font-medium">{selectedDocument.initiator.firstName}</p>
                        {selectedDocument.initiator.email && (
                          <p className="text-sm text-gray-600">{selectedDocument.initiator.email}</p>
                        )}
                        {selectedDocument.initiator.company && (
                          <p className="text-sm text-gray-600">{selectedDocument.initiator.company}</p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Creator */}
                  {selectedDocument.creator && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-green-600 font-semibold text-xs">C</span>
                        </div>
                        <Label className="text-sm font-medium text-gray-700">Creator</Label>
                      </div>
                      <div className="p-4 bg-white rounded-lg border space-y-2">
                        <p className="font-medium">{selectedDocument.creator.firstName}</p>
                        {selectedDocument.creator.email && (
                          <p className="text-sm text-gray-600">{selectedDocument.creator.email}</p>
                        )}
                        {selectedDocument.creatorTargetDate && (
                          <p className="text-sm text-amber-600 font-medium">
                            Target: {format(new Date(selectedDocument.creatorTargetDate), 'MMM dd, yyyy')}
                          </p>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Reviewer */}
                  {selectedDocument.reviewer && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center">
                          <span className="text-orange-600 font-semibold text-xs">R</span>
                        </div>
                        <Label className="text-sm font-medium text-gray-700">Reviewer</Label>
                      </div>
                      <div className="p-4 bg-white rounded-lg border space-y-2">
                        <p className="font-medium">{selectedDocument.reviewer.firstName}</p>
                        {selectedDocument.reviewer.email && (
                          <p className="text-sm text-gray-600">{selectedDocument.reviewer.email}</p>
                        )}
                        {selectedDocument.reviewerTargetDate && (
                          <p className="text-sm text-amber-600 font-medium">
                            Target: {format(new Date(selectedDocument.reviewerTargetDate), 'MMM dd, yyyy')}
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                {/* Approver - Full width if exists */}
                {selectedDocument.approver && (
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                        <span className="text-purple-600 font-semibold text-xs">A</span>
                      </div>
                      <Label className="text-sm font-medium text-gray-700">Approver</Label>
                    </div>
                    <div className="p-4 bg-white rounded-lg border space-y-2 max-w-md">
                      <p className="font-medium">{selectedDocument.approver.firstName}</p>
                      {selectedDocument.approver.email && (
                        <p className="text-sm text-gray-600">{selectedDocument.approver.email}</p>
                      )}
                      {selectedDocument.approverTargetDate && (
                        <p className="text-sm text-amber-600 font-medium">
                          Target: {format(new Date(selectedDocument.approverTargetDate), 'MMM dd, yyyy')}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Modal Footer */}
          <div className="flex justify-end items-center pt-6 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4 rounded-b-lg">
            <Button
              variant="outline"
              onClick={() => setIsDocumentDetailsOpen(false)}
              className="px-6"
            >
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default DocumentPage;