import { store } from '../store';
import { setLogout } from '../store/slices/authSlice';

/**
 * Utility function to refresh the access token using the refresh token
 * @returns Promise<string> - The new access token
 * @throws Error if refresh fails
 */
export const refreshAccessToken = async (): Promise<string> => {
  const refresh_token = localStorage.getItem('refresh_token');
  
  if (!refresh_token) {
    throw new Error('No refresh token available');
  }

  // Get necessary details from localStorage
  const COGNITO_USER_DOMAIN = localStorage.getItem('COGNITO_USER_DOMAIN');
  const COGNITO_USER_APP_CLIENT_ID = localStorage.getItem('COGNITO_USER_APP_CLIENT_ID');

  if (!COGNITO_USER_DOMAIN || !COGNITO_USER_APP_CLIENT_ID) {
    throw new Error('Missing Cognito configuration for token refresh');
  }

  // Prepare the request body for refreshing the token
  const requestBody = `grant_type=refresh_token&client_id=${COGNITO_USER_APP_CLIENT_ID}&refresh_token=${refresh_token}`;

  // Call the token refresh endpoint
  const response = await fetch(`${COGNITO_USER_DOMAIN}/oauth2/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: requestBody
  });

  if (!response.ok) {
    throw new Error(`Token refresh failed with status: ${response.status}`);
  }

  const tokenData = await response.json();
  
  // Store the new access token
  localStorage.setItem('access_token', tokenData.access_token);
  
  // Update the store with new tokens
  store.dispatch({
    type: 'auth/setTokens',
    payload: {
      accessToken: tokenData.access_token,
      refreshToken: tokenData.refresh_token || refresh_token // Use new refresh token if provided
    }
  });

  return tokenData.access_token;
};

/**
 * Utility function to handle logout when token refresh fails
 */
export const handleTokenRefreshFailure = (): void => {
  console.log('Token refresh failed - Logging out user');
  
  try {
    // Dispatch logout action to update Redux state
    store.dispatch(setLogout());

    // Clear all auth-related data from localStorage
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('headerLogoUrl');
    localStorage.removeItem('COGNITO_USER_DOMAIN');
    localStorage.removeItem('COGNITO_USER_APP_CLIENT_ID');

    // Only redirect if we're not already on the login page
    if (!window.location.pathname.includes('/login')) {
      window.location.replace('/login');
    }
  } catch (logoutError) {
    console.error('Error during logout process:', logoutError);
  }
};

/**
 * Check if the current access token is expired or about to expire
 * Note: This is a basic implementation. In a real app, you might want to 
 * decode the JWT token and check its expiration time.
 */
export const isTokenExpired = (): boolean => {
  const token = localStorage.getItem('access_token');
  if (!token) return true;
  
  // For now, we'll rely on the 401 response to determine if token is expired
  // In a more sophisticated implementation, you could decode the JWT and check exp claim
  return false;
};
