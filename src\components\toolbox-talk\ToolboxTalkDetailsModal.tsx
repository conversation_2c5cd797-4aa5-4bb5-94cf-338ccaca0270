import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import ImageComponent from '@/components/common/ImageComponent';
import {
  User,
  MapPin,
  Users,
  FileImage,
  AlertTriangle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { formatDateTime } from '@/utils/dateUtils';
import apiService from '@/services/apiService';
import { useToast } from '@/components/ui/use-toast';

interface ToolboxTalkDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  toolboxTalkId: string | null;
}

interface ToolboxTalkDetails {
  id: string;
  maskId: string;
  commenceDate: string;
  created: string;
  conductedBy: {
    firstName: string;
    lastName?: string;
  };
  riskAssessment?: {
    workActivity: {
      name: string;
    };
  };
  locationOne?: { name: string };
  locationTwo?: { name: string };
  locationThree?: { name: string };
  locationFour?: { name: string };
  locationFive?: { name: string };
  locationSix?: { name: string };
  noOfPersonsParticipated: number;
  uploads: string[];
  tasks?: Array<{
    activity?: {
      type: string;
      name: string;
    };
    hazards?: {
      selected: Array<{
        id: string;
        name: string;
        image: string;
        toolbox_value: string;
        toolbox_remarks?: string;
      }>;
    };
    currentControl?: {
      option: Array<{
        value: string;
        toolbox_value: string;
        toolbox_remarks?: string;
      }>;
    };
  }>;
  controls: {
    isAdditionalControlsIdentified: boolean;
    describeAdditionalControls?: string;
    teamBrief: string;
    teamBriefRemarks?: string;
  };
  toolboxSignStatuses: Array<{
    signedBy: {
      firstName: string;
      lastName?: string;
    };
    sign: string;
    signedDate: string;
  }>;
  isCloseOutChallenges: boolean;
  closeOutChallenges?: Array<{
    unexpectedChallenges: string;
    remarks: string;
  }>;
}

const ToolboxTalkDetailsModal = ({ open, onOpenChange, toolboxTalkId }: ToolboxTalkDetailsModalProps) => {
  const [toolboxTalkDetails, setToolboxTalkDetails] = useState<ToolboxTalkDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchToolboxTalkDetails = async () => {
      if (!toolboxTalkId) return;

      setLoading(true);
      try {
        const uriString = {
          include: [
            { relation: "locationOne" },
            { relation: "locationTwo" },
            { relation: "locationThree" },
            { relation: "locationFour" },
            { relation: "locationFive" },
            { relation: "locationSix" },
            { relation: "conductedBy" },
            {
              relation: "riskAssessment",
              scope: {
                include: [{ relation: "workActivity" }]
              }
            },
            {
              relation: "toolboxSignStatuses",
              scope: {
                include: [{ relation: "signedBy" }]
              }
            }
          ]
        };

        const url = `/toolbox-talks/${toolboxTalkId}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
        const response = await apiService.get(url);
        setToolboxTalkDetails(response);
      } catch (error) {
        console.error('Error fetching toolbox talk details:', error);
        toast({
          title: "Error",
          description: "Failed to fetch toolbox talk details. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };

    if (open && toolboxTalkId) {
      fetchToolboxTalkDetails();
    }
  }, [open, toolboxTalkId, toast]);

  const getLocationString = (details: ToolboxTalkDetails) => {
    const locations = [
      details.locationOne?.name,
      details.locationTwo?.name,
      details.locationThree?.name,
      details.locationFour?.name,
      details.locationFive?.name,
      details.locationSix?.name
    ].filter(Boolean);
    
    return locations.length > 0 ? locations.join(' > ') : '-';
  };

  const InfoCard = ({ icon: Icon, title, children }: { icon: React.ComponentType<{ className?: string }>, title: string, children: React.ReactNode }) => (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Icon className="h-5 w-5 text-primary" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {children}
      </CardContent>
    </Card>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col p-0"
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader className="px-6 py-4 border-b flex-shrink-0">
          <DialogTitle className="text-xl font-semibold">
            Toolbox Talk Details - {toolboxTalkDetails?.maskId || 'Loading...'}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto px-6 py-4">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
                <p className="text-muted-foreground">Loading toolbox talk details...</p>
              </div>
            </div>
          ) : toolboxTalkDetails ? (
            <div className="space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InfoCard icon={Clock} title="Timing Information">
                  <div className="space-y-3">
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Commence TBT:</span>
                      <p className="text-sm">{formatDateTime(toolboxTalkDetails.commenceDate)}</p>
                    </div>
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Completed:</span>
                      <p className="text-sm">{formatDateTime(toolboxTalkDetails.created)}</p>
                    </div>
                  </div>
                </InfoCard>

                <InfoCard icon={User} title="Conducted By">
                  <div className="space-y-3">
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Conductor:</span>
                      <p className="text-sm">
                        {toolboxTalkDetails.conductedBy?.firstName}
                        {toolboxTalkDetails.conductedBy?.lastName && ` ${toolboxTalkDetails.conductedBy.lastName}`}
                      </p>
                    </div>
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Participants:</span>
                      <p className="text-sm">{toolboxTalkDetails.noOfPersonsParticipated} persons</p>
                    </div>
                  </div>
                </InfoCard>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <InfoCard icon={MapPin} title="Location & Activity">
                  <div className="space-y-3">
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Work Activity:</span>
                      <p className="text-sm">{toolboxTalkDetails.riskAssessment?.workActivity?.name || '-'}</p>
                    </div>
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Location:</span>
                      <p className="text-sm">{getLocationString(toolboxTalkDetails)}</p>
                    </div>
                  </div>
                </InfoCard>

                <InfoCard icon={FileImage} title="Evidence">
                  <div className="space-y-3">
                    {toolboxTalkDetails.uploads && toolboxTalkDetails.uploads.length > 0 ? (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {toolboxTalkDetails.uploads.map((fileName, index) => (
                          <div key={index} className="group relative">
                            <div className="relative bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
                              <div className="aspect-square rounded-md overflow-hidden bg-gray-50">
                                <ImageComponent fileName={fileName} size="100" name={true} />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No evidence uploaded</p>
                    )}
                  </div>
                </InfoCard>
              </div>

              {/* Sub Activities */}
              {toolboxTalkDetails.tasks && toolboxTalkDetails.tasks.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5 text-primary" />
                      Sub-Activities
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {toolboxTalkDetails.tasks.map((task, index) => (
                      <div key={index} className="border rounded-lg p-4 space-y-3">
                        <h4 className="font-medium text-primary">Sub-Activity {index + 1}</h4>
                        
                        {task.activity?.type === "activity" && (
                          <div>
                            <span className="font-medium text-sm">Activity:</span>
                            <span className="ml-2 text-sm">{task.activity.name}</span>
                          </div>
                        )}

                        {/* Hazards */}
                        {task.hazards?.selected && task.hazards.selected.length > 0 && (
                          <div className="space-y-2">
                            <h5 className="font-medium text-sm">Hazards:</h5>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {task.hazards.selected.map((hazard) => (
                                <div key={hazard.id} className="border rounded-lg p-3 bg-muted/50">
                                  <div className="flex items-start gap-3">
                                    <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center">
                                      <AlertTriangle className="h-6 w-6 text-orange-500" />
                                    </div>
                                    <div className="flex-1">
                                      <p className="font-medium text-sm">{hazard.name}</p>
                                      <Badge variant={hazard.toolbox_value === "Yes" ? "default" : "destructive"} className="mt-1">
                                        {hazard.toolbox_value}
                                      </Badge>
                                      {(hazard.toolbox_value === "No" || hazard.toolbox_value === "Not Applicable") && hazard.toolbox_remarks && (
                                        <p className="text-xs text-muted-foreground mt-1">
                                          <strong>Remarks:</strong> {hazard.toolbox_remarks}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Controls */}
                        {task.currentControl?.option && task.currentControl.option.length > 0 && (
                          <div className="space-y-2">
                            <h5 className="font-medium text-sm">Controls:</h5>
                            {task.currentControl.option.map((control, controlIndex) => (
                              <div key={controlIndex} className="border-l-4 border-primary pl-3 py-2">
                                <p className="text-sm">{control.value || "No control description provided"}</p>
                                <div className="flex items-center gap-2 mt-1">
                                  <span className="text-xs font-medium">Implemented:</span>
                                  <Badge variant={control.toolbox_value === "Yes" ? "default" : "destructive"} className="text-xs">
                                    {control.toolbox_value}
                                  </Badge>
                                </div>
                                {(control.toolbox_value === "No" || control.toolbox_value === "Not Applicable") && control.toolbox_remarks && (
                                  <p className="text-xs text-muted-foreground mt-1">
                                    <strong>Remarks:</strong> {control.toolbox_remarks}
                                  </p>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              )}

              {/* Additional Controls */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-primary" />
                    Additional Controls
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Identified:</span>
                    <Badge variant={toolboxTalkDetails.controls.isAdditionalControlsIdentified ? "default" : "secondary"} className="ml-2">
                      {toolboxTalkDetails.controls.isAdditionalControlsIdentified ? "Yes" : "No"}
                    </Badge>
                  </div>

                  {toolboxTalkDetails.controls.isAdditionalControlsIdentified && toolboxTalkDetails.controls.describeAdditionalControls && (
                    <div>
                      <span className="font-medium text-sm text-muted-foreground">Description:</span>
                      <p className="text-sm mt-1 p-3 bg-muted rounded-lg">{toolboxTalkDetails.controls.describeAdditionalControls}</p>
                    </div>
                  )}

                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Team Briefed:</span>
                    <Badge variant={toolboxTalkDetails.controls.teamBrief === "Yes" ? "default" : "destructive"} className="ml-2">
                      {toolboxTalkDetails.controls.teamBrief}
                    </Badge>
                    {(toolboxTalkDetails.controls.teamBrief === "No" || toolboxTalkDetails.controls.teamBrief === "Not Applicable") && toolboxTalkDetails.controls.teamBriefRemarks && (
                      <p className="text-sm text-muted-foreground mt-1">
                        <strong>Remarks:</strong> {toolboxTalkDetails.controls.teamBriefRemarks}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Team Member Acknowledgement */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-primary" />
                    Team Member Acknowledgement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {toolboxTalkDetails.toolboxSignStatuses && toolboxTalkDetails.toolboxSignStatuses.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {toolboxTalkDetails.toolboxSignStatuses.map((signature, index) => (
                        <div key={index} className="border rounded-lg p-4 space-y-3">
                          <div>
                            <span className="font-medium text-sm text-muted-foreground">Member:</span>
                            <p className="text-sm">
                              {signature.signedBy?.firstName}
                              {signature.signedBy?.lastName && ` ${signature.signedBy.lastName}`}
                            </p>
                          </div>
                          <div>
                            <span className="font-medium text-sm text-muted-foreground">Signed Date:</span>
                            <p className="text-sm">{formatDateTime(signature.signedDate)}</p>
                          </div>
                          <div className="signature-container">
                            <span className="font-medium text-sm text-muted-foreground mb-2 block">Signature:</span>
                            {signature.sign ? (
                              <div className="w-32 h-16 bg-white border border-gray-200 rounded-lg p-2 shadow-sm overflow-hidden">
                                <ImageComponent fileName={signature.sign} size="32" name={false} />
                              </div>
                            ) : (
                              <div className="w-32 h-16 bg-muted rounded border flex items-center justify-center">
                                <span className="text-xs text-muted-foreground">No Signature</span>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-muted-foreground">No signatures available</p>
                  )}
                </CardContent>
              </Card>

              {/* Close Out */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <AlertTriangle className="h-5 w-5 text-primary" />
                    Close Out
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <span className="font-medium text-sm text-muted-foreground">Unexpected Challenges:</span>
                    <Badge variant={toolboxTalkDetails.isCloseOutChallenges ? "destructive" : "default"} className="ml-2">
                      {toolboxTalkDetails.isCloseOutChallenges ? "Yes" : "No"}
                    </Badge>
                  </div>

                  {toolboxTalkDetails.isCloseOutChallenges && toolboxTalkDetails.closeOutChallenges && toolboxTalkDetails.closeOutChallenges.length > 0 && (
                    <div className="space-y-3">
                      {toolboxTalkDetails.closeOutChallenges.map((challenge, index) => (
                        <div key={index} className="border rounded-lg p-4 bg-muted/50 space-y-2">
                          <div>
                            <span className="font-medium text-sm text-muted-foreground">Challenge:</span>
                            <p className="text-sm mt-1">{challenge.unexpectedChallenges}</p>
                          </div>
                          <div>
                            <span className="font-medium text-sm text-muted-foreground">Remarks:</span>
                            <p className="text-sm mt-1">{challenge.remarks}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">No details available</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ToolboxTalkDetailsModal;
