import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import CreatePermitModal from '../CreatePermitModal';
import authSlice from '@/store/slices/authSlice';

// Mock the API service
jest.mock('@/services/apiService', () => ({
  get: jest.fn(),
  post: jest.fn(),
}));

// Mock the AllFilterLocation component
jest.mock('@/components/observations/AllFilterLocation', () => {
  return function MockAllFilterLocation({ handleFilter }: any) {
    return (
      <div data-testid="location-selector">
        <button
          onClick={() => handleFilter('loc1', 'loc2', 'loc3', 'loc4', 'loc5', 'loc6')}
        >
          Select Location
        </button>
      </div>
    );
  };
});

// Mock the MultiFileUpload component
jest.mock('@/components/common/MultiFileUpload', () => {
  return function MockMultiFileUpload({ onFilesChange }: any) {
    return (
      <div data-testid="file-upload">
        <button
          onClick={() => onFilesChange([new File(['test'], 'test.jpg', { type: 'image/jpeg' })])}
        >
          Upload File
        </button>
      </div>
    );
  };
});

// Mock react-signature-canvas
jest.mock('react-signature-canvas', () => {
  return React.forwardRef((props: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      clear: jest.fn(),
      isEmpty: jest.fn(() => false),
      getTrimmedCanvas: jest.fn(() => ({
        toDataURL: jest.fn(() => 'data:image/png;base64,test')
      }))
    }));
    return <canvas data-testid="signature-canvas" {...props} />;
  });
});

// Create a mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
    },
    preloadedState: {
      auth: {
        isAuthenticated: true,
        accessToken: 'test-token',
        refreshToken: 'test-refresh-token',
        loginConfig: null,
        user: {
          id: 'test-user-id',
          email: '<EMAIL>',
          firstName: 'Test',
          lastName: 'User',
          username: 'testuser',
          role: 'user',
        },
        isLoading: false,
        error: null,
      },
      ...initialState,
    },
  });
};

describe('CreatePermitModal', () => {
  const mockProps = {
    isOpen: true,
    onClose: jest.fn(),
    onSuccess: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the modal when open', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} />
      </Provider>
    );

    expect(screen.getByText('Electronic Permit to Work (ePTW) Application')).toBeInTheDocument();
    expect(screen.getByText('Instructions to Permit Applicant:')).toBeInTheDocument();
  });

  it('displays work type selection', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} />
      </Provider>
    );

    expect(screen.getByText('Permit Work Type *')).toBeInTheDocument();
  });

  it('shows form fields for permit details', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} />
      </Provider>
    );

    expect(screen.getByText('Work Description *')).toBeInTheDocument();
    expect(screen.getByText('Responsible Site / Job Supervisor *')).toBeInTheDocument();
    expect(screen.getByText('Number of Workers *')).toBeInTheDocument();
    expect(screen.getByText('Applicant Contact Number *')).toBeInTheDocument();
    expect(screen.getByText('Supervisor Contact Number *')).toBeInTheDocument();
  });

  it('includes signature canvas', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} />
      </Provider>
    );

    expect(screen.getByText('Applicant Signature')).toBeInTheDocument();
    expect(screen.getByTestId('signature-canvas')).toBeInTheDocument();
  });

  it('has submit and cancel buttons', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} />
      </Provider>
    );

    expect(screen.getByText('Submit Permit')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} />
      </Provider>
    );

    fireEvent.click(screen.getByText('Cancel'));
    expect(mockProps.onClose).toHaveBeenCalled();
  });

  it('does not render when closed', () => {
    const store = createMockStore();
    
    render(
      <Provider store={store}>
        <CreatePermitModal {...mockProps} isOpen={false} />
      </Provider>
    );

    expect(screen.queryByText('Electronic Permit to Work (ePTW) Application')).not.toBeInTheDocument();
  });
});
