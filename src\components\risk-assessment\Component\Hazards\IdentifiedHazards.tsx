import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';

interface HazardItem {
  id: string;
  name: string;
  image?: string;
  hazardName?: string;
}

interface IdentifiedHazardsProps {
  selectedHazards: HazardItem[];
  onDeleteHaz: (hazard: HazardItem) => void;
}

const IdentifiedHazards: React.FC<IdentifiedHazardsProps> = ({ selectedHazards, onDeleteHaz }) => {
  const hasSelectedIndustries = localStorage.getItem('SELECTED_INDUSTRIES');

  if (selectedHazards.length === 0) {
    return null;
  }

  return (
    <div className="mt-6">
      <h6 className="text-lg font-semibold mb-4">Hazards Identified</h6>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
        {selectedHazards.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-white shadow-sm">
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              {!hasSelectedIndustries && item.image && (
                <img
                  src={`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${item.image}`}
                  alt={item.name}
                  className="w-8 h-8 object-contain flex-shrink-0"
                  onError={(e) => {
                    (e.target as HTMLImageElement).style.display = 'none';
                  }}
                />
              )}
              <p className="text-sm font-medium text-gray-900 truncate">
                {item.name}
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteHaz(item)}
              className="h-6 w-6 p-0 text-gray-400 hover:text-red-500 flex-shrink-0 ml-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default IdentifiedHazards;
