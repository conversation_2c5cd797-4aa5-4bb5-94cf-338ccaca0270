import { format, parseISO, isValid } from 'date-fns';

/**
 * Format a date string to DD-MM-YYYY format
 * @param dateString - ISO date string or date string
 * @returns Formatted date string or empty string if invalid
 */
export const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return '';
  
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return '';
    return format(date, 'dd-MM-yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format a date string to a more readable format
 * @param dateString - ISO date string or date string
 * @returns Formatted date string or empty string if invalid
 */
export const formatDateReadable = (dateString: string | null | undefined): string => {
  if (!dateString) return '';

  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return '';
    return format(date, 'dd MMM yyyy');
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
};

/**
 * Format a date string to include both date and time
 * @param dateString - ISO date string or date string
 * @returns Formatted date and time string or empty string if invalid
 */
export const formatDateTime = (dateString: string | null | undefined): string => {
  if (!dateString) return '';

  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return '';
    return format(date, 'dd-MM-yyyy HH:mm');
  } catch (error) {
    console.error('Error formatting date time:', error);
    return '';
  }
};

/**
 * Get the status of a date relative to today
 * @param dateString - ISO date string
 * @returns Status: 'upcoming', 'due-now', 'overdue'
 */
export const getDateStatus = (dateString: string | null | undefined): 'upcoming' | 'due-now' | 'overdue' => {
  if (!dateString) return 'upcoming';
  
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return 'upcoming';
    
    const today = new Date();
    const diffTime = date.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'overdue';
    if (diffDays <= 7) return 'due-now';
    return 'upcoming';
  } catch (error) {
    console.error('Error getting date status:', error);
    return 'upcoming';
  }
};
