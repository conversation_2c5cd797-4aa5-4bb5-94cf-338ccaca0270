import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Upload, X, Image, FileText } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MultiFileUploadProps {
  onFilesChange: (files: File[]) => void;
  acceptedTypes?: string[];
  maxFiles?: number;
  maxFileSize?: number; // in bytes
  className?: string;
  disabled?: boolean;
  description?: string;
}

interface FilePreview {
  file: File;
  id: string;
  preview?: string;
}

const MultiFileUpload: React.FC<MultiFileUploadProps> = ({
  onFilesChange,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/jpg'],
  maxFiles = 5,
  maxFileSize = 104857600, // 100MB default
  className,
  disabled = false,
  description = 'Upload images'
}) => {
  const { toast } = useToast();
  const [selectedFiles, setSelectedFiles] = useState<FilePreview[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const validateFile = (file: File): boolean => {
    // Check file type
    if (acceptedTypes.length > 0 && !acceptedTypes.includes(file.type)) {
      toast({
        title: "Invalid file type",
        description: `${file.name} is not a supported file type. Accepted types: ${acceptedTypes.join(', ')}`,
        variant: "destructive"
      });
      return false;
    }

    // Check file size
    if (file.size > maxFileSize) {
      toast({
        title: "File too large",
        description: `${file.name} is too large. Maximum size is ${formatFileSize(maxFileSize)}.`,
        variant: "destructive"
      });
      return false;
    }

    return true;
  };

  const processFiles = (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    
    // Check if adding these files would exceed the maximum
    if (selectedFiles.length + fileArray.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `You can upload a maximum of ${maxFiles} files. Currently selected: ${selectedFiles.length}`,
        variant: "destructive"
      });
      return;
    }

    const validFiles: FilePreview[] = [];

    fileArray.forEach((file) => {
      if (validateFile(file)) {
        const id = Math.random().toString(36).substr(2, 9);
        const filePreview: FilePreview = {
          file,
          id,
        };

        // Create preview for images
        if (file.type.startsWith('image/')) {
          filePreview.preview = URL.createObjectURL(file);
        }

        validFiles.push(filePreview);
      }
    });

    if (validFiles.length > 0) {
      const newFiles = [...selectedFiles, ...validFiles];
      setSelectedFiles(newFiles);
      onFilesChange(newFiles.map(f => f.file));
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      processFiles(e.target.files);
      // Reset input value so same file can be selected again
      e.target.value = '';
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      processFiles(e.dataTransfer.files);
    }
  };

  const removeFile = (id: string) => {
    const updatedFiles = selectedFiles.filter(f => f.id !== id);
    setSelectedFiles(updatedFiles);
    onFilesChange(updatedFiles.map(f => f.file));
    
    // Clean up preview URLs
    const removedFile = selectedFiles.find(f => f.id === id);
    if (removedFile?.preview) {
      URL.revokeObjectURL(removedFile.preview);
    }
  };

  const clearAllFiles = () => {
    // Clean up all preview URLs
    selectedFiles.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    setSelectedFiles([]);
    onFilesChange([]);
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
          isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
          disabled ? "opacity-50 cursor-not-allowed" : "hover:border-primary hover:bg-primary/5"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={!disabled ? triggerFileSelect : undefined}
      >
        <Upload className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground mb-2">
          {description}
        </p>
        <p className="text-xs text-muted-foreground">
          Drag and drop files here, or click to select files
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          Max {maxFiles} files, up to {formatFileSize(maxFileSize)} each
        </p>
        
        <Input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />
      </div>

      {/* Selected Files */}
      {selectedFiles.length > 0 && (
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium">
              Selected Files ({selectedFiles.length}/{maxFiles})
            </Label>
            <Button
              variant="outline"
              size="sm"
              onClick={clearAllFiles}
              disabled={disabled}
            >
              Clear All
            </Button>
          </div>
          
          <div className="grid grid-cols-1 gap-2">
            {selectedFiles.map((filePreview) => (
              <div
                key={filePreview.id}
                className="flex items-center gap-3 p-3 border rounded-lg bg-muted/20"
              >
                {/* File Icon/Preview */}
                <div className="flex-shrink-0">
                  {filePreview.preview ? (
                    <img
                      src={filePreview.preview}
                      alt={filePreview.file.name}
                      className="w-10 h-10 object-cover rounded"
                    />
                  ) : (
                    <div className="w-10 h-10 bg-muted rounded flex items-center justify-center">
                      {filePreview.file.type.startsWith('image/') ? (
                        <Image className="w-5 h-5 text-muted-foreground" />
                      ) : (
                        <FileText className="w-5 h-5 text-muted-foreground" />
                      )}
                    </div>
                  )}
                </div>
                
                {/* File Info */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {filePreview.file.name}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(filePreview.file.size)}
                  </p>
                </div>
                
                {/* Remove Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFile(filePreview.id)}
                  disabled={disabled}
                  className="flex-shrink-0 h-8 w-8 p-0"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiFileUpload;
