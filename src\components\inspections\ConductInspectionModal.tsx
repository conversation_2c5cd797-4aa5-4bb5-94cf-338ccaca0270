import React, { useState, useRef, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/components/ui/use-toast';
import { CalendarIcon, Upload, X, Plus, Trash2, PenTool, TextCursor } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import ImageComponent from '@/components/common/ImageComponent';
import SignatureCanvas, { SignatureCanvasProps } from 'react-signature-canvas';

// API endpoints
const FILE_URL = `${API_BASE_URL}/files`;
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;
const SUBMIT_CHECKLIST_INSPECTION = (id: string) => `${API_BASE_URL}/inspection-checklist-submit/${id}`;

interface ConductInspectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  inspectionDetails: any;
  actionData: any;
}

// New checklist structure interfaces
interface ChecklistMetadata {
  name: string;
  version: string;
  createdAt: string;
  totalComponents: number;
  modes: {
    communicate: number;
    feedback: number;
  };
}

interface ChecklistComponent {
  id: string;
  type: string;
  position: number;
  data: {
    id?: string;
    type: string;
    position: number;
    required: boolean;
    allowFromLibrary: boolean;
    // Common fields
    title?: string;
    text?: string;
    label?: string;
    content?: string;
    // Checkpoint group specific
    checkpoints?: ChecklistCheckpoint[];
    groupAnswer?: string;
    reason?: string;
    // Response fields
    selected?: string;
    remarks?: string;
    actionToBeTaken?: string;
    dueDate?: Date | null;
    assignee?: string;
    uploads?: string[];
    // Date component specific
    selectedDate?: Date | null;
    // Signature component specific
    signature?: string;
    // Text input component specific
    placeholder?: string;
    textValue?: string;
    // Checkpoint group component specific
    isChecked?: boolean;
  };
  validation: {
    isValid: boolean;
    lastValidated: string;
  };
}

interface ChecklistCheckpoint {
  id: string;
  text: string;
  description?: string;
  // Response data
  selected?: string;
  remarks?: string;
  actionToBeTaken?: string;
  dueDate?: Date | null;
  assignee?: string;
  uploads?: string[];
}

interface ChecklistStructure {
  metadata: ChecklistMetadata;
  components: ChecklistComponent[];
}

// Legacy interfaces for backward compatibility
interface ChecklistGroup {
  label: string;
  groupAnswer?: string; // Keep for backward compatibility
  reason?: string; // Keep for backward compatibility
  isChecked?: boolean; // New checkbox approach
  questions: ChecklistQuestion[];
}

interface ChecklistQuestion {
  label: string;
  selected: string;
  remarks: string;
  actionToBeTaken: string;
  dueDate: Date | null;
  assignee: string;
  uploads: string[];
}

interface PostAction {
  actionToBeTaken: string;
  dueDate: Date | null;
  uploads: string[];
  assignee: string;
}

interface AssigneeOption {
  label: string;
  value: string;
}

const ConductInspectionModal: React.FC<ConductInspectionModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  inspectionDetails,
  actionData
}) => {
  console.log(actionData)
  const { toast } = useToast();
  const signRefs = useRef<{ [key: string]: SignatureCanvas | null }>({});

  // State management
  const [checklistData, setChecklistData] = useState<ChecklistComponent[]>([]);
  const [isLegacyFormat, setIsLegacyFormat] = useState(false);
  const [legacyChecklistData, setLegacyChecklistData] = useState<ChecklistGroup[]>([]);
  const [postActions, setPostActions] = useState<PostAction[]>([]);
  const [showPostActions, setShowPostActions] = useState(false);
  const [assignees, setAssignees] = useState<AssigneeOption[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSignaturePad, setShowSignaturePad] = useState<{ [key: string]: boolean }>({});

  // Error handling
  const [errorMap, setErrorMap] = useState({
    group: {},
    checklist: {},
    post: {},
  });

  // Initialize checklist data - handle both new and legacy formats
  useEffect(() => {
    if (inspectionDetails?.checklist?.value) {
      const checklistValue = inspectionDetails.checklist.value;

      // Check if it's the new format (has components array)
      if (checklistValue.components && Array.isArray(checklistValue.components)) {
        // New format with components
        setChecklistData(checklistValue.components);
        setIsLegacyFormat(false);
      } else if (Array.isArray(checklistValue)) {
        // Legacy format (array of groups)
        setLegacyChecklistData(JSON.parse(JSON.stringify(checklistValue)));
        setIsLegacyFormat(true);
      } else {
        console.warn('Unknown checklist format:', checklistValue);
      }
    }
  }, [inspectionDetails]);

  // Fetch assignees
  const fetchAssignees = useCallback(async () => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: inspectionDetails?.locationOneId || '',
        locationTwoId: inspectionDetails?.locationTwoId || '',
        locationThreeId: inspectionDetails?.locationThreeId || '',
        locationFourId: inspectionDetails?.locationFourId || '',
        mode: 'ins_action_owner',
      });

      const data = response.map((item: any) => ({
        label: item.firstName,
        value: item.id
      }));
      setAssignees(data);
    } catch (error) {
      console.error('Error fetching assignees:', error);
      toast({
        title: "Error",
        description: "Failed to fetch assignees",
        variant: "destructive"
      });
    }
  }, [toast]);

  useEffect(() => {
    if (isOpen) {
      fetchAssignees();
    }
  }, [isOpen, fetchAssignees]);

  // File upload handler - supports both formats
  const handleFileUpload = async (files: FileList | null, groupIndex: number, questionIndex: number) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await apiService.post(FILE_URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      if (response?.files?.[0]?.originalname) {
        const uploaded = response.files[0].originalname;

        if (isLegacyFormat) {
          // Legacy format
          const updated = [...legacyChecklistData];
          const uploads = updated[groupIndex].questions[questionIndex].uploads || [];
          updated[groupIndex].questions[questionIndex].uploads = [...uploads, uploaded];
          setLegacyChecklistData(updated);
        } else {
          // New format
          const updated = [...checklistData];
          const component = updated[groupIndex];

          if (component.type === 'checkpoint') {
            // Individual checkpoint - store uploads directly on component.data
            const uploads = component.data.uploads || [];
            component.data.uploads = [...uploads, uploaded];
          } else if (component.type === 'checkpoint-group') {
            // Checkpoint group - store uploads on specific checkpoint
            const checkpoints = component.data.checkpoints || [];
            if (checkpoints[questionIndex]) {
              const uploads = checkpoints[questionIndex].uploads || [];
              checkpoints[questionIndex].uploads = [...uploads, uploaded];
            }
          } else if (component.type === 'sign') {
            // Sign component - store single signature (allow replacement)
            component.data.signature = uploaded;
          }

          setChecklistData(updated);
        }

        toast({
          title: "Success",
          description: "File uploaded successfully",
        });
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast({
        title: "Error",
        description: "Failed to upload file",
        variant: "destructive"
      });
    }
  };

  // Remove image handler - supports both formats
  const handleRemoveImage = (gIdx: number, qIdx: number, imgIdx: number) => {
    if (isLegacyFormat) {
      // Legacy format
      const updated = [...legacyChecklistData];
      const uploads = updated[gIdx].questions[qIdx].uploads || [];
      uploads.splice(imgIdx, 1);
      updated[gIdx].questions[qIdx].uploads = uploads;
      setLegacyChecklistData(updated);
    } else {
      // New format
      const updated = [...checklistData];
      const component = updated[gIdx];

      if (component.type === 'checkpoint') {
        // Individual checkpoint - remove from component.data.uploads
        if (component.data.uploads) {
          component.data.uploads.splice(imgIdx, 1);
        }
      } else if (component.type === 'checkpoint-group') {
        // Checkpoint group - remove from specific checkpoint
        const checkpoints = component.data.checkpoints || [];
        if (checkpoints[qIdx] && checkpoints[qIdx].uploads) {
          checkpoints[qIdx].uploads!.splice(imgIdx, 1);
        }
      } else if (component.type === 'sign') {
        // Sign component - allow signature removal
        component.data.signature = undefined;
      }

      setChecklistData(updated);
    }
  };

  // Post action handlers
  const handlePostActionChange = (index: number, field: string, value: any) => {
    const updated = [...postActions];
    updated[index][field as keyof PostAction] = value;
    setPostActions(updated);
  };

  const handlePostFileUpload = async (files: FileList | null, index: number) => {
    if (!files || files.length === 0) return;

    const file = files[0];
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await apiService.post(FILE_URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      if (response?.files?.[0]?.originalname) {
        const uploadedFile = response.files[0].originalname;
        const updated = [...postActions];
        updated[index].uploads.push(uploadedFile);
        setPostActions(updated);

        toast({
          title: "Success",
          description: "File uploaded successfully",
        });
      }
    } catch (error) {
      console.error('Post Action file upload error:', error);
      toast({
        title: "Error",
        description: "Failed to upload file",
        variant: "destructive"
      });
    }
  };

  const addNewPostAction = () => {
    setPostActions([...postActions, {
      actionToBeTaken: '',
      dueDate: null,
      uploads: [],
      assignee: ''
    }]);
  };

  const handleRemovePostAction = (idx: number) => {
    const updated = [...postActions];
    updated.splice(idx, 1);
    setPostActions(updated);
  };

  const handleRemovePostImage = (actionIndex: number, imageIndex: number) => {
    const updated = [...postActions];
    updated[actionIndex].uploads.splice(imageIndex, 1);
    setPostActions(updated);
  };

  // Signature handling functions
  const handleSaveSignature = async (componentId: string, gIdx: number) => {
    const componentKey = `${componentId}-${gIdx}`;
    const canvas = signRefs.current[componentKey];

    console.log('Save signature called:', { componentId, gIdx, componentKey, canvas });

    if (!canvas) {
      toast({
        title: "Error",
        description: "Signature canvas not found",
        variant: "destructive"
      });
      return;
    }

    if (canvas.isEmpty()) {
      toast({
        title: "Error",
        description: "Please provide a signature before saving",
        variant: "destructive"
      });
      return;
    }

    // Allow signature replacement - no need to check for existing signature

    try {
      // Convert canvas to blob with high quality
      const dataURL = canvas.toDataURL('image/png', 1.0); // Maximum quality
      console.log('DataURL generated:', dataURL.substring(0, 50) + '...');

      const response = await fetch(dataURL);
      const blob = await response.blob();
      console.log('Blob created:', blob.size, 'bytes');

      // Create FormData and upload
      const formData = new FormData();
      const fileName = `signature_${componentId}_${Date.now()}.png`;
      formData.append('file', blob, fileName);
      console.log('FormData created with filename:', fileName);

      const uploadResponse = await apiService.post(FILE_URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      console.log('Upload response:', uploadResponse);

      if (uploadResponse?.files?.[0]?.originalname) {
        const uploaded = uploadResponse.files[0].originalname;
        console.log('File uploaded successfully:', uploaded);

        // Update component data with single signature file
        const updated = [...checklistData];
        updated[gIdx].data.signature = uploaded; // Store as single signature reference
        setChecklistData(updated);

        // Hide signature pad
        setShowSignaturePad(prev => ({ ...prev, [componentKey]: false }));

        toast({
          title: "Success",
          description: "Signature saved successfully",
        });
      } else {
        console.error('Upload response missing files array:', uploadResponse);
        toast({
          title: "Error",
          description: "Upload response was invalid",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Signature save error:', error);
      toast({
        title: "Error",
        description: "Failed to save signature",
        variant: "destructive"
      });
    }
  };

  const handleClearSignature = (componentId: string, gIdx: number) => {
    const componentKey = `${componentId}-${gIdx}`;
    const canvas = signRefs.current[componentKey];
    if (canvas) {
      canvas.clear();
    }
  };

  // Validation logic - supports both formats
  const runValidation = () => {
    const nErr = { group: {}, checklist: {}, post: {} };

    if (isLegacyFormat) {
      // Legacy format validation
      legacyChecklistData.forEach((g, gIdx) => {
        if (!g.isChecked) {
          nErr.group[`${gIdx}-answer`] = 'This checkpoint group is required';
        }

        // Validate questions when checked
        if (g.isChecked) {
          g.questions.forEach((q, qIdx) => {
            const base = `${gIdx}-${qIdx}`;
            if (!q.selected) {
              nErr.checklist[`${base}-sel`] = 'Select an option';
            } else {
              if ((q.selected === 'No' || q.selected === 'N/A') && !q.remarks) {
                nErr.checklist[`${base}-remarks`] = 'Remarks required';
              }
              if (q.selected === 'No') {
                if (!q.actionToBeTaken) nErr.checklist[`${base}-action`] = 'Action required';
                if (!q.dueDate) nErr.checklist[`${base}-due`] = 'Due date required';
                if (!q.assignee) nErr.checklist[`${base}-assignee`] = 'Assignee required';
              }
            }
          });
        }
      });
    } else {
      // New format validation
      checklistData.forEach((component, gIdx) => {
        // Validate sign components
        if (component.type === 'sign' && component.data.required && !component.data.signature) {
          nErr.checklist[`${gIdx}-signature`] = 'Digital signature is required';
        }

        // Validate date components
        if (component.type === 'date' && component.data.required && !component.data.selectedDate) {
          nErr.checklist[`${gIdx}-date`] = 'Date selection is required';
        }

        // Validate text input components
        if (component.type === 'text-input' && component.data.required && !component.data.textValue?.trim()) {
          nErr.checklist[`${gIdx}-text-input`] = 'Text input is required';
        }

        // Validate individual checkpoint components
        if (component.type === 'checkpoint') {
          if (component.data.required && !component.data.selected) {
            nErr.checklist[`${gIdx}-checkpoint-sel`] = 'Please select an option';
          } else if (component.data.selected) {
            if ((component.data.selected === 'No' || component.data.selected === 'N/A') && !component.data.remarks) {
              nErr.checklist[`${gIdx}-checkpoint-remarks`] = 'Remarks are required';
            }
            if (component.data.selected === 'No') {
              if (!component.data.actionToBeTaken) nErr.checklist[`${gIdx}-checkpoint-action`] = 'Action to be taken is required';
              if (!component.data.dueDate) nErr.checklist[`${gIdx}-checkpoint-due`] = 'Due date is required';
              if (!component.data.assignee) nErr.checklist[`${gIdx}-checkpoint-assignee`] = 'Assignee is required';
            }
          }
        }

        // Validate checkpoint-group components
        if (component.type === 'checkpoint-group') {
          if (component.data.required && !component.data.isChecked) {
            nErr.checklist[`${gIdx}-group-answer`] = 'This checkpoint group is required';
          }

          if (component.data.isChecked) {
            // Validate individual checkpoints within the group
            const checkpoints = component.data.checkpoints || [];
            checkpoints.forEach((checkpoint, qIdx) => {
              const base = `${gIdx}-${qIdx}`;
              if (!checkpoint.selected) {
                nErr.checklist[`${base}-sel`] = 'Select an option';
              } else {
                if ((checkpoint.selected === 'No' || checkpoint.selected === 'N/A') && !checkpoint.remarks) {
                  nErr.checklist[`${base}-remarks`] = 'Remarks required';
                }
                if (checkpoint.selected === 'No') {
                  if (!checkpoint.actionToBeTaken) nErr.checklist[`${base}-action`] = 'Action required';
                  if (!checkpoint.dueDate) nErr.checklist[`${base}-due`] = 'Due date required';
                  if (!checkpoint.assignee) nErr.checklist[`${base}-assignee`] = 'Assignee required';
                }
              }
            });
          }
        }
      });
    }

    // Validate post actions
    postActions.forEach((a, idx) => {
      if (!a.actionToBeTaken) nErr.post[`${idx}-action`] = 'Action required';
      if (!a.dueDate) nErr.post[`${idx}-due`] = 'Due date required';
      if (!a.assignee) nErr.post[`${idx}-assignee`] = 'Assignee required';
    });

    setErrorMap(nErr);
    const hasErrors =
      Object.keys(nErr.group).length +
      Object.keys(nErr.checklist).length +
      Object.keys(nErr.post).length > 0;
    return !hasErrors;
  };

  // Submit handler - supports both formats
  const handleSubmit = async () => {
    if (!runValidation()) {
      toast({
        title: "Validation Error",
        description: "Please fix the highlighted errors before submitting.",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const payload = {
        checklist: isLegacyFormat ? legacyChecklistData : checklistData,
        postActions: postActions
      };
      console.log(payload)


      await apiService.patch(SUBMIT_CHECKLIST_INSPECTION(actionData?.id), payload);

      toast({
        title: "Success",
        description: "Inspection submitted successfully",
      });
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error submitting inspection:", error);
      toast({
        title: "Error",
        description: "Failed to submit inspection. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-semibold">Conduct Inspection</h4>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm text-muted-foreground">
                  #{inspectionDetails?.maskId || 'N/A'}
                </span>
                <Badge variant="secondary">
                  {inspectionDetails?.status || 'Unknown'}
                </Badge>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Inspection Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Inspection Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* First Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Inspection Category</p>
                  <p className="text-sm text-gray-900">{inspectionDetails?.inspectionCategory || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Scheduled Date</p>
                  <p className="text-sm text-gray-900">
                    {inspectionDetails?.scheduledDate
                      ? format(new Date(inspectionDetails.scheduledDate), 'dd-MM-yyyy')
                      : "N/A"}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Due Date</p>
                  <p className="text-sm text-gray-900">
                    {inspectionDetails?.dueDate
                      ? format(new Date(inspectionDetails.dueDate), 'dd-MM-yyyy')
                      : "N/A"}
                  </p>
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Inspector</p>
                  <p className="text-sm text-gray-900">{inspectionDetails?.inspector?.firstName || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Checklist</p>
                  <p className="text-sm text-gray-900">{inspectionDetails?.checklist?.name || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Checklist Version</p>
                  <p className="text-sm text-gray-900">{inspectionDetails?.checklistVersion || "N/A"}</p>
                </div>
              </div>

              {/* Third Row - Location */}
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">Location</p>
                  <p className="text-sm text-gray-900">
                    {[
                      inspectionDetails?.locationOne,
                      inspectionDetails?.locationTwo,
                      inspectionDetails?.locationThree,
                      inspectionDetails?.locationFour,
                      inspectionDetails?.locationFive,
                      inspectionDetails?.locationSix
                    ]
                      .filter(location => location?.name)
                      .map(location => location.name)
                      .join(' > ') || "N/A"}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Checklist Components - New Format */}
          {!isLegacyFormat && checklistData.map((component, gIdx) => {
            // Handle Header Component
            if (component.type === 'header') {
              return (
                <div key={component.id} className="mb-6">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-1 h-8 bg-gradient-to-b from-blue-500 to-blue-600 rounded-full"></div>
                    <h2 className="text-xl font-bold text-gray-800">{component.data.text}</h2>
                  </div>
                  <div className="h-px bg-gradient-to-r from-blue-500 via-blue-300 to-transparent"></div>
                </div>
              );
            }

            // Handle Section Header Component
            if (component.type === 'section-header') {
              return (
                <div key={component.id} className="mb-4">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="w-6 h-6 bg-indigo-500 rounded-md flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                    <h3 className="text-lg font-semibold text-indigo-700">{component.data.text}</h3>
                    {component.data.required && <span className="text-red-500 text-sm">*</span>}
                  </div>
                  <div className="h-px bg-indigo-200 ml-8"></div>
                </div>
              );
            }

            // Handle Text Body Component
            if (component.type === 'text-body') {
              return (
                <Card key={component.id} className="mb-4 border-l-4 border-l-gray-400">
                  <CardContent className="pt-4">
                    <div className="prose prose-sm max-w-none">
                      <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                        {component.data.content}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            // Handle Individual Checkpoint Component
            if (component.type === 'checkpoint') {
              const checkpointSelError = errorMap.checklist[`${gIdx}-checkpoint-sel`];
              const checkpointRemarksError = errorMap.checklist[`${gIdx}-checkpoint-remarks`];
              const checkpointActionError = errorMap.checklist[`${gIdx}-checkpoint-action`];
              const checkpointDueError = errorMap.checklist[`${gIdx}-checkpoint-due`];
              const checkpointAssigneeError = errorMap.checklist[`${gIdx}-checkpoint-assignee`];
              const hasCheckpointError = checkpointSelError || checkpointRemarksError || checkpointActionError || checkpointDueError || checkpointAssigneeError;

              return (
                <Card key={component.id} className={`mb-4 border-l-4 ${hasCheckpointError ? 'border-l-red-500' : 'border-l-green-400'}`}>
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <div className={`w-6 h-6 ${hasCheckpointError ? 'bg-red-100' : 'bg-green-100'} rounded-full flex items-center justify-center mt-1`}>
                        <div className={`w-2 h-2 ${hasCheckpointError ? 'bg-red-500' : 'bg-green-500'} rounded-full`}></div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-3">
                          <h4 className="font-medium text-gray-800">{component.data.text}</h4>
                          {component.data.required && <span className="text-red-500 text-sm">*</span>}
                        </div>

                        {/* Validation Error Display for Selection */}
                        {checkpointSelError && (
                          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-600 flex items-center gap-2">
                              <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">!</span>
                              </span>
                              {checkpointSelError}
                            </p>
                          </div>
                        )}

                        {/* Response Options */}
                        <div className="flex gap-2 mb-3">
                          {['Yes', 'No', 'N/A'].map((opt) => (
                            <Button
                              key={opt}
                              variant={component.data.selected === opt ? "default" : "outline"}
                              size="sm"
                              onClick={() => {
                                const updated = [...checklistData];
                                updated[gIdx].data.selected = opt;
                                if (opt !== 'No') {
                                  updated[gIdx].data.actionToBeTaken = '';
                                  updated[gIdx].data.dueDate = null;
                                  updated[gIdx].data.assignee = '';
                                }
                                setChecklistData(updated);
                              }}
                              className={opt === 'Yes' ? 'hover:bg-green-600' : opt === 'No' ? 'hover:bg-red-600' : 'hover:bg-gray-600'}
                            >
                              {opt}
                            </Button>
                          ))}
                        </div>

                        {/* Remarks for No/N/A */}
                        {(component.data.selected === 'No' || component.data.selected === 'N/A') && (
                          <div className="space-y-2 mb-3">
                            <Label>Remarks</Label>
                            <Textarea
                              value={component.data.remarks || ''}
                              onChange={(e) => {
                                const updated = [...checklistData];
                                updated[gIdx].data.remarks = e.target.value;
                                setChecklistData(updated);
                              }}
                              placeholder="Enter remarks..."
                              className={`border-2 ${checkpointRemarksError ? 'border-red-500' : ''}`}
                            />
                            {checkpointRemarksError && (
                              <p className="text-sm text-red-600 flex items-center gap-2">
                                <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                  <span className="text-white text-xs">!</span>
                                </span>
                                {checkpointRemarksError}
                              </p>
                            )}
                          </div>
                        )}

                        {/* Action fields for No */}
                        {component.data.selected === 'No' && (
                          <div className="bg-red-50 border border-red-200 rounded-lg p-4 space-y-3">
                            <div className="flex items-center gap-2 text-red-700">
                              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                              <span className="font-medium">Corrective Action Required</span>
                            </div>

                            <div className="space-y-2">
                              <Label>Action to be Taken</Label>
                              <Textarea
                                value={component.data.actionToBeTaken || ''}
                                onChange={(e) => {
                                  const updated = [...checklistData];
                                  updated[gIdx].data.actionToBeTaken = e.target.value;
                                  setChecklistData(updated);
                                }}
                                placeholder="Describe the action required..."
                                className={checkpointActionError ? 'border-red-500' : ''}
                              />
                              {checkpointActionError && (
                                <p className="text-sm text-red-600 flex items-center gap-2">
                                  <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                    <span className="text-white text-xs">!</span>
                                  </span>
                                  {checkpointActionError}
                                </p>
                              )}
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Due Date</Label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        "w-full justify-start text-left font-normal",
                                        !component.data.dueDate && "text-muted-foreground",
                                        checkpointDueError && "border-red-500"
                                      )}
                                    >
                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                      {component.data.dueDate ? format(component.data.dueDate, "PPP") : "Pick a date"}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <Calendar
                                      mode="single"
                                      selected={component.data.dueDate || undefined}
                                      onSelect={(date) => {
                                        const updated = [...checklistData];
                                        updated[gIdx].data.dueDate = date || null;
                                        setChecklistData(updated);
                                      }}
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                                {checkpointDueError && (
                                  <p className="text-sm text-red-600 flex items-center gap-2">
                                    <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                      <span className="text-white text-xs">!</span>
                                    </span>
                                    {checkpointDueError}
                                  </p>
                                )}
                              </div>

                              <div className="space-y-2">
                                <Label>Assignee</Label>
                                <Select
                                  value={component.data.assignee || ''}
                                  onValueChange={(value) => {
                                    const updated = [...checklistData];
                                    updated[gIdx].data.assignee = value;
                                    setChecklistData(updated);
                                  }}
                                >
                                  <SelectTrigger className={checkpointAssigneeError ? 'border-red-500' : ''}>
                                    <SelectValue placeholder="Select assignee" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {assignees.map((assignee) => (
                                      <SelectItem key={assignee.value} value={assignee.value}>
                                        {assignee.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                {checkpointAssigneeError && (
                                  <p className="text-sm text-red-600 flex items-center gap-2">
                                    <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                      <span className="text-white text-xs">!</span>
                                    </span>
                                    {checkpointAssigneeError}
                                  </p>
                                )}
                              </div>
                            </div>
                            <div className="space-y-2 mt-3">
                              <Label>Upload Evidence (Optional)</Label>
                              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-gray-400 transition-colors">
                                <input
                                  type="file"
                                  accept="image/*,video/*,.pdf,.doc,.docx"
                                  onChange={(e) => handleFileUpload(e.target.files, gIdx, 0)}
                                  className="hidden"
                                  id={`checkpoint-file-${gIdx}`}
                                />
                                <label
                                  htmlFor={`checkpoint-file-${gIdx}`}
                                  className="cursor-pointer flex flex-col items-center gap-2"
                                >
                                  <Upload className="h-8 w-8 text-gray-400" />
                                  <span className="text-sm text-gray-600">
                                    Drag & drop or click to upload
                                  </span>
                                </label>
                              </div>

                              {/* Display uploaded files */}
                              {component.data.uploads && component.data.uploads.length > 0 && (
                                <div className="grid grid-cols-3 gap-2 mt-2">
                                  {component.data.uploads.map((file, i) => (
                                    <div key={i} className="relative">
                                      <ImageComponent fileName={file} size="100" name={false} />
                                      <Button
                                        variant="destructive"
                                        size="sm"
                                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                        onClick={() => handleRemoveImage(gIdx, 0, i)}
                                      >
                                        <X className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>

                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            // Handle Date Component
            if (component.type === 'date') {
              const dateError = errorMap.checklist[`${gIdx}-date`];

              return (
                <Card key={component.id} className={`mb-4 border-l-4 ${dateError ? 'border-l-red-500' : 'border-l-purple-400'}`}>
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <div className={`w-6 h-6 ${dateError ? 'bg-red-100' : 'bg-purple-100'} rounded-full flex items-center justify-center mt-1`}>
                        <CalendarIcon className={`h-3 w-3 ${dateError ? 'text-red-600' : 'text-purple-600'}`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-3">
                          <Label className="font-medium text-gray-800">{component.data.label}</Label>
                          {component.data.required && <span className="text-red-500 text-sm">*</span>}
                        </div>

                        {/* Validation Error Display */}
                        {dateError && (
                          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-600 flex items-center gap-2">
                              <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">!</span>
                              </span>
                              {dateError}
                            </p>
                          </div>
                        )}

                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !component.data.selectedDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {component.data.selectedDate ? format(component.data.selectedDate, "PPP") : "Select date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={component.data.selectedDate || undefined}
                              onSelect={(date) => {
                                const updated = [...checklistData];
                                updated[gIdx].data.selectedDate = date || null;
                                setChecklistData(updated);
                              }}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            // Handle Sign Component
            if (component.type === 'sign') {
              const componentKey = `${component.id}-${gIdx}`;
              const isSignaturePadVisible = showSignaturePad[componentKey];
              const signatureError = errorMap.checklist[`${gIdx}-signature`];

              return (
                <Card key={component.id} className={`mb-4 border-l-4 ${signatureError ? 'border-l-red-500' : 'border-l-orange-400'}`}>
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <div className={`w-6 h-6 ${signatureError ? 'bg-red-100' : 'bg-orange-100'} rounded-full flex items-center justify-center mt-1`}>
                        <PenTool className={`h-3 w-3 ${signatureError ? 'text-red-600' : 'text-orange-600'}`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-3">
                          <Label className="font-medium text-gray-800">{component.data.label}</Label>
                          {component.data.required && <span className="text-red-500 text-sm">*</span>}
                        </div>

                        {/* Validation Error Display */}
                        {signatureError && (
                          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-600 flex items-center gap-2">
                              <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">!</span>
                              </span>
                              {signatureError}
                            </p>
                          </div>
                        )}

                        <div className="border-2 border-dashed border-orange-300 rounded-lg p-4 bg-orange-50">
                          {/* Show signature pad only if no signature exists */}
                          {!component.data.signature && !isSignaturePadVisible ? (
                            <div className="text-center">
                              <PenTool className="h-8 w-8 text-orange-400 mx-auto mb-2" />
                              <p className="text-sm text-orange-700 mb-2">Digital Signature Required</p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-orange-300 text-orange-700 hover:bg-orange-100"
                                onClick={() => {
                                  setShowSignaturePad(prev => ({ ...prev, [componentKey]: true }));
                                }}
                              >
                                <PenTool className="h-4 w-4 mr-2" />
                                Add Signature
                              </Button>
                            </div>
                          ) : !component.data.signature && isSignaturePadVisible ? (
                            <div className="space-y-3">
                              <div className="text-center">
                                <p className="text-sm text-orange-700 mb-2">Please sign below</p>
                              </div>

                              {/* Signature Canvas */}
                              <div className="bg-white border-2 border-orange-200 rounded-lg p-3">
                                <div className="w-full" style={{ height: '300px' }}>
                                  <SignatureCanvas
                                    ref={(ref) => {
                                      if (ref) {
                                        signRefs.current[componentKey] = ref;
                                      }
                                    }}
                                    canvasProps={{
                                      width: 600,
                                      height: 300,
                                      className: 'signature-canvas border border-gray-200 rounded',
                                      style: {
                                        width: '100%',
                                        height: '100%',
                                        display: 'block',
                                        touchAction: 'none'
                                      }
                                    }}
                                    backgroundColor="white"
                                    penColor="#000000"
                                    minWidth={0.5}
                                    maxWidth={2.5}
                                    velocityFilterWeight={0.7}
                                    dotSize={1}
                                    throttle={16}
                                  />
                                </div>
                                <p className="text-xs text-gray-500 mt-2 text-center">
                                  Sign above using your mouse, finger, or stylus
                                </p>
                              </div>

                              {/* Signature Controls */}
                              <div className="flex gap-2 justify-center">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleClearSignature(component.id, gIdx)}
                                  className="border-gray-300"
                                >
                                  Clear
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleSaveSignature(component.id, gIdx)}
                                  className="border-green-300 text-green-700 hover:bg-green-100"
                                >
                                  Save Signature
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setShowSignaturePad(prev => ({ ...prev, [componentKey]: false }));
                                    handleClearSignature(component.id, gIdx);
                                  }}
                                  className="border-gray-300"
                                >
                                  Cancel
                                </Button>
                              </div>
                            </div>
                          ) : (
                            /* Display saved signature (removable and replaceable) */
                            <div className="text-center">
                              <p className="text-xs text-gray-600 mb-3">Digital Signature Captured</p>
                              <div className="relative inline-block">
                                <div className="border-2 border-green-200 rounded-lg p-3 bg-green-50">
                                  <div style={{ width: '400px' }}>
                                    <ImageComponent fileName={component.data.signature} size="300" name={false} />
                                  </div>
                                </div>
                                {/* Remove signature button */}
                                <Button
                                  variant="destructive"
                                  size="sm"
                                  className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                  onClick={() => {
                                    const updated = [...checklistData];
                                    updated[gIdx].data.signature = undefined;
                                    setChecklistData(updated);
                                    toast({
                                      title: "Success",
                                      description: "Signature removed. You can sign again if needed.",
                                    });
                                  }}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>
                              <div className="flex items-center justify-center gap-2 mt-3">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-sm text-green-700 font-medium">Signature Complete</span>
                              </div>
                              <p className="text-xs text-gray-500 mt-2">Click X to remove and sign again</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            // Handle Text Input Component
            if (component.type === 'text-input') {
              const textInputError = errorMap.checklist[`${gIdx}-text-input`];

              return (
                <Card key={component.id} className={`mb-4 border-l-4 ${textInputError ? 'border-l-red-500' : 'border-l-blue-400'}`}>
                  <CardContent className="pt-4">
                    <div className="flex items-start gap-3">
                      <div className={`w-6 h-6 ${textInputError ? 'bg-red-100' : 'bg-blue-100'} rounded-full flex items-center justify-center mt-1`}>
                        <TextCursor className={`h-3 w-3 ${textInputError ? 'text-red-600' : 'text-blue-600'}`} />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-3">
                          <Label className="font-medium text-gray-800">{component.data.label}</Label>
                          {component.data.required && <span className="text-red-500 text-sm">*</span>}
                        </div>

                        {/* Validation Error Display */}
                        {textInputError && (
                          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-sm text-red-600 flex items-center gap-2">
                              <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                <span className="text-white text-xs">!</span>
                              </span>
                              {textInputError}
                            </p>
                          </div>
                        )}

                        <Input
                          value={component.data.textValue || ''}
                          onChange={(e) => {
                            const updated = [...checklistData];
                            updated[gIdx].data.textValue = e.target.value;
                            setChecklistData(updated);
                          }}
                          placeholder={component.data.placeholder || "Enter text here..."}
                          className={cn(
                            "w-full",
                            textInputError && "border-red-500"
                          )}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            }

            // Handle Checkpoint Group Component
            if (component.type === 'checkpoint-group') {
              const groupAnswerError = errorMap.checklist[`${gIdx}-group-answer`];
              const hasGroupError = groupAnswerError;

              return (
                <Card key={component.id} className={`mb-4 border-l-4 ${hasGroupError ? 'border-l-red-500' : 'border-l-blue-500'}`}>
                  <CardHeader>
                    <CardTitle className="text-base flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 ${hasGroupError ? 'bg-red-500' : 'bg-blue-500'} text-white rounded-full flex items-center justify-center text-sm font-bold`}>
                          {component.position + 1}
                        </div>
                        <div className="flex items-center gap-3">
                          <Checkbox
                            id={`group-${gIdx}`}
                            checked={component.data.isChecked || false}
                            onCheckedChange={(checked) => {
                              const updated = [...checklistData];
                              updated[gIdx].data.isChecked = checked as boolean;
                              if (!checked) {
                                // Clear checkpoints if unchecked
                                if (updated[gIdx].data.checkpoints) {
                                  updated[gIdx].data.checkpoints.forEach((checkpoint) => {
                                    checkpoint.selected = '';
                                    checkpoint.remarks = '';
                                    checkpoint.actionToBeTaken = '';
                                    checkpoint.dueDate = null;
                                    checkpoint.assignee = '';
                                    checkpoint.uploads = [];
                                  });
                                }
                              }
                              setChecklistData(updated);
                            }}
                          />
                          <label htmlFor={`group-${gIdx}`} className="cursor-pointer">
                            {component.data.title}
                            {component.data.required && <span className="text-red-500 ml-1">*</span>}
                          </label>
                        </div>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* Validation Error Display for Group Answer */}
                    {groupAnswerError && (
                      <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
                        <p className="text-sm text-red-600 flex items-center gap-2">
                          <span className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                            <span className="text-white text-xs">!</span>
                          </span>
                          {groupAnswerError}
                        </p>
                      </div>
                    )}

                    {/* Checkpoints when checked */}
                    {component.data.isChecked && (
                      <div className="space-y-4">
                        {component.data.checkpoints?.map((checkpoint, qIdx) => (
                          <div key={checkpoint.id} className="border rounded-lg p-4 space-y-3">
                            <div className="font-medium">{checkpoint.text}</div>

                            {/* Question options */}
                            <div className="flex gap-2">
                              {['Yes', 'No', 'N/A'].map((opt) => (
                                <Button
                                  key={opt}
                                  variant={checkpoint.selected === opt ? "default" : "outline"}
                                  size="sm"
                                  onClick={() => {
                                    const updated = [...checklistData];
                                    const checkpoints = updated[gIdx].data.checkpoints || [];
                                    if (checkpoints[qIdx]) {
                                      checkpoints[qIdx].selected = opt;
                                      if (opt !== 'No') {
                                        checkpoints[qIdx].actionToBeTaken = '';
                                        checkpoints[qIdx].dueDate = null;
                                        checkpoints[qIdx].assignee = '';
                                      }
                                      setChecklistData(updated);
                                    }
                                  }}
                                  className={opt === 'Yes' ? 'hover:bg-green-600' : opt === 'No' ? 'hover:bg-red-600' : 'hover:bg-gray-600'}
                                >
                                  {opt}
                                </Button>
                              ))}
                            </div>

                            {/* Remarks for No/N/A */}
                            {(checkpoint.selected === 'No' || checkpoint.selected === 'N/A') && (
                              <div className="space-y-2">
                                <Label>Remarks</Label>
                                <Textarea
                                  value={checkpoint.remarks || ''}
                                  onChange={(e) => {
                                    const updated = [...checklistData];
                                    const checkpoints = updated[gIdx].data.checkpoints || [];
                                    if (checkpoints[qIdx]) {
                                      checkpoints[qIdx].remarks = e.target.value;
                                      setChecklistData(updated);
                                    }
                                  }}
                                  placeholder="Enter remarks..."
                                />
                              </div>
                            )}

                            {/* Action fields for No */}
                            {checkpoint.selected === 'No' && (
                              <div className="bg-red-50 border border-red-200 rounded-lg p-4 space-y-3">
                                <div className="flex items-center gap-2 text-red-700">
                                  <div className="w-4 h-4 bg-red-500 rounded-full"></div>
                                  <span className="font-medium">Corrective Action Required</span>
                                </div>

                                <div className="space-y-2">
                                  <Label>Action to be Taken</Label>
                                  <Textarea
                                    value={checkpoint.actionToBeTaken || ''}
                                    onChange={(e) => {
                                      const updated = [...checklistData];
                                      const checkpoints = updated[gIdx].data.checkpoints || [];
                                      if (checkpoints[qIdx]) {
                                        checkpoints[qIdx].actionToBeTaken = e.target.value;
                                        setChecklistData(updated);
                                      }
                                    }}
                                    placeholder="Describe the action required..."
                                  />
                                </div>

                                <div className="grid grid-cols-2 gap-4">
                                  <div className="space-y-2">
                                    <Label>Due Date</Label>
                                    <Popover>
                                      <PopoverTrigger asChild>
                                        <Button
                                          variant="outline"
                                          className={cn(
                                            "w-full justify-start text-left font-normal",
                                            !checkpoint.dueDate && "text-muted-foreground"
                                          )}
                                        >
                                          <CalendarIcon className="mr-2 h-4 w-4" />
                                          {checkpoint.dueDate ? format(checkpoint.dueDate, "PPP") : "Pick a date"}
                                        </Button>
                                      </PopoverTrigger>
                                      <PopoverContent className="w-auto p-0">
                                        <Calendar
                                          mode="single"
                                          selected={checkpoint.dueDate || undefined}
                                          onSelect={(date) => {
                                            const updated = [...checklistData];
                                            const checkpoints = updated[gIdx].data.checkpoints || [];
                                            if (checkpoints[qIdx]) {
                                              checkpoints[qIdx].dueDate = date || null;
                                              setChecklistData(updated);
                                            }
                                          }}
                                          initialFocus
                                        />
                                      </PopoverContent>
                                    </Popover>
                                  </div>

                                  <div className="space-y-2">
                                    <Label>Assignee</Label>
                                    <Select
                                      value={checkpoint.assignee || ''}
                                      onValueChange={(value) => {
                                        const updated = [...checklistData];
                                        const checkpoints = updated[gIdx].data.checkpoints || [];
                                        if (checkpoints[qIdx]) {
                                          checkpoints[qIdx].assignee = value;
                                          setChecklistData(updated);
                                        }
                                      }}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select assignee" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {assignees.map((assignee) => (
                                          <SelectItem key={assignee.value} value={assignee.value}>
                                            {assignee.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>

                                {/* File upload for No responses */}
                                <div className="space-y-2">
                                  <Label>Upload Evidence (Optional)</Label>
                                  <div className="border-2 border-dashed border-red-300 rounded-lg p-4 text-center hover:border-red-400 transition-colors bg-red-25">
                                    <input
                                      type="file"
                                      accept="image/*,video/*,.pdf,.doc,.docx"
                                      onChange={(e) => handleFileUpload(e.target.files, gIdx, qIdx)}
                                      className="hidden"
                                      id={`file-${gIdx}-${qIdx}`}
                                    />
                                    <label
                                      htmlFor={`file-${gIdx}-${qIdx}`}
                                      className="cursor-pointer flex flex-col items-center gap-2"
                                    >
                                      <Upload className="h-8 w-8 text-red-400" />
                                      <span className="text-sm text-red-600">
                                        Drag & drop evidence or click to upload
                                      </span>
                                    </label>
                                  </div>

                                  {/* Display uploaded files */}
                                  {checkpoint.uploads && checkpoint.uploads.length > 0 && (
                                    <div className="grid grid-cols-3 gap-2 mt-2">
                                      {checkpoint.uploads.map((img, i) => (
                                        <div key={i} className="relative">
                                          <ImageComponent fileName={img} size="100" name={false} />
                                          <Button
                                            variant="destructive"
                                            size="sm"
                                            className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                            onClick={() => handleRemoveImage(gIdx, qIdx, i)}
                                          >
                                            <X className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}


                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            }

            // Handle other component types if needed
            return (
              <Card key={component.id} className="mb-4">
                <CardHeader>
                  <CardTitle className="text-base">
                    Unknown Component Type: {component.type}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-500">Component type "{component.type}" is not yet supported.</p>
                </CardContent>
              </Card>
            );
          })}

          {/* Legacy Checklist Groups */}
          {isLegacyFormat && legacyChecklistData.map((group, gIdx) => (
            <Card key={gIdx}>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-3">
                  <Checkbox
                    id={`legacy-group-${gIdx}`}
                    checked={group.isChecked || false}
                    onCheckedChange={(checked) => {
                      const updated = [...legacyChecklistData];
                      updated[gIdx].isChecked = checked as boolean;
                      if (!checked) {
                        // Clear questions if unchecked
                        updated[gIdx].questions.forEach((q) => {
                          q.selected = '';
                          q.remarks = '';
                          q.actionToBeTaken = '';
                          q.dueDate = null;
                          q.assignee = '';
                          q.uploads = [];
                        });
                      }
                      setLegacyChecklistData(updated);
                    }}
                  />
                  <label htmlFor={`legacy-group-${gIdx}`} className="cursor-pointer">
                    {group.label}
                  </label>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Questions when checked */}
                {group.isChecked && (
                  <div className="space-y-4">
                    {group.questions.map((q, qIdx) => (
                      <div key={qIdx} className="border rounded-lg p-4 space-y-3">
                        <div className="font-medium">{q.label}</div>

                        {/* Question options */}
                        <div className="flex gap-2">
                          {['Yes', 'No', 'N/A'].map((opt) => (
                            <Button
                              key={opt}
                              variant={q.selected === opt ? "default" : "outline"}
                              size="sm"
                              onClick={() => {
                                const updated = [...legacyChecklistData];
                                updated[gIdx].questions[qIdx].selected = opt;
                                if (opt !== 'No') {
                                  updated[gIdx].questions[qIdx].actionToBeTaken = '';
                                  updated[gIdx].questions[qIdx].dueDate = null;
                                  updated[gIdx].questions[qIdx].assignee = '';
                                }
                                setLegacyChecklistData(updated);
                              }}
                            >
                              {opt}
                            </Button>
                          ))}
                        </div>

                        {/* Remarks for No/N/A */}
                        {(q.selected === 'No' || q.selected === 'N/A') && (
                          <div className="space-y-2">
                            <Label>Remarks</Label>
                            <Textarea
                              value={q.remarks}
                              onChange={(e) => {
                                const updated = [...legacyChecklistData];
                                updated[gIdx].questions[qIdx].remarks = e.target.value;
                                setLegacyChecklistData(updated);
                              }}
                              placeholder="Enter remarks..."
                            />
                          </div>
                        )}

                        {/* Action fields for No */}
                        {q.selected === 'No' && (
                          <div className="space-y-3">
                            <div className="space-y-2">
                              <Label>Action to be Taken</Label>
                              <Textarea
                                value={q.actionToBeTaken}
                                onChange={(e) => {
                                  const updated = [...legacyChecklistData];
                                  updated[gIdx].questions[qIdx].actionToBeTaken = e.target.value;
                                  setLegacyChecklistData(updated);
                                }}
                                placeholder="Describe the action required..."
                              />
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label>Due Date</Label>
                                <Popover>
                                  <PopoverTrigger asChild>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        "w-full justify-start text-left font-normal",
                                        !q.dueDate && "text-muted-foreground"
                                      )}
                                    >
                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                      {q.dueDate ? format(q.dueDate, "PPP") : "Pick a date"}
                                    </Button>
                                  </PopoverTrigger>
                                  <PopoverContent className="w-auto p-0">
                                    <Calendar
                                      mode="single"
                                      selected={q.dueDate || undefined}
                                      onSelect={(date) => {
                                        const updated = [...legacyChecklistData];
                                        updated[gIdx].questions[qIdx].dueDate = date || null;
                                        setLegacyChecklistData(updated);
                                      }}
                                      initialFocus
                                    />
                                  </PopoverContent>
                                </Popover>
                              </div>

                              <div className="space-y-2">
                                <Label>Assignee</Label>
                                <Select
                                  value={q.assignee}
                                  onValueChange={(value) => {
                                    const updated = [...legacyChecklistData];
                                    updated[gIdx].questions[qIdx].assignee = value;
                                    setLegacyChecklistData(updated);
                                  }}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select assignee" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {assignees.map((assignee) => (
                                      <SelectItem key={assignee.value} value={assignee.value}>
                                        {assignee.label}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>

                            {/* File upload for No responses */}
                            <div className="space-y-2">
                              <Label>Upload Media (Optional)</Label>
                              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                                <input
                                  type="file"
                                  accept="image/*,video/*,.pdf,.doc,.docx"
                                  onChange={(e) => handleFileUpload(e.target.files, gIdx, qIdx)}
                                  className="hidden"
                                  id={`file-${gIdx}-${qIdx}`}
                                />
                                <label
                                  htmlFor={`file-${gIdx}-${qIdx}`}
                                  className="cursor-pointer flex flex-col items-center gap-2"
                                >
                                  <Upload className="h-8 w-8 text-gray-400" />
                                  <span className="text-sm text-gray-600">
                                    Drag & drop or click to upload
                                  </span>
                                </label>
                              </div>

                              {/* Display uploaded files */}
                              {q.uploads && q.uploads.length > 0 && (
                                <div className="grid grid-cols-3 gap-2 mt-2">
                                  {q.uploads.map((img, i) => (
                                    <div key={i} className="relative">
                                      <ImageComponent fileName={img} size="100" name={false} />
                                      <Button
                                        variant="destructive"
                                        size="sm"
                                        className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                        onClick={() => handleRemoveImage(gIdx, qIdx, i)}
                                      >
                                        <X className="h-3 w-3" />
                                      </Button>
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          </div>
                        )}


                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
          {/* Post Actions Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base flex items-center justify-between">
                Post Actions
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPostActions(!showPostActions)}
                >
                  {showPostActions ? 'Hide' : 'Show'} Post Actions
                </Button>
              </CardTitle>
            </CardHeader>
            {showPostActions && (
              <CardContent className="space-y-4">
                {postActions.map((action, idx) => (
                  <div key={idx} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Post Action {idx + 1}</h4>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleRemovePostAction(idx)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <Label>Action to be Taken</Label>
                      <Textarea
                        value={action.actionToBeTaken}
                        onChange={(e) => handlePostActionChange(idx, 'actionToBeTaken', e.target.value)}
                        placeholder="Describe the post action..."
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Due Date</Label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !action.dueDate && "text-muted-foreground"
                              )}
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {action.dueDate ? format(action.dueDate, "PPP") : "Pick a date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0">
                            <Calendar
                              mode="single"
                              selected={action.dueDate || undefined}
                              onSelect={(date) => handlePostActionChange(idx, 'dueDate', date || null)}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div className="space-y-2">
                        <Label>Assignee</Label>
                        <Select
                          value={action.assignee}
                          onValueChange={(value) => handlePostActionChange(idx, 'assignee', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select assignee" />
                          </SelectTrigger>
                          <SelectContent>
                            {assignees.map((assignee) => (
                              <SelectItem key={assignee.value} value={assignee.value}>
                                {assignee.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Post Action File Upload */}
                    <div className="space-y-2">
                      <Label>Upload Files (Optional)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          type="file"
                          accept="image/*,video/*,.pdf,.doc,.docx"
                          onChange={(e) => handlePostFileUpload(e.target.files, idx)}
                          className="hidden"
                          id={`post-file-${idx}`}
                        />
                        <label
                          htmlFor={`post-file-${idx}`}
                          className="cursor-pointer flex flex-col items-center gap-2"
                        >
                          <Upload className="h-8 w-8 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            Drag & drop or click to upload
                          </span>
                        </label>
                      </div>

                      {/* Display uploaded files */}
                      {action.uploads && action.uploads.length > 0 && (
                        <div className="grid grid-cols-3 gap-2 mt-2">
                          {action.uploads.map((file, i) => (
                            <div key={i} className="relative">
                              <ImageComponent fileName={file} size="100" name={false} />
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                onClick={() => handleRemovePostImage(idx, i)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                <Button
                  variant="outline"
                  onClick={addNewPostAction}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Post Action
                </Button>
              </CardContent>
            )}
          </Card>


        </div>

        {/* Footer Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Submitting...
              </>
            ) : (
              'Submit Inspection'
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ConductInspectionModal;