import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Edit, Trash2, GripVertical } from "lucide-react";
import EditableComponentRenderer from "./EditableComponentRenderer";

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface DocumentComponentRendererProps {
  item: DocumentComponent;
  index: number;
  onRemove: (id: string) => void;
  onUpdate: (id: string, content: any) => void;
  onReorder?: (sourceIndex: number, targetIndex: number) => void;
  isDraggable?: boolean;
}

const DocumentComponentRenderer: React.FC<DocumentComponentRendererProps> = ({
  item,
  index,
  onRemove,
  onUpdate,
  onReorder,
  isDraggable = false,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  // Drag and drop handlers for reordering
  const handleDragStart = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;

    e.dataTransfer.setData("componentId", item.id);
    e.dataTransfer.effectAllowed = "move";
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;

    e.preventDefault();
    e.dataTransfer.dropEffect = "move";
    setIsDragOver(true);
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    if (!isDraggable || !onReorder) return;

    e.preventDefault();
    setIsDragOver(false);

    const sourceId = e.dataTransfer.getData("componentId");
    if (sourceId && sourceId !== item.id) {
      // This will be handled by the DropZone components instead
      // ComponentRenderer drop is mainly for visual feedback
      return;
    }
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = (content: any) => {
    onUpdate(item.id, content);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setIsEditing(false);
  };

  const handleRemove = () => {
    onRemove(item.id);
  };

  const getComponentName = (type: string) => {
    const names: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image/File Upload',
      'file-attachment': 'File Attachment',
      'link': 'Link'
    };
    return names[type] || type;
  };

  return (
    <Card
      className={`p-5 fade-in shadow-sm border-slate-200 dark:border-slate-700 ${isDraggable ? 'cursor-move' : ''}
        ${isDragging ? 'opacity-50 rotate-1 scale-[0.98]' : ''}
        ${isDragOver ? 'border-primary border-2 bg-primary/5 dark:bg-primary/10 scale-[1.01]' : ''}
        transition-all duration-200 hover:shadow-md
      `}
      draggable={isDraggable}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="flex justify-between items-start mb-3">
        <div className="flex items-center">
          {isDraggable && (
            <div className="text-muted-foreground mr-2 cursor-grab p-1 hover:bg-slate-100 dark:hover:bg-slate-700 rounded-md transition-colors">
              <GripVertical className="h-5 w-5" />
            </div>
          )}
          <div className="text-sm font-medium text-slate-600 dark:text-slate-400">
            {getComponentName(item.type)}
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEdit}
            className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-900/20"
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRemove}
            className="h-8 w-8 p-0 hover:bg-red-100 dark:hover:bg-red-900/20 text-red-600 dark:text-red-400"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <EditableComponentRenderer
        component={item}
        onUpdate={handleSave}
        onRemove={handleRemove}
        isEditing={isEditing}
        onSave={handleSave}
        onCancel={handleCancel}
      />
    </Card>
  );
};

export default DocumentComponentRenderer;
