import React, { useEffect, useState } from 'react';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent } from '@/components/ui/card';
import apiService from '@/services/apiService';

interface HazardItem {
  id: string;
  name: string;
  image?: string;
  hazardName?: string;
}

interface HazardItemProps {
  hazard: HazardItem;
  selectedHazards: HazardItem[];
  onClickHazards: (hazard: HazardItem) => void;
}

const HazardItem: React.FC<HazardItemProps> = ({ hazard, selectedHazards, onClickHazards }) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const isSelected = selectedHazards.some(h => h.id === hazard.id);

  useEffect(() => {
    const fetchImage = async () => {
      if (hazard.image && localStorage.getItem('SELECTED_INDUSTRIES')) {
        try {
          const presignedRes = await apiService.get(`/files/presigned-url/az-risk/${hazard.image}`);
          if (presignedRes) {
            setImageUrl(presignedRes);
          }
        } catch (error) {
          console.error(`Failed to fetch image for ${hazard.name}`, error);
          // Fallback to default image URL
          setImageUrl(`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`);
        }
      } else if (hazard.image) {
        setImageUrl(`https://mpower-s3.s3-ap-southeast-1.amazonaws.com/uploads/hazards/${hazard.image}`);
      }
    };

    fetchImage();
  }, [hazard]);

  return (
    <Card 
      className={`cursor-pointer transition-all hover:shadow-md ${
        isSelected ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-gray-50'
      }`}
      onClick={() => onClickHazards(hazard)}
    >
      <CardContent className="p-3">
        <div className="flex items-center space-x-3">
          <Checkbox 
            checked={isSelected}
            readOnly
            className="pointer-events-none"
          />
          
          {imageUrl && (
            <div className="flex-shrink-0">
              <img
                src={imageUrl}
                alt={hazard.name}
                className="w-10 h-10 object-contain"
                onError={(e) => {
                  // Hide image if it fails to load
                  (e.target as HTMLImageElement).style.display = 'none';
                }}
              />
            </div>
          )}
          
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {hazard.name}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default HazardItem;
