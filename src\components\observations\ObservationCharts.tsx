import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Chart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { format, subDays, differenceInDays, parseISO, isAfter, isBefore, startOfMonth, endOfMonth, eachDayOfInterval, startOfWeek, addWeeks, getWeek } from 'date-fns';
import { Observation } from '@/types/observation';
import {
  ClipboardList,
  CheckCircle,
  AlertCircle,
  Clock,
  Users,
  MapPin,
  BarChart2,
  Pie<PERSON>hart as PieChartIcon,
  TrendingUp
} from 'lucide-react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

// Mock data for observations
export const generateMockObservations = (count: number): Observation[] => {
  const statuses = ['New', 'Open', 'In Progress', 'Pending Review', 'Closed'];
  const categories = ['Environment', 'HSE', 'Social'];
  const types = ['Safe', 'Unsafe'];
  const actionConditions = ['Action', 'Condition'];
  const users = ['John Smith', 'Emma Johnson', 'Michael Brown', 'Sarah Wilson', 'Robert Davis'];
  const locations = [
    'Boston, Floor 1',
    'New York, Floor 2',
    'Chicago, Floor 3',
    'London, Reception',
    'Sydney, Floor 1',
    'Munich, Conference Room'
  ];

  const observations: Observation[] = [];

  for (let i = 0; i < count; i++) {
    const reportedDate = subDays(new Date(), Math.floor(Math.random() * 90)); // Random date within last 90 days
    const dueDate = Math.random() > 0.3 ? new Date(reportedDate.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000) : null;
    const type = types[Math.floor(Math.random() * types.length)];
    const rectifiedOnSpot = Math.random() > 0.7 ? 'yes' : 'no';
    const needsReviewer = Math.random() > 0.6 ? 'yes' : 'no';

    const location = locations[Math.floor(Math.random() * locations.length)];
    const [site, level] = location.split(', ');

    observations.push({
      id: `OBS-${1000 + i}`,
      location,
      fullLocation: {
        country: 'United States',
        region: 'Northeast',
        site,
        level
      },
      category: categories[Math.floor(Math.random() * categories.length)],
      type,
      actionCondition: actionConditions[Math.floor(Math.random() * actionConditions.length)],
      reportedBy: users[Math.floor(Math.random() * users.length)],
      reportedDate,
      actionAssignee: Math.random() > 0.3 ? users[Math.floor(Math.random() * users.length)] : null,
      reviewedBy: Math.random() > 0.7 ? users[Math.floor(Math.random() * users.length)] : null,
      status: type === 'Safe' || rectifiedOnSpot === 'yes' ? 'Closed' :
              needsReviewer === 'yes' ? 'Pending Review' :
              Math.random() > 0.5 ? 'Open' : statuses[Math.floor(Math.random() * 3)],
      description: `Mock observation ${i + 1}`,
      attachments: [],
      dueDate,
      rectifiedOnSpot,
      actionTaken: rectifiedOnSpot === 'yes' ? 'Issue was fixed immediately' : null,
      needsReviewer,
      actionToBeTaken: needsReviewer === 'yes' ? 'Needs further review' : null
    });
  }

  return observations;
};

// Generate 100 mock observations
const mockObservations = generateMockObservations(100);

// Helper function to get observations by time period
const getObservationsByTimePeriod = (observations: Observation[], days: number) => {
  const cutoffDate = subDays(new Date(), days);
  return observations.filter(obs => isAfter(new Date(obs.reportedDate), cutoffDate));
};

// Helper function to group observations by date (kept for reference)
// Not used in the current implementation as we're using weekly grouping instead
/*
const groupObservationsByDate = (observations: Observation[]) => {
  const grouped = observations.reduce((acc, obs) => {
    const dateStr = format(new Date(obs.reportedDate), 'yyyy-MM-dd');
    if (!acc[dateStr]) {
      acc[dateStr] = [];
    }
    acc[dateStr].push(obs);
    return acc;
  }, {} as Record<string, Observation[]>);

  return Object.entries(grouped).map(([date, obs]) => ({
    date,
    count: obs.length,
    safe: obs.filter(o => o.type === 'Safe').length,
    unsafe: obs.filter(o => o.type === 'Unsafe').length
  }));
};
*/

// Helper function to group observations by category
const groupObservationsByCategory = (observations: Observation[]) => {
  const grouped = observations.reduce((acc, obs) => {
    if (!acc[obs.category]) {
      acc[obs.category] = 0;
    }
    acc[obs.category]++;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(grouped).map(([name, value]) => ({ name, value }));
};

// Helper function to group observations by type
const groupObservationsByType = (observations: Observation[]) => {
  const grouped = observations.reduce((acc, obs) => {
    if (!acc[obs.type]) {
      acc[obs.type] = 0;
    }
    acc[obs.type]++;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(grouped).map(([name, value]) => ({ name, value }));
};

// Helper function to group observations by status
const groupObservationsByStatus = (observations: Observation[]) => {
  const grouped = observations.reduce((acc, obs) => {
    if (!acc[obs.status]) {
      acc[obs.status] = 0;
    }
    acc[obs.status]++;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(grouped).map(([name, value]) => ({ name, value }));
};

// Helper function to get observations by location
const groupObservationsByLocation = (observations: Observation[]) => {
  const grouped = observations.reduce((acc, obs) => {
    if (!acc[obs.location]) {
      acc[obs.location] = 0;
    }
    acc[obs.location]++;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(grouped)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value)
    .slice(0, 5); // Top 5 locations
};

// Helper function to group observations by week and status
const groupObservationsByWeekAndStatus = (observations: Observation[]) => {
  // Define the statuses we want to track
  const statusesToTrack = ['New', 'Open', 'In Progress', 'Pending Review', 'Overdue', 'Closed'];

  // Group observations by week
  const grouped = observations.reduce((acc, obs) => {
    // Get the start of the week for this observation
    const date = new Date(obs.reportedDate);
    const weekStart = startOfWeek(date, { weekStartsOn: 1 }); // Start week on Monday
    const weekKey = format(weekStart, 'yyyy-MM-dd');

    // Calculate days since today
    const today = new Date();
    const daysSince = Math.abs(differenceInDays(today, weekStart));

    // Initialize the week if it doesn't exist
    if (!acc[weekKey]) {
      acc[weekKey] = {
        week: weekKey,
        weekLabel: `${format(weekStart, 'MMM d')} (${daysSince} days)`,
        daysSince: daysSince,
        total: 0,
        // Initialize counts for each status
        ...statusesToTrack.reduce((statusAcc, status) => {
          statusAcc[status] = 0;
          return statusAcc;
        }, {} as Record<string, number>)
      };
    }

    // Increment the total count
    acc[weekKey].total++;

    // Determine if the observation is overdue
    const isOverdue = obs.dueDate &&
                     isBefore(new Date(obs.dueDate), new Date()) &&
                     (obs.status === 'Open' || obs.status === 'In Progress' || obs.status === 'Pending Review');

    // Increment the appropriate status count
    if (isOverdue) {
      acc[weekKey]['Overdue']++;
    } else if (statusesToTrack.includes(obs.status)) {
      acc[weekKey][obs.status]++;
    }

    return acc;
  }, {} as Record<string, any>);

  // Convert to array and sort by week
  return Object.values(grouped)
    .sort((a, b) => (a.week > b.week ? 1 : -1));
};

// Colors for charts
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82ca9d'];
const STATUS_COLORS = {
  'New': '#3b82f6', // blue
  'Open': '#f59e0b', // amber
  'In Progress': '#8b5cf6', // violet
  'Pending Review': '#6b7280', // gray
  'Closed': '#10b981', // emerald
  'Action Completed & Closed': '#4f46e5', // indigo-600
};
const TYPE_COLORS = {
  'Safe': '#10b981', // emerald
  'Unsafe': '#ef4444', // red
};

interface ObservationChartsProps {
  observations?: Observation[];
}

const ObservationCharts: React.FC<ObservationChartsProps> = ({
  observations = mockObservations
}) => {
  // Get recent observations (last 30 days)
  const recentObservations = getObservationsByTimePeriod(observations, 30);

  // Prepare data for charts
  const weeklyStatusData = groupObservationsByWeekAndStatus(recentObservations);
  const categoryData = groupObservationsByCategory(recentObservations);
  const typeData = groupObservationsByType(recentObservations);
  const statusData = groupObservationsByStatus(recentObservations);
  const locationData = groupObservationsByLocation(recentObservations);

  // Prepare Highcharts options for the stacked bar chart
  const observationsOverTimeOptions = {
    chart: {
      type: 'column',
      style: {
        fontFamily: 'inherit'
      }
    },
    title: {
      text: null
    },
    xAxis: {
      categories: weeklyStatusData.map(data => data.weekLabel),
      crosshair: true,
      labels: {
        rotation: -45,
        style: {
          fontSize: '11px'
        }
      }
    },
    yAxis: {
      min: 0,
      title: {
        text: 'Number of Observations'
      },
      stackLabels: {
        enabled: true,
        style: {
          fontWeight: 'bold',
          textOutline: 'none'
        }
      }
    },
    legend: {
      align: 'center',
      verticalAlign: 'bottom',
      backgroundColor: 'transparent',
      borderColor: '#CCC',
      borderWidth: 0,
      shadow: false
    },
    tooltip: {
      useHTML: true,
      headerFormat: '<b>{point.x}</b><br/>',
      pointFormat: '{series.name}: <b>{point.y}</b><br/>Total: <b>{point.stackTotal}</b>'
    },
    plotOptions: {
      column: {
        stacking: 'normal',
        dataLabels: {
          enabled: true,
          color: '#FFFFFF',
          align: 'center',
          format: '{point.y}',
          style: {
            fontSize: '10px',
            fontWeight: 'bold',
            textOutline: '1px contrast'
          },
          // Only show data labels for values > 0
          formatter: function() {
            return this.y > 0 ? this.y : '';
          }
        }
      }
    },
    series: [
      {
        name: 'New',
        data: weeklyStatusData.map(data => data.New || 0),
        color: STATUS_COLORS['New']
      },
      {
        name: 'Open',
        data: weeklyStatusData.map(data => data.Open || 0),
        color: STATUS_COLORS['Open']
      },
      {
        name: 'In Progress',
        data: weeklyStatusData.map(data => data['In Progress'] || 0),
        color: STATUS_COLORS['In Progress']
      },
      {
        name: 'Pending Review',
        data: weeklyStatusData.map(data => data['Pending Review'] || 0),
        color: STATUS_COLORS['Pending Review']
      },
      {
        name: 'Overdue',
        data: weeklyStatusData.map(data => data.Overdue || 0),
        color: '#dc2626' // red-600
      },
      {
        name: 'Closed',
        data: weeklyStatusData.map(data => data.Closed || 0),
        color: STATUS_COLORS['Closed']
      }
    ],
    credits: {
      enabled: false
    },
    responsive: {
      rules: [{
        condition: {
          maxWidth: 500
        },
        chartOptions: {
          legend: {
            layout: 'horizontal',
            align: 'center',
            verticalAlign: 'bottom'
          }
        }
      }]
    }
  };

  // No need to calculate metrics as they're already displayed in the cards

  return (
    <div className="space-y-6">
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Observations Over Time Chart */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart2 className="h-5 w-5" />
              Observations Over Time
            </CardTitle>
            <CardDescription>Last 30 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <HighchartsReact
                highcharts={Highcharts}
                options={observationsOverTimeOptions}
                containerProps={{ style: { height: '100%', width: '100%' } }}
              />
            </div>
          </CardContent>
        </Card>

        {/* Observation Types Chart */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChartIcon className="h-5 w-5" />
              Observation Distribution
            </CardTitle>
            <CardDescription>By type and category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-80">
              <div>
                <h4 className="text-sm font-medium text-center mb-2">By Type</h4>
                <ResponsiveContainer width="100%" height="90%">
                  <PieChart>
                    <Pie
                      data={typeData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {typeData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={TYPE_COLORS[entry.name as keyof typeof TYPE_COLORS] || COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} observations`, 'Count']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div>
                <h4 className="text-sm font-medium text-center mb-2">By Category</h4>
                <ResponsiveContainer width="100%" height="90%">
                  <PieChart>
                    <Pie
                      data={categoryData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey="name"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {categoryData.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`${value} observations`, 'Count']} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Observation Status Chart */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Observation Status
            </CardTitle>
            <CardDescription>Current status distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={statusData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value} observations`, 'Count']} />
                  <Legend />
                  <Bar dataKey="value" name="Count">
                    {statusData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={STATUS_COLORS[entry.name as keyof typeof STATUS_COLORS] || COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Top Locations Chart */}
        <Card className="border hover:shadow-md transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Top Observation Locations
            </CardTitle>
            <CardDescription>Locations with most observations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={locationData}
                  layout="vertical"
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis type="category" dataKey="name" width={150} />
                  <Tooltip formatter={(value) => [`${value} observations`, 'Count']} />
                  <Legend />
                  <Bar dataKey="value" name="Count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ObservationCharts;
