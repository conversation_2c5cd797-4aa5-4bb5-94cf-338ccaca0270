
import PageHeader from "@/components/common/PageHeader";
import Tabs<PERSON>ontainer from "@/components/common/TabsContainer";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import ObservationCharts from "@/components/observations/ObservationCharts";
import InteractiveObservationDashboard from "@/components/observations/InteractiveObservationDashboard";
import { generateMockObservations } from "@/components/observations/ObservationCharts";
import {
  ClipboardList,
  FileWarning,
  AlertTriangle,
  Clipboard,
  CheckSquare,
  BookOpen,
  FileCheck
} from 'lucide-react';

const placeholderCard = (title: string, description: string, count: number | string) => (
  <Card className="border hover:shadow-md transition-shadow">
    <CardHeader className="pb-2">
      <CardTitle className="text-lg">{title}</CardTitle>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
    <CardContent>
      <div className="text-3xl font-bold text-primary">{count}</div>
    </CardContent>
  </Card>
);

const HomePage = () => {
  // Generate mock observations for the dashboard
  const mockObservations = generateMockObservations(100);

  const tabs = [
    {
      value: "observation",
      label: "Observation",
      icon: <ClipboardList className="h-4 w-4" />,
      content: (
        <div className="space-y-6">
          <InteractiveObservationDashboard observations={mockObservations} />
          <ObservationCharts observations={mockObservations} />
        </div>
      )
    },
    {
      value: "risk-assessment",
      label: "Integrated Risk Assessment",
      icon: <FileWarning className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {placeholderCard("Risk Assessments", "Total", 34)}
          {placeholderCard("High Risk Items", "Requiring review", 8)}
          {placeholderCard("Updated This Month", "Recent activity", 14)}
        </div>
      )
    },
    {
      value: "permit-to-work",
      label: "ePermit to Work",
      icon: <FileCheck className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {placeholderCard("Active Permits", "Currently open", 7)}
          {placeholderCard("Pending Approval", "Awaiting review", 3)}
          {placeholderCard("Closing Today", "Need attention", 2)}
        </div>
      )
    },
    {
      value: "operational-tasks",
      label: "Operational Tasks",
      icon: <Clipboard className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {placeholderCard("Daily Tasks", "To be completed", 23)}
          {placeholderCard("Weekly Tasks", "In progress", 12)}
          {placeholderCard("Monthly Tasks", "Upcoming", 5)}
        </div>
      )
    },
    {
      value: "incident-investigation",
      label: "Incident Investigation",
      icon: <AlertTriangle className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {placeholderCard("Open Incidents", "Under investigation", 4)}
          {placeholderCard("Critical Incidents", "High priority", 1)}
          {placeholderCard("Resolved This Month", "Completed", 8)}
        </div>
      )
    },
    {
      value: "inspection",
      label: "Inspection",
      icon: <CheckSquare className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {placeholderCard("Scheduled", "This month", 15)}
          {placeholderCard("Completed", "Last month", 28)}
          {placeholderCard("Findings", "Require action", 11)}
        </div>
      )
    },
    {
      value: "knowledge",
      label: "Knowledge",
      icon: <BookOpen className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {placeholderCard("Resources", "Available documents", 87)}
          {placeholderCard("Training Courses", "Available", 14)}
          {placeholderCard("New Content", "Added this month", 6)}
        </div>
      )
    }
  ];

  return (
    <>
      <PageHeader
        title=" Dashboard"
        description="Monitor and manage all your safety initiatives in one place"
      />

      <TabsContainer tabs={tabs} />
    </>
  );
};

export default HomePage;
