import { refreshAccessToken, handleTokenRefreshFailure } from './tokenRefresh';

/**
 * Enhanced fetch wrapper that handles authentication, 401 responses, and token refresh
 * Use this for API calls that can't use the axios instance (e.g., external APIs)
 */
export const fetchWithAuth = async (
  url: string,
  options: RequestInit = {},
  retryCount = 0
): Promise<Response> => {
  // Get token from localStorage
  const token = localStorage.getItem('access_token');

  // Prepare headers
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  // Add authorization header if token exists
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  // Make the request
  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 responses with token refresh
  if (response.status === 401 && retryCount === 0) {
    const refresh_token = localStorage.getItem('refresh_token');

    if (refresh_token) {
      try {
        console.log('Attempting to refresh access token in fetchWithAuth...');

        // Use the utility function to refresh the token
        await refreshAccessToken();

        console.log('Successfully refreshed access token in fetchWithAuth');

        // Retry the original request with the new token (increment retryCount to prevent infinite loop)
        return fetchWithAuth(url, options, retryCount + 1);
      } catch (refreshError) {
        console.error('Error refreshing token in fetchWithAuth:', refreshError);
        // Fall through to logout logic
      }
    }

    // If refresh failed or no refresh token, log out the user
    console.log('401 Unauthorized - Logging out user');

    handleTokenRefreshFailure();

    throw new Error('Unauthorized - User logged out');
  }

  return response;
};

/**
 * Convenience wrapper for GET requests with JSON response
 */
export const fetchJsonWithAuth = async <T = any>(
  url: string,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetchWithAuth(url, {
    method: 'GET',
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Convenience wrapper for POST requests with JSON response
 */
export const postJsonWithAuth = async <T = any>(
  url: string,
  data?: any,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetchWithAuth(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Convenience wrapper for PUT requests with JSON response
 */
export const putJsonWithAuth = async <T = any>(
  url: string,
  data?: any,
  options: RequestInit = {}
): Promise<T> => {
  const response = await fetchWithAuth(url, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  return await response.json();
};

/**
 * Convenience wrapper for DELETE requests
 */
export const deleteWithAuth = async (
  url: string,
  options: RequestInit = {}
): Promise<Response> => {
  return await fetchWithAuth(url, {
    method: 'DELETE',
    ...options,
  });
};
