import React, { useState, useEffect } from 'react';
import RiskAssessmentForm from '@/components/risk-assessment/TestRiskAssessment';
import HazardAssessmentForm from '@/components/risk-assessment/HazardAssessmentForm';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface RiskAssessmentData {
  id: string;
  departmentId: string;
  workActivityId: string;
  status: string;
  teamLeaderDeclaration: {
    name: string;
    sign: string;
  };
  raTeamMembers: Array<{
    id: string;
    name: string;
  }>;
  tasks: any[][];
  additonalRemarks?: string;
  overallRecommendationOne?: {
    label: string;
    value: string;
  };
  overallRecommendationTwo?: {
    label: string;
    value: string;
  };
  highRisk?: Array<{
    id: string;
    name: string;
    hazardName?: string;
  }>;
  nonRoutineDepartment?: string;
  nonRoutineWorkActivity?: string;
  hazardName?: string;
  description?: string;
  shortName?: string;
  type?: 'Routine' | 'Non Routine' | 'High-Risk Hazard';
}

interface NewRiskAssessmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  editData?: RiskAssessmentData;
  domain?: 'create' | 'edit';
}

const NewRiskAssessmentDialog: React.FC<NewRiskAssessmentDialogProps> = ({
  open,
  onOpenChange,
  editData,
  domain = 'create',
}) => {

  const [modalType, setModalType] = useState<'routine' | 'nonroutine' | 'hazard'>('routine');

  // Update modalType when editData changes
  useEffect(() => {
    if (editData?.type) {
      let mappedType: 'routine' | 'nonroutine' | 'hazard';

      if (editData.type === 'Routine') {
        mappedType = 'routine';
      } else if (editData.type === 'Non Routine') {
        mappedType = 'nonroutine';
      } else if (editData.type === 'High-Risk Hazard') {
        mappedType = 'hazard';
      } else {
        // Fallback for any unexpected type
        mappedType = 'routine';
      }

      setModalType(mappedType);
    } else {
      setModalType('routine');
    }
  }, [editData]);

  const getDialogTitle = () => {
    const prefix = domain === 'edit' ? 'Edit' : 'New';
    switch (modalType) {
      case 'routine':
        return `${prefix} Routine Risk Assessment`;
      case 'nonroutine':
        return `${prefix} Non-Routine Risk Assessment`;
      case 'hazard':
        return `${prefix} High-Risk Scenarios Assessment`;
      default:
        return `${prefix} Risk Assessment`;
    }
  };

  const getTestRiskAssessmentType = (): 'routine' | 'nonroutine' | 'highrisk' => {
    return modalType === 'hazard' ? 'highrisk' : modalType;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto bg-white">
        <DialogHeader className="bg-white border-b border-gray-200 p-6 -m-6 mb-6">
          <DialogTitle className="text-2xl font-bold text-gray-800">
            {getDialogTitle()}
          </DialogTitle>

          {/* Type Selection Cards */}
          <div className="mt-8 px-8">
            <div className="text-center mb-8">
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Select Risk Assessment Type</h3>
              <p className="text-base text-gray-600 leading-relaxed max-w-2xl mx-auto">
                Choose the appropriate risk assessment type based on your activity requirements.
                Each type has specific guidelines and evaluation criteria tailored to different operational scenarios.
              </p>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Routine Risk Assessment */}
              <div
                onClick={() => setModalType('routine')}
                className={`relative cursor-pointer rounded-xl border-2 p-8 transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                  modalType === 'routine'
                    ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 shadow-lg transform scale-105'
                    : 'border-gray-200 bg-white hover:border-blue-300'
                }`}
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                    modalType === 'routine' ? 'bg-blue-500 text-white shadow-lg' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  </div>

                  <div className="space-y-3">
                    <h3 className={`font-bold text-xl ${
                      modalType === 'routine' ? 'text-blue-800' : 'text-gray-800'
                    }`}>
                      Routine Assessment
                    </h3>

                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">
                        Standard Operations
                      </p>
                      <p className="text-sm text-gray-600 leading-relaxed px-2">
                        Designed for regularly performed activities with established procedures and standard safety controls.
                      </p>
                    </div>

                    <div className="pt-2">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Regular Activities
                      </div>
                    </div>
                  </div>
                </div>

                {modalType === 'routine' && (
                  <div className="absolute top-3 right-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>

              {/* Non-Routine Risk Assessment */}
              <div
                onClick={() => setModalType('nonroutine')}
                className={`relative cursor-pointer rounded-xl border-2 p-8 transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                  modalType === 'nonroutine'
                    ? 'border-orange-500 bg-gradient-to-br from-orange-50 to-orange-100 shadow-lg transform scale-105'
                    : 'border-gray-200 bg-white hover:border-orange-300'
                }`}
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                    modalType === 'nonroutine' ? 'bg-orange-500 text-white shadow-lg' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>

                  <div className="space-y-3">
                    <h3 className={`font-bold text-xl ${
                      modalType === 'nonroutine' ? 'text-orange-800' : 'text-gray-800'
                    }`}>
                      Non-Routine Assessment
                    </h3>

                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">
                        Special Operations
                      </p>
                      <p className="text-sm text-gray-600 leading-relaxed px-2">
                        For infrequent or one-time activities requiring special consideration and customized safety controls.
                      </p>
                    </div>

                    <div className="pt-2">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        Infrequent Activities
                      </div>
                    </div>
                  </div>
                </div>

                {modalType === 'nonroutine' && (
                  <div className="absolute top-3 right-3">
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>

              {/* High-Risk Scenarios */}
              <div
                onClick={() => setModalType('hazard')}
                className={`relative cursor-pointer rounded-xl border-2 p-8 transition-all duration-300 hover:shadow-xl hover:scale-105 ${
                  modalType === 'hazard'
                    ? 'border-red-500 bg-gradient-to-br from-red-50 to-red-100 shadow-lg transform scale-105'
                    : 'border-gray-200 bg-white hover:border-red-300'
                }`}
              >
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className={`w-16 h-16 rounded-full flex items-center justify-center mb-2 transition-all duration-300 ${
                    modalType === 'hazard' ? 'bg-red-500 text-white shadow-lg' : 'bg-gray-100 text-gray-600'
                  }`}>
                    <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>

                  <div className="space-y-3">
                    <h3 className={`font-bold text-xl ${
                      modalType === 'hazard' ? 'text-red-800' : 'text-gray-800'
                    }`}>
                      High-Risk Scenarios
                    </h3>

                    <div className="space-y-2">
                      <p className="text-sm font-medium text-gray-700">
                        Critical Operations
                      </p>
                      <p className="text-sm text-gray-600 leading-relaxed px-2">
                        For activities with significant potential for serious injury, major incidents, or critical safety concerns.
                      </p>
                    </div>

                    <div className="pt-2">
                      <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        High-Risk Activities
                      </div>
                    </div>
                  </div>
                </div>

                {modalType === 'hazard' && (
                  <div className="absolute top-3 right-3">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center shadow-lg">
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogHeader>

        {modalType === 'hazard' ? (
          <HazardAssessmentForm
            domain={domain === 'edit' ? 'edit' : 'new'}
            data={domain === 'edit' ? editData : null}
          />
        ) : (
          <RiskAssessmentForm
            type={getTestRiskAssessmentType()}
            domain={domain === 'edit' ? 'edit' : 'new'}
            data={domain === 'edit' ? editData : null}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};

export default NewRiskAssessmentDialog;
