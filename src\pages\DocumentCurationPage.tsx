import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Info, Download, Save, FileText, Eye } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Separator } from "@/components/ui/separator";
import apiService from "@/services/apiService";
import { API_BASE_URL } from "@/constants/index";
import DocumentGeneralInfoModal from "@/components/documents/DocumentGeneralInfoModal";
import DocumentSidebar from "@/components/document-curation/DocumentSidebar";
import DocumentDroppableArea from "@/components/document-curation/DocumentDroppableArea";

// API Configuration
const DOCUMENTS_API = `${API_BASE_URL}/documents`;

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'doc' | 'image' | 'video' | 'other';
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
}

const DocumentCurationPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();

  // State management
  const [document, setDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneralInfoOpen, setIsGeneralInfoOpen] = useState(false);

  // Document curation state
  const [documentComponents, setDocumentComponents] = useState<DocumentComponent[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);

  // Load document data on component mount
  useEffect(() => {
    const loadDocumentData = async () => {
      if (id) {
        try {
          setIsLoading(true);
          
          // Fetch document details from API
          const uriString = {
            include: [
              { relation: "creator" },
              { relation: "documentCategory" },
              { relation: "reviewer" },
              { relation: "approver" },
              { relation: "initiator" }
            ]
          };

          const url = `${DOCUMENTS_API}/${id}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
          const documentResponse = await apiService.get(url);

          // Transform API response to match our Document interface
          const transformedDocument: Document = {
            id: documentResponse.id,
            name: documentResponse.name,
            type: getFileTypeFromName(documentResponse.name),
            size: documentResponse.size || 'Unknown',
            uploadedBy: documentResponse.creator?.firstName || documentResponse.uploadedBy || documentResponse.createdBy || 'Unknown',
            uploadedDate: documentResponse.uploadedDate || documentResponse.created || new Date().toISOString(),
            category: documentResponse.documentCategory?.name || documentResponse.category || 'Uncategorized',
            tags: documentResponse.keywords ? documentResponse.keywords.split(',').map((tag: string) => tag.trim()) : [],
            description: documentResponse.purpose || documentResponse.description || '',
            maskId: documentResponse.maskId,
            scopeApplicability: documentResponse.scopeApplicability,
            purpose: documentResponse.purpose,
            keywords: documentResponse.keywords,
            docId: documentResponse.docId,
            created: documentResponse.created,
            updated: documentResponse.updated,
            creatorTargetDate: documentResponse.creatorTargetDate,
            reviewerTargetDate: documentResponse.reviewerTargetDate,
            approverTargetDate: documentResponse.approverTargetDate,
            initiatorId: documentResponse.initiatorId,
            creatorId: documentResponse.creatorId,
            reviewerId: documentResponse.reviewerId,
            approverId: documentResponse.approverId,
            documentCategoryId: documentResponse.documentCategoryId,
            initiator: documentResponse.initiator,
            creator: documentResponse.creator,
            reviewer: documentResponse.reviewer,
            approver: documentResponse.approver,
            documentCategory: documentResponse.documentCategory
          };

          setDocument(transformedDocument);
        } catch (error) {
          console.error("Error loading document:", error);
          toast({
            title: "Error",
            description: "Failed to load document data",
            variant: "destructive"
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    loadDocumentData();
  }, [id, toast]);

  // Helper function to determine file type from filename
  const getFileTypeFromName = (filename: string): 'pdf' | 'doc' | 'image' | 'video' | 'other' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return 'video';
      default:
        return 'other';
    }
  };

  // Drag and drop handlers
  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleAddComponent = (component: DocumentComponent) => {
    setDocumentComponents(prev => [...prev, component]);
    toast({
      title: "Component Added",
      description: `${getComponentLabel(component.type)} has been added.`,
    });
  };



  // Helper functions
  const generateId = () => {
    return 'comp_' + Math.random().toString(36).substr(2, 9);
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'video': 'Video',
      'file-attachment': 'File Attachment',
      'table': 'Table',
      'link': 'Link',
      'download-button': 'Download Button',
      'text-input': 'Text Input',
      'checkbox': 'Checkbox',
      'date-picker': 'Date Picker',
      'signature': 'Signature',
      'file-upload': 'File Upload'
    };
    return labels[type] || type;
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case 'document-header':
        return { text: 'Document Title', level: 1 };
      case 'section-header':
        return { text: 'Section Header', level: 2 };
      case 'paragraph':
        return { text: 'Enter your paragraph content here...' };
      case 'bullet-list':
        return { items: ['List item 1', 'List item 2', 'List item 3'] };
      case 'numbered-list':
        return { items: ['First item', 'Second item', 'Third item'] };
      case 'quote':
        return { text: 'Enter your quote here...', author: '' };
      case 'separator':
        return { style: 'line' };
      case 'image':
        return { src: '', alt: '', caption: '' };
      case 'video':
        return { src: '', title: '', description: '' };
      case 'file-attachment':
        return { filename: '', description: '', size: '' };
      case 'table':
        return {
          headers: ['Column 1', 'Column 2', 'Column 3'],
          rows: [
            ['Row 1 Col 1', 'Row 1 Col 2', 'Row 1 Col 3'],
            ['Row 2 Col 1', 'Row 2 Col 2', 'Row 2 Col 3']
          ]
        };
      case 'link':
        return { text: 'Link text', url: '', target: '_blank' };
      case 'download-button':
        return { text: 'Download', filename: '', description: '' };
      case 'text-input':
        return { label: 'Text Input', placeholder: 'Enter text...', required: false };
      case 'checkbox':
        return { label: 'Checkbox option', checked: false };
      case 'date-picker':
        return { label: 'Select Date', required: false };
      case 'signature':
        return { label: 'Digital Signature', required: true };
      case 'file-upload':
        return { label: 'Upload File', acceptedTypes: '', maxSize: '10MB' };
      default:
        return { text: 'Default content' };
    }
  };

  const handleRemoveComponent = (id: string) => {
    setDocumentComponents(prev => prev.filter(comp => comp.id !== id));
    toast({
      title: "Component Removed",
      description: "Component has been removed.",
    });
  };

  const handleUpdateComponent = (id: string, content: any) => {
    setDocumentComponents(prev =>
      prev.map(comp => comp.id === id ? { ...comp, content } : comp)
    );
  };

  const handleReorderComponents = (sourceIndex: number, targetIndex: number) => {
    setDocumentComponents(prev => {
      const newComponents = [...prev];
      const [removed] = newComponents.splice(sourceIndex, 1);
      newComponents.splice(targetIndex, 0, removed);
      return newComponents.map((comp, index) => ({ ...comp, position: index }));
    });
  };

  const handleBack = () => {
    navigate('/documents');
  };

  const handleGeneralInfo = () => {
    setIsGeneralInfoOpen(true);
  };

  const handlePreview = () => {
    setPreviewOpen(true);
  };

  const handleSave = () => {
    toast({
      title: "Document Saved",
      description: `Document saved with ${documentComponents.length} components.`,
    });
  };

  const handleDownload = () => {
    toast({
      title: "Download",
      description: "Document download functionality will be implemented here.",
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading document...</p>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Document Not Found</h2>
          <p className="text-gray-500 mb-4">The requested document could not be found.</p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="hover:bg-slate-100"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">📄</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-800">
                    Document Curation
                  </h1>
                  <p className="text-sm text-slate-600">
                    {document.name}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-700">
                  {document.documentCategory?.name || document.category}
                </span>
              </div>
              <div className="flex items-center gap-2 px-3 py-2 bg-green-50 rounded-lg border border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-700">
                  {documentComponents.length} components
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGeneralInfo}
                className="hover:bg-blue-50"
              >
                <Info className="h-4 w-4 mr-2" />
                Information
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreview}
                className="hover:bg-purple-50"
              >
                <Eye className="h-4 w-4 mr-2" />
                Preview
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSave}
                className="hover:bg-green-50"
              >
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="hover:bg-orange-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden" onDragEnd={handleDragEnd}>
        <DocumentSidebar
          onDragStart={handleDragStart}
        />

        <DocumentDroppableArea
          items={documentComponents}
          onAddItem={handleAddComponent}
          onRemoveItem={handleRemoveComponent}
          onUpdateItem={handleUpdateComponent}
          onReorderItems={handleReorderComponents}
        />
      </div>

      {/* Document General Info Modal */}
      <DocumentGeneralInfoModal
        open={isGeneralInfoOpen}
        onOpenChange={setIsGeneralInfoOpen}
        action={null}
        document={document}
        loading={false}
      />
    </div>
  );
};

export default DocumentCurationPage;
