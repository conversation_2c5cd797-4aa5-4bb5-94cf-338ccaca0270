import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Info, Download, Edit, Save, FileText, User, Calendar, Building } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { format } from 'date-fns';
import apiService from "@/services/apiService";
import { API_BASE_URL } from "@/constants/index";
import DocumentGeneralInfoModal from "@/components/documents/DocumentGeneralInfoModal";

// API Configuration
const DOCUMENTS_API = `${API_BASE_URL}/documents`;

interface Document {
  id: string;
  name: string;
  type: 'pdf' | 'doc' | 'image' | 'video' | 'other';
  size: string;
  uploadedBy: string;
  uploadedDate: string;
  category: string;
  tags: string[];
  description?: string;
  maskId?: string;
  scopeApplicability?: string;
  purpose?: string;
  keywords?: string;
  docId?: string;
  created?: string;
  updated?: string;
  creatorTargetDate?: string;
  reviewerTargetDate?: string;
  approverTargetDate?: string;
  initiatorId?: string;
  creatorId?: string;
  reviewerId?: string;
  approverId?: string;
  documentCategoryId?: string;
  initiator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  creator?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  reviewer?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  approver?: {
    id: string;
    firstName: string;
    email?: string;
    company?: string;
  };
  documentCategory?: {
    id: string;
    name: string;
    level?: number;
  };
}

const DocumentCurationPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();

  // State management
  const [document, setDocument] = useState<Document | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isGeneralInfoOpen, setIsGeneralInfoOpen] = useState(false);

  // Load document data on component mount
  useEffect(() => {
    const loadDocumentData = async () => {
      if (id) {
        try {
          setIsLoading(true);
          
          // Fetch document details from API
          const uriString = {
            include: [
              { relation: "creator" },
              { relation: "documentCategory" },
              { relation: "reviewer" },
              { relation: "approver" },
              { relation: "initiator" }
            ]
          };

          const url = `${DOCUMENTS_API}/${id}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
          const documentResponse = await apiService.get(url);

          // Transform API response to match our Document interface
          const transformedDocument: Document = {
            id: documentResponse.id,
            name: documentResponse.name,
            type: getFileTypeFromName(documentResponse.name),
            size: documentResponse.size || 'Unknown',
            uploadedBy: documentResponse.creator?.firstName || documentResponse.uploadedBy || documentResponse.createdBy || 'Unknown',
            uploadedDate: documentResponse.uploadedDate || documentResponse.created || new Date().toISOString(),
            category: documentResponse.documentCategory?.name || documentResponse.category || 'Uncategorized',
            tags: documentResponse.keywords ? documentResponse.keywords.split(',').map((tag: string) => tag.trim()) : [],
            description: documentResponse.purpose || documentResponse.description || '',
            maskId: documentResponse.maskId,
            scopeApplicability: documentResponse.scopeApplicability,
            purpose: documentResponse.purpose,
            keywords: documentResponse.keywords,
            docId: documentResponse.docId,
            created: documentResponse.created,
            updated: documentResponse.updated,
            creatorTargetDate: documentResponse.creatorTargetDate,
            reviewerTargetDate: documentResponse.reviewerTargetDate,
            approverTargetDate: documentResponse.approverTargetDate,
            initiatorId: documentResponse.initiatorId,
            creatorId: documentResponse.creatorId,
            reviewerId: documentResponse.reviewerId,
            approverId: documentResponse.approverId,
            documentCategoryId: documentResponse.documentCategoryId,
            initiator: documentResponse.initiator,
            creator: documentResponse.creator,
            reviewer: documentResponse.reviewer,
            approver: documentResponse.approver,
            documentCategory: documentResponse.documentCategory
          };

          setDocument(transformedDocument);
        } catch (error) {
          console.error("Error loading document:", error);
          toast({
            title: "Error",
            description: "Failed to load document data",
            variant: "destructive"
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    loadDocumentData();
  }, [id, toast]);

  // Helper function to determine file type from filename
  const getFileTypeFromName = (filename: string): 'pdf' | 'doc' | 'image' | 'video' | 'other' => {
    const extension = filename.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'doc';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return 'video';
      default:
        return 'other';
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-500" />;
      case 'doc':
        return <FileText className="h-8 w-8 text-blue-500" />;
      case 'image':
        return <FileText className="h-8 w-8 text-green-500" />;
      case 'video':
        return <FileText className="h-8 w-8 text-purple-500" />;
      default:
        return <FileText className="h-8 w-8 text-gray-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const handleBack = () => {
    navigate('/documents');
  };

  const handleGeneralInfo = () => {
    setIsGeneralInfoOpen(true);
  };

  const handleDownload = () => {
    toast({
      title: "Download",
      description: "Document download functionality will be implemented here.",
    });
  };

  const handleEdit = () => {
    toast({
      title: "Edit",
      description: "Document editing functionality will be implemented here.",
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-slate-600">Loading document...</p>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex items-center justify-center">
        <div className="text-center">
          <FileText className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Document Not Found</h2>
          <p className="text-gray-500 mb-4">The requested document could not be found.</p>
          <Button onClick={handleBack} variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Documents
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="hover:bg-slate-100"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">📄</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-800">
                    Document Curation
                  </h1>
                  <p className="text-sm text-slate-600">
                    {document.name}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-blue-700">
                  {document.documentCategory?.name || document.category}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGeneralInfo}
                className="hover:bg-blue-50"
              >
                <Info className="h-4 w-4 mr-2" />
                Information
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                className="hover:bg-green-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEdit}
                className="hover:bg-orange-50"
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 max-w-7xl mx-auto w-full px-6 py-8">
        <div className="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden">
          {/* Document Header */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b border-slate-200">
            <div className="flex items-start gap-4">
              {getFileIcon(document.type)}
              <div className="flex-1">
                <h2 className="text-2xl font-bold text-slate-800 mb-2">{document.name}</h2>
                <div className="flex items-center gap-4 text-sm text-slate-600">
                  <div className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span>{document.maskId || 'No Document ID'}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <User className="h-4 w-4" />
                    <span>{document.creator?.firstName || document.uploadedBy}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(document.created)}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Building className="h-4 w-4" />
                    <span>{document.size}</span>
                  </div>
                </div>
                {document.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {document.tags.map((tag, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Document Content Area */}
          <div className="p-6">
            <div className="text-center py-16">
              <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-lg">
                <FileText className="h-12 w-12 text-blue-500" />
              </div>
              <h3 className="text-2xl font-bold mb-3 text-slate-700">
                Document Curation Interface
              </h3>
              <p className="text-slate-600 mb-6 max-w-md mx-auto">
                This is where the document curation functionality will be implemented. 
                You can view, edit, and manage document content here.
              </p>
              <div className="flex gap-3 items-center justify-center">
                <Button
                  variant="outline"
                  onClick={handleGeneralInfo}
                  className="px-6 py-3 h-auto hover:bg-blue-50"
                >
                  <Info className="h-5 w-5 mr-2" />
                  View Information
                </Button>
                <Button
                  onClick={handleEdit}
                  className="px-6 py-3 h-auto bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl"
                >
                  <Edit className="h-5 w-5 mr-2" />
                  Start Editing
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Document General Info Modal */}
      <DocumentGeneralInfoModal
        open={isGeneralInfoOpen}
        onOpenChange={setIsGeneralInfoOpen}
        action={null}
        document={document}
        loading={false}
      />
    </div>
  );
};

export default DocumentCurationPage;
