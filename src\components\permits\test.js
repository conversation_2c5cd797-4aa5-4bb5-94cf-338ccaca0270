import React, { useState, useRef, useEffect, useCallback } from 'react'
import { Modal, Form } from 'react-bootstrap'
import { useSelector } from 'react-redux';
import API from '../../services/API';
import { FILE_URL, PERMIT_REPORTS, GET_USER_ROLE_BY_MODE, PERMIT_REPORT_WITH_ID, SUBMIT_PERMIT_ACTION } from '../../constants';
import AllFilterLocation from '../../investigation/LocationDropDown';
import ImageComponent from '../../services/FileDownlodS3';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import Swal from 'sweetalert2';
import Select from 'react-select';
import { DropzoneArea } from 'material-ui-dropzone';
import SignatureCanvas from 'react-signature-canvas'
import { Button } from 'primereact/button';
import 'primereact/resources/primereact.css';
import "primereact/resources/themes/saga-blue/theme.css";
import 'primeicons/primeicons.css';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { MobileDateTimePicker } from '@mui/x-date-pickers/MobileDateTimePicker';
import dayjs from 'dayjs';
import { TextField } from "@mui/material";
import {
    addHours,
    isSameDay,
    setHours,
    setMinutes,
    getHours,
    getMinutes,
    isAfter,
    isEqual,
    isBefore,
} from 'date-fns';
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const workTypeOptions = [
    { value: 'High-Risk Hazard', label: 'High-Risk Hazard Permit' },
    { value: 'Routine', label: 'Routine Work Activity' },
    { value: 'Non Routine', label: 'Non Routine Work Activity' }
];

function PermitModal({ show, handleClose, initialFormData, isEditMode, type, showItem }) {


    // console.log(initialFormData)
    const [formData, setFormData] = useState(initialFormData);
    const signRef = useRef()
    const user = useSelector((state) => state.login.user);
    const [errors, setErrors] = useState({});
    const [reviewer, setReviewer] = useState([])
    const [assessor, setAssessor] = useState([])
    const [permitTypeOptions, setPermitTypeOptions] = useState([]);

    // Current time
    const now = new Date();

    // 24 hours from now
    const maxStartDate = addHours(now, 24);

    /**
     * START DATE/TIME HELPER FUNCTIONS
     */
    function getStartDateMinTime() {
        // If the user picks "today", ensure they cannot pick a time in the past.
        if (isSameDay(now, formData.permitStartDate || now)) {
            return now;
        } else {
            // If they pick tomorrow, let them choose from midnight onward.
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getStartDateMaxTime() {
        // The latest the start date/time can be is 24 hours from `now`.
        if (isSameDay(maxStartDate, formData.permitStartDate || now)) {
            return maxStartDate;
        } else {
            // If the user picks a date other than `now`’s date, allow full day
            return setHours(setMinutes(new Date(), 59), 23);
        }
    }

    /**
     * END DATE/TIME HELPER FUNCTIONS
     */
    function getEndDateMinTime() {
        // The end date/time must always be >= start date/time
        if (!formData.permitStartDate) return now;
        const start = formData.permitStartDate;
        const end = formData.permitEndDate || formData.permitStartDate;

        if (isSameDay(start, end)) {
            // If end date is the same day as start date
            return start;
        } else {
            // If end date is a different day, let them choose from midnight onward
            return setHours(setMinutes(new Date(), 0), 0);
        }
    }

    function getEndDateMaxTime() {
        // The end date/time must be within 24 hours of the start date/time
        if (!formData.permitStartDate) {
            return addHours(now, 24);
        }
        const maxDateTime = addHours(formData.permitStartDate, 24);
        const end = formData.permitEndDate || formData.permitStartDate;

        if (isSameDay(maxDateTime, end)) {
            return maxDateTime;
        } else {
            // If the user picks a different day from start, allow up to 23:59 of that day
            return setHours(setMinutes(new Date(maxDateTime), 59), 23);
        }
    }

    useEffect(() => {
        if (formData.permitWorkType === 'High-Risk Hazard') {
            fetchPermitTypeOptions('high-risk-hazard-list');
        } else if (formData.permitWorkType === 'Routine') {
            fetchPermitTypeOptions('routine-list');
        } else if (formData.permitWorkType === 'Non Routine') {
            fetchPermitTypeOptions('non-routine-list');
        }
    }, [formData.permitWorkType]);

    useEffect(() => {
        getCrewList();
        getCrewList1();
    }, [formData]);

    const getCrewList = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: formData.locationOneId || "",
                locationTwoId: formData.locationTwoId || "",
                locationThreeId: formData.locationThreeId || "",
                locationFourId: formData.locationFourId || "",
                mode: 'eptwReviewer'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setReviewer(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]);

    const getCrewList1 = useCallback(async () => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: formData.locationOneId || "",
                locationTwoId: formData.locationTwoId || "",
                locationThreeId: formData.locationThreeId || "",
                locationFourId: formData.locationFourId || "",
                mode: 'eptwAssessor'
            });

            if (response.status === 200) {
                const data = response.data.map((item) => ({
                    label: item.firstName,
                    value: item.id
                }));
                setAssessor(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, [formData.locationOneId, formData.locationTwoId, formData.locationThreeId, formData.locationFourId]);

    const fetchPermitTypeOptions = async (endpoint) => {
        try {
            const response = await API.get(`/${endpoint}`);
            const options = response.data.map((item) => ({
                value: item.hazardName,
                label: item.hazardName,
                id: item.riskAssessmentId,
                controls: item.controls
            }));
            setPermitTypeOptions(options);
        } catch (error) {
            console.error("Error fetching permit type options:", error);
        }
    };

    // useEffect(() => {
    //     // Convert permitStartDate/endDate from string to Date object if provided
    //     const startDate = initialFormData?.permitStartDate
    //         ? new Date(initialFormData.permitStartDate)
    //         : null;
    //     const endDate = initialFormData?.permitEndDate
    //         ? new Date(initialFormData.permitEndDate)
    //         : null;

    //     setFormData({
    //         ...initialFormData,
    //         permitStartDate: startDate,
    //         permitEndDate: endDate,
    //     });
    // }, [initialFormData]);

    const validateFields = () => {
        const newErrors = {};

        if (!formData.permitStartDate) newErrors.permitStartDate = "Start date is required.";
        if (!formData.permitEndDate) newErrors.permitEndDate = "End date is required.";
        if (!formData.workDescription) newErrors.workDescription = "Work description is required.";
        if (!formData.nameOfSiteSupervisor) newErrors.nameOfSiteSupervisor = "Site supervisor name is required.";
        if (!formData.applicantContactNo) newErrors.applicantContactNo = "Applicant Contact number is required.";
        if (!formData.supervisorContactNo) newErrors.supervisorContactNo = "Supervisor Contact number is required.";
        if (!formData.noOfWorkers) newErrors.noOfWorkers = "Number of workers is required.";
        if (!formData.permitWorkType) newErrors.permitWorkType = "Permit Type is required.";
        if (!formData.permitType || formData.permitType.length === 0) {
            newErrors.permitType = "Checkpoint selection is required.";
        }
        if (!formData.supportingDocuments || formData.supportingDocuments.length < 2) {
            newErrors.supportingDocuments = "Minimum 2 files must be uploaded.";
        }


        if (user.type === 'External') {
            if (!formData.reviewerId) newErrors.reviewerId = "Reviewer is required.";
        }
        if (user.type === 'Internal') {
            if (!formData.assessorId) newErrors.assessorId = "Assessor is required.";
        }

        if (signRef.current && signRef.current.isEmpty()) {
            newErrors.signature = "Applicant signature is required.";
        }

        formData.permitRiskControl?.forEach((control, index) => {
            if (!control.value) {
                newErrors[`value${index}`] = "A Yes/No/NA selection is required.";
            }
            if (
                (control.value === "No" || control.value === "Not Applicable") &&
                !control.remarks
            ) {
                newErrors[`remarks${index}`] =
                    "Remarks are required when the value is No or Not Applicable.";
            }

        });

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const dataURItoFile = (dataURI, filename) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }
        return new File([ab], filename, { type: mimeString });
    };

    const uploadSignature = async () => {
        const filename = `${new Date().getTime()}_captin_sign.png`;
        const formData1 = new FormData();
        const signatureFile = dataURItoFile(
            signRef.current.getTrimmedCanvas().toDataURL("image/png"),
            filename
        );
        formData1.append('file', signatureFile);

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                return response.data.files[0].originalname;
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error:", error);
            throw error;
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleFilter = (
        locationOneId,
        locationTwoId,
        locationThreeId,
        locationFourId,
        locationFiveId,
        locationSixId
    ) => {
        setFormData((prev) => ({
            ...prev,
            locationOneId,
            locationTwoId,
            locationThreeId,
            locationFourId,
            locationFiveId,
            locationSixId
        }));
    };

    const handleSubmit = async () => {
        if (validateFields()) {
            try {
                // Upload signature if provided
                const uploadedSignature = await uploadSignature();

                // Update formData with uploaded signature
                const updatedFormData = {
                    ...formData,
                    applicantStatus: {
                        ...formData.applicantStatus,
                        signature: uploadedSignature,
                        status: true,
                        comments: '',
                        signedDate: new Date().toISOString()
                    },
                    noOfWorkers: Number(formData.noOfWorkers)
                };


                console.log(updatedFormData)
                // Determine API call based on mode
                const response = isEditMode
                    ? await API.patch(PERMIT_REPORT_WITH_ID(formData.id), updatedFormData)
                    : await API.post(PERMIT_REPORTS, updatedFormData);

                if (response.status === 200) {
                    Swal.fire(
                        "Permit",
                        isEditMode ? "Updated Successfully" : "Submitted Successfully",
                        "success"
                    );
                    handleClose();
                    // window.location.reload();
                }
            } catch (error) {
                console.error('Error saving permit:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Save Error',
                    text:
                        error.response?.data?.message ||
                        'An error occurred while saving the permit. Please try again.',
                });
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Save Error',
                text: 'Please fill all required fields and try again.',
            });
        }
    };

    const removeKeys = (obj, keysToRemove) => {
        const clonedObj = { ...obj };
        keysToRemove.forEach((key) => delete clonedObj[key]);
        return clonedObj;
    };

    const handleActionSubmit = async () => {
        if (validateFields()) {
            try {
                const uploadedSignature = await uploadSignature();

                const updatedFormData = {
                    ...removeKeys(formData, [
                        'locationOne',
                        'locationTwo',
                        'locationThree',
                        'locationFour',
                        'locationFive',
                        'locationSix',
                        'applicant',
                        'applicantId',
                        'approverStatus',
                        'applicantStatus',
                        'reviewerStatus',
                        'status'
                    ]),
                    applicantStatus: {
                        ...formData.applicantStatus,
                        signature: uploadedSignature,
                        status: true,
                        comments: '',
                        signedDate: new Date().toISOString()
                    },
                    noOfWorkers: Number(formData.noOfWorkers)
                };

                const response = await API.patch(
                    SUBMIT_PERMIT_ACTION(showItem.id),
                    updatedFormData
                );

                if (response.status === 204) {
                    Swal.fire(
                        "Permit",
                        isEditMode ? "Updated Successfully" : "Submitted Successfully",
                        "success"
                    );
                    handleClose();
                    // window.location.reload();
                }
            } catch (error) {
                console.error('Error saving permit:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Save Error',
                    text:
                        error.response?.data?.message ||
                        'An error occurred while saving the permit. Please try again.',
                });
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Save Error',
                text: 'Please fill all required fields and try again.',
            });
        }
    };

    const handleApplicantChange = (selectedOption) => {
        setFormData({
            ...formData,
            reviewerId: selectedOption ? selectedOption.value : ""
        });
    };

    const handleAssessorChange = (selectedOption) => {
        setFormData({
            ...formData,
            assessorId: selectedOption ? selectedOption.value : ""
        });
    };

    const handleWorkTypeChange = (selectedOption) => {
        setFormData({
            ...formData,
            permitWorkType: selectedOption ? selectedOption.value : "",
            permitType: [],
            permitRiskControl: []
        });
        setPermitTypeOptions([]);
    };

    useEffect(() => {
        setFormData({
            ...formData,
            permitWorkType: "High-Risk Hazard",
            permitType: [],
            permitRiskControl: []
        });
    }, [])

    const handlePermitTypeChange = (selectedOptions) => {

        console.log(selectedOptions)
        if (!selectedOptions) {
            setFormData({
                ...formData,
                permitType: [],
                permitRiskControl: [],
                riskAssessmentId: ''
            });
            return;
        }

        // For 'Routine' or 'Non Routine', only allow one selection
        if (
            (formData.permitWorkType === 'Routine' ||
                formData.permitWorkType === 'Non Routine' || formData.permitWorkType === 'High-Risk Hazard') &&
            selectedOptions.length > 1
        ) {
            Swal.fire({
                icon: 'error',
                title: 'Selection Error',
                text: 'Only one checkpoint can be selected for this Permit Work Type.'
            });
            // Keep only the first selected item
            selectedOptions = selectedOptions.slice(0, 1);
        }

        const selectedHazardNames = selectedOptions.map((option) => option.value);
        const selectedControls = selectedOptions.flatMap((option) =>
            option.controls.map((control) => ({
                remarks: "",
                evidence: [],
                description: control.value,
                currentType: control.current_type,
                files: control.files,
                method: control.method,
                permitType: option.label,
                value: ""
            }))
        );

        setFormData({
            ...formData,
            permitType: selectedHazardNames,
            permitRiskControl: selectedControls,
            riskAssessmentId: selectedOptions[0].id
        });
    };

    const handleRiskControlChange = (index, field, value) => {
        const updatedControls = [...formData.permitRiskControl];
        updatedControls[index][field] = value;
        setFormData({ ...formData, permitRiskControl: updatedControls });
    };

    const getBoxStyle = (option, selectedValue) => ({
        cursor: "pointer",
        padding: "10px 15px",
        margin: "5px",
        borderRadius: "5px",
        border: "1px solid #ccc",
        backgroundColor:
            selectedValue === option
                ? option === "Yes"
                    ? "#a5d6a7"
                    : option === "No"
                        ? "#ef9a9a"
                        : "#fff59d"
                : "transparent",
        color: selectedValue === option ? "#333" : "#555",
        textAlign: "center",
        width: "140px",
    });

    const handleBoxClick = (controlIndex, selectedValue) => {
        const updatedControls = [...formData.permitRiskControl];
        updatedControls[controlIndex].value = selectedValue;
        setFormData({ ...formData, permitRiskControl: updatedControls });
    };

    const handleEvidenceUpload = async (controlIndex, files) => {
        const updatedControls = [...formData.permitRiskControl];
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);
            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    const uploadedFileName = response.data.files[0].originalname;
                    updatedControls[controlIndex].evidence = [
                        ...(updatedControls[controlIndex].evidence || []),
                        uploadedFileName
                    ];
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }

        setFormData({ ...formData, permitRiskControl: updatedControls });
    };

    const handleSuportingUpload = async (files) => {
        if (files.length > 0) {
            const latestFile = files[files.length - 1];
            const formData1 = new FormData();
            formData1.append('file', latestFile);
            try {
                const response = await API.post(FILE_URL, formData1, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                });

                if (response && response.status === 200) {
                    setFormData((prevState) => ({
                        ...prevState,
                        supportingDocuments: [
                            ...(prevState.supportingDocuments || []),
                            response.data.files[0].originalname
                        ]
                    }));
                }
            } catch (error) {
                console.error("File upload error: ", error);
            }
        }
    };

    const handleRemoveEvidence = (controlIndex, fileIndex) => {
        const updatedControls = [...formData.permitRiskControl];
        updatedControls[controlIndex].evidence.splice(fileIndex, 1);
        setFormData({ ...formData, permitRiskControl: updatedControls });
    };

    const handleStartDateChange = (date) => {
        // If the user clears the date (or the date is null), handle that case:
        const isoDate = date ? date.toISOString() : null;
        setFormData({
            ...formData,
            permitStartDate: isoDate,
            permitEndDate: null,
        });
    };

    const handleEndDateChange = (date) => {
        const isoDate = date ? date.toISOString() : null;
        setFormData({
            ...formData,
            permitEndDate: isoDate,
        });
    };


    const handleRemoveMainImage = (index) => {
        const updatedImages = formData.supportingDocuments.filter((_, i) => i !== index);
        setFormData({
            ...formData,
            supportingDocuments: updatedImages
        });
    };

    // Group controls by permitType
    const groupedControls = formData.permitRiskControl.reduce((acc, control, index) => {
        (acc[control.permitType] = acc[control.permitType] || []).push({
            ...control,
            controlIndex: index
        });
        return acc;
    }, {});

    return (
        <div>
            <Modal show={show} onHide={handleClose} size="lg">
                <Modal.Header closeButton>
                    <Modal.Title>Electronic Permit to Work (ePTW) Application</Modal.Title>
                </Modal.Header>
                <Modal.Body>
                    <h4 className='fw-bold'>
                        Instructions to Permit Applicant:
                    </h4>
                    <i className='d-flex mb-2' style={{ fontSize: '14px' }}>
                        Please ensure all required fields are filled out accurately and thoroughly. This information is critical to assessing and managing safety risks associated with potentially hazardous work activities. Complete and precise details will support effective risk management and help protect everyone on-site.
                    </i>
                    <Form>
                        {/* PERMIT WORK TYPE */}
                        <Form.Group className="mb-3">
                            <Form.Label>Permit Work Type</Form.Label>
                            {/* <Select
                                options={workTypeOptions}
                                value={workTypeOptions.find(
                                    (option) => option.value === formData.permitWorkType
                                )}
                                onChange={handleWorkTypeChange}
                                placeholder="Select Permit Work Type"
                                isClearable
                            />
                            {errors.permitWorkType && (
                                <p className="text-danger">{errors.permitWorkType}</p>
                            )} */}

                            <Select
                                options={permitTypeOptions}
                                value={permitTypeOptions.filter((option) =>
                                    formData.permitType?.includes(option.value)
                                )}
                                onChange={handlePermitTypeChange}
                                placeholder="Select Check Point(s)"
                                isMulti
                                isClearable
                                isDisabled={!formData.permitWorkType}
                            />
                            {errors.permitType && (
                                <p className="text-danger">{errors.permitType}</p>
                            )}
                        </Form.Group>

                        {/* START/END DATE & TIME */}
                        <Form.Group className="row mb-3">
                            <div className="col-md-6">
                                <Form.Label className='d-flex'>
                                    Permit Start Date and Time
                                </Form.Label>
                                <ReactDatePicker
                                    showIcon
                                    className="form-control"
                                    showTimeSelect
                                    timeFormat="h:mm aa"
                                    timeIntervals={15}
                                    timeCaption="time"
                                    dateFormat="dd/MM/yyyy h:mm aa"
                                    // selected={formData.permitStartDate}
                                    selected={
                                        formData.permitStartDate
                                            ? new Date(formData.permitStartDate)
                                            : null
                                    }
                                    onChange={handleStartDateChange}
                                    minDate={now}
                                    maxDate={maxStartDate}
                                    minTime={getStartDateMinTime()}
                                    maxTime={getStartDateMaxTime()}
                                    filterTime={(time) => {
                                        // Ensure the chosen time is >= now and <= now+24h if same day
                                        const selectedDate = formData.permitStartDate || now;
                                        const currentTime = new Date(time);
                                        const minTime = getStartDateMinTime();
                                        const maxTime = getStartDateMaxTime();
                                        return (
                                            (isAfter(currentTime, minTime) ||
                                                isEqual(currentTime, minTime)) &&
                                            (isBefore(currentTime, maxTime) ||
                                                isEqual(currentTime, maxTime))
                                        );
                                    }}
                                    filterDate={(date) => {
                                        // Must be between now (inclusive) and now + 24 hours
                                        return (
                                            (isAfter(date, now) || isEqual(date, now)) &&
                                            (isBefore(date, maxStartDate) ||
                                                isEqual(date, maxStartDate))
                                        );
                                    }}
                                />
                                {errors.permitStartDate && (
                                    <p className="text-danger">{errors.permitStartDate}</p>
                                )}
                            </div>
                            <div className="col-md-6">
                                <Form.Label className='d-flex'>
                                    Permit End Date and Time
                                </Form.Label>
                                <ReactDatePicker
                                    showIcon
                                    className="form-control"
                                    showTimeSelect
                                    timeFormat="h:mm aa"
                                    timeIntervals={15}
                                    timeCaption="time"
                                    dateFormat="dd/MM/yyyy h:mm aa"
                                    // selected={formData.permitEndDate}
                                    selected={
                                        formData.permitEndDate
                                            ? new Date(formData.permitEndDate)
                                            : null
                                    }
                                    onChange={handleEndDateChange}
                                    disabled={!formData.permitStartDate}
                                    minDate={formData.permitStartDate || now}
                                    maxDate={
                                        formData.permitStartDate
                                            ? addHours(formData.permitStartDate, 24)
                                            : addHours(now, 24)
                                    }
                                    minTime={getEndDateMinTime()}
                                    maxTime={getEndDateMaxTime()}
                                    filterTime={(time) => {
                                        const currentTime = new Date(time);
                                        const minTime = getEndDateMinTime();
                                        const maxTime = getEndDateMaxTime();
                                        return (
                                            (isAfter(currentTime, minTime) ||
                                                isEqual(currentTime, minTime)) &&
                                            (isBefore(currentTime, maxTime) ||
                                                isEqual(currentTime, maxTime))
                                        );
                                    }}
                                    filterDate={(date) => {
                                        const minDate = formData.permitStartDate || now;
                                        const maxDate = formData.permitStartDate
                                            ? addHours(formData.permitStartDate, 24)
                                            : addHours(now, 24);
                                        return (
                                            (isAfter(date, minDate) || isEqual(date, minDate)) &&
                                            (isBefore(date, maxDate) || isEqual(date, maxDate))
                                        );
                                    }}
                                />
                                {errors.permitEndDate && (
                                    <p className="text-danger">{errors.permitEndDate}</p>
                                )}
                            </div>
                        </Form.Group>

                        <Form.Control
                            type="datetime-local"
                            value={formData.permitStartDate?.slice(0, 16)} // ISO format sliced to "yyyy-MM-ddTHH:mm"
                            onChange={(e) =>
                                setFormData((prev) => ({
                                    ...prev,
                                    permitStartDate: e.target.value ? new Date(e.target.value).toISOString() : "",
                                }))
                            }
                        />

                        {/* WORK DESCRIPTION */}
                        <Form.Group className="mb-3">
                            <Form.Label>Work Description</Form.Label>
                            <Form.Control
                                as="textarea"
                                name="workDescription"
                                value={formData.workDescription || ""}
                                onChange={handleInputChange}
                                rows={3}
                            />
                            {errors.workDescription && (
                                <p className="text-danger">{errors.workDescription}</p>
                            )}
                        </Form.Group>

                        {/* LOCATION */}
                        <Form.Group className="mb-3">
                            <Form.Label>Location</Form.Label>
                            <AllFilterLocation
                                handleFilter={handleFilter}
                                getLocation={formData}
                            />
                        </Form.Group>

                        {/* SUPERVISOR, CONTACTS, WORKERS, ETC. */}
                        <Form.Group className="mb-3">
                            <Form.Label>Responsible Site / Job Supervisor</Form.Label>
                            <Form.Control
                                type="text"
                                name="nameOfSiteSupervisor"
                                value={formData.nameOfSiteSupervisor || ""}
                                onChange={handleInputChange}
                            />
                            {errors.nameOfSiteSupervisor && (
                                <p className="text-danger">
                                    {errors.nameOfSiteSupervisor}
                                </p>
                            )}
                        </Form.Group>

                        <Form.Group className="row mb-3">
                            <div className="col-md-6">
                                <Form.Label>Number of Workers</Form.Label>
                                <Form.Control
                                    type="number"
                                    name="noOfWorkers"
                                    value={formData.noOfWorkers || ""}
                                    onChange={handleInputChange}
                                />
                                {errors.noOfWorkers && (
                                    <p className="text-danger">{errors.noOfWorkers}</p>
                                )}
                            </div>
                            <div className="col-md-6">
                                <Form.Label>Work Order / Job Number</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="workOrderNo"
                                    value={formData.workOrderNo || ""}
                                    onChange={handleInputChange}
                                />
                            </div>
                        </Form.Group>

                        <Form.Group className="row mb-3">
                            <div className="col-md-6">
                                <Form.Label>Applicant Contact Number</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="applicantContactNo"
                                    value={formData.applicantContactNo || ""}
                                    onChange={handleInputChange}
                                />
                                {errors.applicantContactNo && (
                                    <p className="text-danger">
                                        {errors.applicantContactNo}
                                    </p>
                                )}
                            </div>
                            <div className="col-md-6">
                                <Form.Label>Supervisor Contact Number</Form.Label>
                                <Form.Control
                                    type="text"
                                    name="supervisorContactNo"
                                    value={formData.supervisorContactNo || ""}
                                    onChange={handleInputChange}
                                />
                                {errors.supervisorContactNo && (
                                    <p className="text-danger">
                                        {errors.supervisorContactNo}
                                    </p>
                                )}
                            </div>
                        </Form.Group>

                        {/* SUPPORTING DOCUMENTS UPLOAD */}
                        <div className="col-12 mt-3 mb-2">
                            <label className="mb-2">
                                Add/Upload images of the work location and
                                authorized workers list
                                (Min 2 attachment is mandatory) *
                            </label>
                            <DropzoneArea
                                dropzoneText="Attach supporting images / documents"
                                filesLimit={5}
                                maxFileSize={104857600} // ~100 MB
                                showPreviewsInDropzone={false}
                                showPreviews={false}
                                dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                                onChange={(files) => handleSuportingUpload(files)}
                            />
                            {errors.supportingDocuments && (
                                <p className="text-danger">{errors.supportingDocuments}</p>
                            )}
                        </div>
                        <div className='col-12 mt-3 mb-4'>
                            {formData.supportingDocuments &&
                                formData.supportingDocuments.length > 0 && (
                                    <label className='mb-2'>Uploaded Documents</label>
                                )}
                            <div className="image-preview mt-3">
                                {formData.supportingDocuments &&
                                    formData.supportingDocuments.length > 0 && (
                                        <div className="row">
                                            {formData.supportingDocuments.map((file, idx) => (
                                                <div
                                                    key={idx}
                                                    className="col-3"
                                                    style={{ position: 'relative' }}
                                                >
                                                    <div className="boxShadow d-flex align-items-center">
                                                        <ImageComponent
                                                            fileName={file}
                                                            size={'100'}
                                                            name={true}
                                                        />
                                                        <i
                                                            className="pi pi-trash"
                                                            onClick={() => handleRemoveMainImage(idx)}
                                                            style={{
                                                                position: 'absolute',
                                                                top: '5px',
                                                                right: '5px',
                                                                cursor: 'pointer',
                                                                color: 'red',
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                            </div>
                        </div>

                        {/* CHECKPOINTS / PERMIT TYPE */}
                        {/* <Form.Group className="mb-3">
                            <Form.Label>Checkpoints</Form.Label>
                            <Select
                                options={permitTypeOptions}
                                value={permitTypeOptions.filter((option) =>
                                    formData.permitType?.includes(option.value)
                                )}
                                onChange={handlePermitTypeChange}
                                placeholder="Select Check Point(s)"
                                isMulti
                                isClearable
                                isDisabled={!formData.permitWorkType}
                            />
                            {errors.permitType && (
                                <p className="text-danger">{errors.permitType}</p>
                            )}
                        </Form.Group> */}

                        {/* RISK CONTROLS */}
                        {Object.entries(groupedControls).map(([permitType, controls]) => (
                            <div key={permitType} className="mb-4">
                                <h5 className="mt-4 mb-3 permit-head">{permitType}</h5>
                                {controls.map((control) => (
                                    <div
                                        key={control.controlIndex}
                                        className="border p-3 mb-3 rounded"
                                    >
                                        <p>
                                            <strong>{control.controlIndex + 1}.</strong>{' '}
                                            {control.description}
                                        </p>
                                        <p>
                                            <strong>Control Type:</strong> {control.currentType}
                                        </p>
                                        <Form.Group className="mb-3">
                                            <div style={{ display: "flex" }}>
                                                {["Yes", "No", "Not Applicable"].map((option) => (
                                                    <div
                                                        key={`${option}-${control.controlIndex}`}
                                                        onClick={() =>
                                                            handleBoxClick(control.controlIndex, option)
                                                        }
                                                        style={getBoxStyle(
                                                            option,
                                                            control.value
                                                        )}
                                                    >
                                                        {option}
                                                    </div>
                                                ))}
                                            </div>
                                            {errors[`value${control.controlIndex}`] && (
                                                <p className="text-danger">
                                                    {errors[`value${control.controlIndex}`]}
                                                </p>
                                            )}
                                        </Form.Group>

                                        <Form.Group className="mb-3">
                                            <Form.Label>Remarks</Form.Label>
                                            <Form.Control
                                                type="text"
                                                value={control.remarks}
                                                onChange={(e) =>
                                                    handleRiskControlChange(
                                                        control.controlIndex,
                                                        "remarks",
                                                        e.target.value
                                                    )
                                                }
                                            />
                                            {errors[`remarks${control.controlIndex}`] && (
                                                <p className="text-danger">
                                                    {errors[`remarks${control.controlIndex}`]}
                                                </p>
                                            )}
                                        </Form.Group>

                                        {/* EVIDENCE UPLOAD */}
                                        <div className="col-12 mt-3 mb-2">
                                            <label htmlFor="incidentImages" className="mb-2">
                                                Evidences
                                            </label>
                                            <DropzoneArea
                                                dropzoneText="Attach supporting images / documents"
                                                filesLimit={5}
                                                maxFileSize={104857600}
                                                showPreviewsInDropzone={false}
                                                showPreviews={false}
                                                dropzoneClass="dropzoneText d-flex align-items-center justify-content-center"
                                                onChange={(files) =>
                                                    handleEvidenceUpload(
                                                        control.controlIndex,
                                                        files
                                                    )
                                                }
                                            />
                                        </div>
                                        <div className="col-12 mt-3 mb-4">
                                            {control.evidence && control.evidence.length > 0 && (
                                                <label
                                                    htmlFor="username"
                                                    className="mb-2"
                                                >
                                                    Uploaded
                                                </label>
                                            )}
                                            <div className="image-preview mt-3">
                                                {control.evidence &&
                                                    control.evidence.length > 0 && (
                                                        <div className="row">
                                                            {control.evidence.map(
                                                                (file, fileIndex) => (
                                                                    <div
                                                                        key={fileIndex}
                                                                        className="col-3"
                                                                        style={{
                                                                            position:
                                                                                "relative"
                                                                        }}
                                                                    >
                                                                        <div className="boxShadow d-flex align-items-center">
                                                                            <ImageComponent
                                                                                fileName={file}
                                                                                size={"100"}
                                                                                name={true}
                                                                            />
                                                                            <i
                                                                                className="pi pi-trash"
                                                                                onClick={() =>
                                                                                    handleRemoveEvidence(
                                                                                        control.controlIndex,
                                                                                        fileIndex
                                                                                    )
                                                                                }
                                                                                style={{
                                                                                    position:
                                                                                        "absolute",
                                                                                    top: "5px",
                                                                                    right: "5px",
                                                                                    cursor:
                                                                                        "pointer",
                                                                                    color: "red"
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                )
                                                            )}
                                                        </div>
                                                    )}
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        ))}

                        {/* SIGNATURE BLOCK */}
                        <div className="row mb-4 text-center">
                            <div className="d-flex flex-column col-12">
                                <Form.Label className="fw-bold">Applicant Sign</Form.Label>
                                <i>
                                    I confirm that all work activities are complete, the site is
                                    fully restored to a safe condition, and all tools, equipment,
                                    and materials have been secured and accounted for.
                                </i>
                                <div className="row mt-2">
                                    <div className="col-12">
                                        {errors.signature && (
                                            <p className="text-danger">{errors.signature}</p>
                                        )}
                                        <SignatureCanvas
                                            penColor="#1F3BB3"
                                            backgroundColor="white"
                                            canvasProps={{
                                                width: 450,
                                                height: 120,
                                                className: "sigCanvas",
                                                style: {
                                                    boxShadow: "0px 0px 10px 3px rgb(189 189 189)"
                                                }
                                            }}
                                            ref={signRef}
                                        />
                                        <i
                                            className="fa fa-undo undo"
                                            onClick={() => signRef.current.clear()}
                                        ></i>
                                        <p>{user.firstName}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* REVIEWER/ASSESSOR SELECT */}
                        {user.type === 'External' ? (
                            <Form.Group className="mb-3">
                                <Form.Label>Reviewer</Form.Label>
                                <Select
                                    options={reviewer}
                                    value={reviewer.find(
                                        (option) => option.value === formData.reviewerId
                                    )}
                                    onChange={handleApplicantChange}
                                    placeholder="Select Reviewer"
                                    isClearable
                                />
                                {errors.reviewerId && (
                                    <p className="text-danger">{errors.reviewerId}</p>
                                )}
                            </Form.Group>
                        ) : (
                            <Form.Group className="mb-3">
                                <Form.Label>Assessor</Form.Label>
                                <Select
                                    options={assessor}
                                    value={assessor.find(
                                        (option) => option.value === formData.assessorId
                                    )}
                                    onChange={handleAssessorChange}
                                    placeholder="Select Assessor"
                                    isClearable
                                />
                                {errors.assessorId && (
                                    <p className="text-danger">{errors.assessorId}</p>
                                )}
                            </Form.Group>
                        )}
                    </Form>
                </Modal.Body>
                <Modal.Footer>
                    <Button variant="secondary" onClick={handleClose}>
                        Close
                    </Button>
                    <Button
                        variant="primary"
                        onClick={type === 'action' ? handleActionSubmit : handleSubmit}
                    >
                        Submit
                    </Button>
                </Modal.Footer>
            </Modal>
            {console.log(formData)}
        </div>
    );
}

export default PermitModal;
