"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { <PERSON>dal, Button, Form, Card, Row, Col, Container } from "react-bootstrap";
import { ACKNOWLEDGE_PERMIT_ACTION, FILE_URL, GET_USER_ROLE_BY_MODE, OBS_ACTION_SUBMIT, SUBMIT_PERMIT_ACTION } from "@/constant";
import Swal from "sweetalert2";
import API from "@/services/API";
import Select, { SingleValue } from "react-select";
import ImageComponent from "../../services/FileDownlodS3";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import ViewOBS from "../observation/ViewOBS";
import FileUploader from "@/services/FileUploader";
import ModalSelect from "@/services/ModalSelect";
import ViewEptw from "../eptw-gen/ViewEPTW";
import SignatureCanvas from "react-signature-canvas";

// If using a dropzone library, import it below, e.g.:
// import { DropzoneArea } from "material-ui-dropzone";

// import ViewObs from "./ViewObs"; // Adjust import path to your actual file

// Define the object shape for applicationDetails
interface ApplicationDetail {
    maskId?: string;
    status?: string;
    // Add more fields as necessary
}

// Define the shape for showItem
interface ShowItem {
    id: string;
    actionType: "take_action" | "reperform_action" | "review" | "verify_action" | "Approve" | "Review" | "Assess" | 'Acknowledgement';
    actionToBeTaken?: string;  //
    // Add other fields if needed
}
type ApplicationDetails = any;
interface OptionType {
    value: string;
    label: string;
}

interface ActionModalProps {
    show: boolean;                          // Whether the modal is visible
    applicationDetails?: ApplicationDetail; // Single object used for .maskId, .status
    showItem?: ShowItem;                    // Contains .id, .actionType, etc.
    closeModal: (flag?: boolean) => void;   // Callback to close modal
}



const EPTWModal: React.FC<ActionModalProps> = ({
    show,
    applicationDetails,
    showItem,
    closeModal,
}) => {
    // If modal should not be visible, return null


    const signRef = useRef<SignatureCanvas>(null);
    // Component states
    const [apiStatus, setApiStatus] = useState<string>("");
    const [signs, setSign] = useState<string>("");
    const [signModal, setSignModal] = useState<boolean>(false);
    const [comments, setComments] = useState<string>("");
    const [showErrors, setShowErrors] = useState<boolean>(false);
    const [assessor, setAssessor] = useState<OptionType[]>([]);
    const [assessorId, setAssessorId] = useState<string>("");
    const [actionTaken, setActionTaken] = useState<string>("");
    const [actionToBeTaken, setActionToBeTaken] = useState<string>("");
    const [evidence, setEvidence] = useState<string[]>([]);
    const [dueDate, setDueDate] = useState<string | null>(null);
    const [uploads, setUploads] = useState<string[]>([]);

    // Decide which crewList to fetch based on showItem.actionType

    useEffect(() => {
        if (showItem?.actionType === 'Review') {
            getCrewList('eptwAssessor')
        } else if (showItem?.actionType === 'Assess') {
            getCrewList('eptwApprover')
        }


    }, [showItem])

    // Fetch the crew list
    const getCrewList = useCallback(async (type: string) => {
        try {
            const response = await API.post(GET_USER_ROLE_BY_MODE, {
                locationOneId: "",
                locationTwoId: "",
                locationThreeId: "",
                locationFourId: "",
                mode: type,
            });

            if (response.status === 200) {
                const data: OptionType[] = response.data.map((item: any) => ({
                    label: item.firstName,
                    value: item.id,
                }));
                setAssessor(data);
            }
        } catch (error) {
            console.error("Error fetching crew list:", error);
        }
    }, []);
    const dataURItoFile = (dataURI: any, filename: any) => {
        var byteString = atob(dataURI.split(",")[1]);
        // separate out the mime component
        var mimeString = dataURI.split(",")[0].split(":")[1].split(";")[0];
        // write the bytes of the string to an ArrayBuffer
        var ab = new ArrayBuffer(byteString.length);
        var dw = new DataView(ab);
        for (var i = 0; i < byteString.length; i++) {
            dw.setUint8(i, byteString.charCodeAt(i));
        }

        // write the ArrayBuffer to a blob, and you're done
        return new File([ab], filename, { type: mimeString });
    };
    const uploadSignature = async () => {
        const filename = `${new Date().getTime()}_captin_sign.png`;
        const formData1 = new FormData();
        const signatureFile = dataURItoFile(signs, filename);
        formData1.append('file', signatureFile);

        try {
            const response = await API.post(FILE_URL, formData1, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                }
            });

            if (response && response.status === 200) {
                // Return the uploaded file name
                return response.data.files[0].originalname;
            } else {
                throw new Error("File upload failed.");
            }
        } catch (error) {
            console.error("File upload error:", error);
            throw error;
        }
    };
    const isValid = () => {
        if (showItem?.actionType === 'Acknowledgement') return signs !== '';
        else {
            if (apiStatus === 'Approve') {
                return showItem?.actionType === 'Approve' ? comments !== '' && signs !== '' : signs !== '' && assessorId !== '';
            }
            if (apiStatus === 'Return') {
                return comments !== '';
            }
            return false;
        }
    };
    // Validation & Submission

    const handleAknowledgeSubmit = async () => {
        setShowErrors(true);
        if (isValid()) {
            try {
                let payload;

                const signatureFileName = await uploadSignature();

                payload = {

                    acknowledgementStatus: { signature: signatureFileName, comments: '', uploads: uploads },

                };

                const response = await API.patch(ACKNOWLEDGE_PERMIT_ACTION(showItem?.id || ""), payload);

                if (response.status === 204) {
                    Swal.fire("Permit", "Submitted Successfully", "success");
                    closeModal();
                    // window.location.reload(); 
                }
            } catch (error) {
                console.error("Error:", error);
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Validation',
                text: `Please fill all the required fields`
            });
        }
    }
    const handleSubmit = async () => {

        setShowErrors(true);
        if (isValid()) {
            try {
                let payload;

                if (apiStatus === "Return") {
                    // If apiStatus is "Return", only include comments and status
                    payload = {
                        comments: comments,
                        status: "Returned"
                    };
                } else {
                    // If apiStatus is not "Return", upload signature and set other fields
                    const signatureFileName = await uploadSignature();

                    payload = {
                        comments: comments,
                        ...(showItem?.actionType === 'Review'
                            ? { reviewerStatus: { signature: signatureFileName }, assessorId: assessorId }
                            : showItem?.actionType === 'Assess'
                                ? { assessorStatus: { signature: signatureFileName }, approverId: assessorId }
                                : { approverStatus: { signature: signatureFileName } })
                    };
                }
                const response = await API.patch(SUBMIT_PERMIT_ACTION(showItem?.id || ""), payload);

                if (response.status === 204) {
                    Swal.fire("Permit", "Submitted Successfully", "success");
                    closeModal();
                    // window.location.reload(); 
                }
            } catch (error) {
                console.error("Error:", error);
            }
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Validation',
                text: `Please fill all the required fields`
            });
        }
    };

    // Handle react-select change
    const handleApplicantChange = (selectedOption: SingleValue<OptionType>) => {
        setAssessorId(selectedOption ? selectedOption.value : "");
    };

    // Handle evidence uploads


    // Remove evidence by index
    const handleRemoveMainImage = (index: number) => {
        setEvidence((prev) => prev.filter((_, i) => i !== index));
    };

    return (<>
        <Modal
            show={show}
            size="lg"
            onHide={() => closeModal(false)}
            aria-labelledby="example-modal-sizes-title-md"
            id="pdf-content"
        >
            <Modal.Header closeButton>
                {applicationDetails && (
                    <div className="row" style={{ width: "100%" }}>
                        <div className="col-9">
                            <div className="row">
                                <div className="col-12">
                                    <h4>Permit</h4>
                                    <div className="d-flex align-items-center">
                                        <p className="me-2">#{applicationDetails.maskId || ""}</p>
                                        <p className="badge bg-primary text-white">{applicationDetails.status}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </Modal.Header>

            <Modal.Body>
                {/* If you pass applicationDetails as reportData to ViewObs */}
                {applicationDetails && <ViewEptw applicationDetails={applicationDetails as unknown as ApplicationDetails} />}

                <h5 className='p-3 fw-bold'>{showItem?.actionToBeTaken}</h5>
                {showItem?.actionType === "Acknowledgement" ? <>
                    <Col className="m-auto mb-3 mt-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                            <Card.Body>
                                <label htmlFor="incidentImages" className="mb-2">
                                    Add/Upload Evidence if any
                                </label>
                                <FileUploader
                                    disabled={false}
                                    onFilesSelected={(files) => setUploads(files)}
                                    files={uploads}

                                />
                            </Card.Body>
                        </Card>
                    </Col>

                    <Col className="m-auto mb-3 mt-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                            <Card.Body>
                                <Row>
                                    <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                        <label style={{ textAlign: 'justify' }}>
                                            I acknowledge that,to the best of my knowledge, the work has been completed as per the permit.
                                        </label>
                                    </Col>
                                    <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => setSignModal(true)}>
                                        <span className="bi bi-pencil-square" style={{ fontSize: 60 }}>
                                        </span>
                                    </Col>
                                    <div className="d-flex justify-content-center">
                                        {signs ? (
                                            <img src={signs} height={100} style={{ minWidth: 150 }} />
                                        ) : (
                                            showErrors && (
                                                <p className="text-danger mt-2">Signature is required for approval.</p>
                                            )
                                        )}
                                    </div>
                                </Row>
                            </Card.Body>
                        </Card>
                    </Col>
                </> : <>
                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                            <Card.Body >
                                {/* Approve / Return */}
                                <Col className="m-auto mb-3" xs={12}>
                                    <Card style={{ boxShadow: "rgba(0, 0, 0, 0.24) 0px 3px 8px" }}>
                                        <Card.Body>
                                            <Row className="justify-content-center">
                                                <Col xs={12} className="d-flex text-center">
                                                    <Container
                                                        fluid
                                                        className="col-6 p-2"
                                                        style={{
                                                            border: "1px solid #dee2e6",
                                                            borderRadius: 10,
                                                            color: "#000000",
                                                            cursor: "pointer",
                                                        }}
                                                        onClick={() => setApiStatus("Approve")}
                                                    >
                                                        <Row>
                                                            <Col xs={4}>
                                                                <div
                                                                    style={
                                                                        apiStatus === "Approve"
                                                                            ? {
                                                                                width: 24,
                                                                                height: 24,
                                                                                borderRadius: 12,
                                                                                background: "green",
                                                                            }
                                                                            : {
                                                                                width: 24,
                                                                                height: 24,
                                                                                borderRadius: 12,
                                                                                background: "lightgray",
                                                                            }
                                                                    }
                                                                >
                                                                    {apiStatus === "Approve" && (
                                                                        <i className="bi bi-check text-white"></i>

                                                                    )}
                                                                </div>
                                                            </Col>
                                                            <Col xs={8} style={apiStatus === "Approve" ? { color: "green" } : {}}>
                                                                Approve
                                                            </Col>
                                                        </Row>
                                                    </Container>

                                                    <Container
                                                        fluid
                                                        className="col-5 p-2"
                                                        style={{
                                                            border: "1px solid #dee2e6",
                                                            borderRadius: 10,
                                                            color: "#000000",
                                                            cursor: "pointer",
                                                        }}
                                                        onClick={() => setApiStatus("Return")}
                                                    >
                                                        <Row>
                                                            <Col xs={4}>
                                                                <div
                                                                    style={
                                                                        apiStatus === "Return"
                                                                            ? {
                                                                                width: 24,
                                                                                height: 24,
                                                                                borderRadius: 12,
                                                                                background: "red",
                                                                            }
                                                                            : {
                                                                                width: 24,
                                                                                height: 24,
                                                                                borderRadius: 12,
                                                                                background: "lightgray",
                                                                            }
                                                                    }
                                                                >
                                                                    {apiStatus === "Return" && (
                                                                        <i className="bi bi-check text-white"></i>
                                                                    )}
                                                                </div>
                                                            </Col>
                                                            <Col xs={8} style={apiStatus === "Return" ? { color: "red" } : {}}>
                                                                Return
                                                            </Col>
                                                        </Row>
                                                    </Container>
                                                </Col>
                                            </Row>
                                            {showErrors && !apiStatus && (
                                                <p className="text-danger mt-2">Please select Approve or Return.</p>
                                            )}
                                        </Card.Body>
                                    </Card>
                                </Col>
                                {showErrors && !apiStatus && (
                                    <p className="text-danger mt-2">Please select Approve or Return.</p>
                                )}
                            </Card.Body>
                        </Card>
                    </Col>

                    <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                        <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                            <Card.Body>
                                <Row className="justify-content-center">
                                    <Col className="d-flex text-center">
                                        <textarea
                                            rows={4}
                                            cols={50}
                                            className="form-control"
                                            placeholder="Enter your comments here..."
                                            onChange={(e) => setComments(e.target.value)}
                                        />
                                    </Col>
                                </Row>
                                {showErrors && comments === '' && (
                                    <p className="text-danger mt-2">Comments are required.</p>
                                )}
                            </Card.Body>
                        </Card>
                    </Col>




                    {apiStatus === 'Approve' && (<>
                        {showItem?.actionType !== 'Approve' &&
                            <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                                <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                    <Card.Body>
                                        <Form.Group className="mb-3">
                                            {/* <Form.Label>{showItem?.actionType === 'Review' ? 'Assessor' : 'Approver'}</Form.Label> */}
                                            <ModalSelect
                                                title={showItem?.actionType === "Review"
                                                    ? "Assessor"
                                                    : showItem?.actionType === "Assess" ? "Approver" : "Reviewer"}
                                                options={assessor}                // same array of { value, label }
                                                selectedValue={assessorId}
                                                onChange={(newVal) => setAssessorId(newVal)}
                                                placeholder={
                                                    showItem?.actionType === "Review"
                                                        ? "Select Assessor"
                                                        : showItem?.actionType === "Assess" ? "Select Approver" : "Select Reviewer"
                                                }
                                                clearable
                                                disabled={false}
                                            />

                                        </Form.Group>
                                        {showErrors && assessorId === '' && (
                                            <p className="text-danger mt-2">{showItem?.actionType === 'Review' ? 'Assessor' : 'Approver'}  required.</p>
                                        )}

                                    </Card.Body>
                                </Card>
                            </Col>
                        }
                        <Col className="m-auto mb-3" xs={12} sm={12} md={12}>
                            <Card style={{ boxShadow: 'rgba(0, 0, 0, 0.24) 0px 3px 8px' }}>
                                <Card.Body>
                                    <Row>
                                        <Col xs={12} sm={12} md={12} className="d-flex text-justify">
                                            <label style={{ textAlign: 'justify' }}>
                                                I acknowledge this permit application.
                                            </label>
                                        </Col>
                                        <Col xs={12} sm={12} md={12} className="d-flex justify-content-center p-2" onClick={() => setSignModal(true)}>
                                            <span className="bi bi-pencil-square" style={{ fontSize: 60 }}>
                                            </span>
                                        </Col>
                                        <div className="d-flex justify-content-center">
                                            {signs ? (
                                                <img src={signs} height={100} style={{ minWidth: 150 }} />
                                            ) : (
                                                showErrors && (
                                                    <p className="text-danger mt-2">Signature is required for approval.</p>
                                                )
                                            )}
                                        </div>
                                    </Row>
                                </Card.Body>
                            </Card>
                        </Col>
                    </>
                    )}
                </>}
            </Modal.Body>

            <Modal.Footer>
                {showItem?.actionType === "Review" ? (
                    <Button variant="primary" onClick={handleSubmit}>
                        {apiStatus === 'Return' ? 'Return to Applicant' : 'Submit to Assessor'}
                    </Button>
                ) : showItem?.actionType === "Assess" ? (
                    <Button variant="primary" onClick={handleSubmit}>
                        {apiStatus === 'Return' ? 'Return to Applicant' : 'Submit to Approver'}
                    </Button>
                ) : showItem?.actionType === "Acknowledgement" ? (<>
                    <Button variant="primary" onClick={handleAknowledgeSubmit}>
                        Permit CloseOut
                    </Button>
                </>)
                    : (
                        <Button variant="primary" onClick={handleSubmit}>
                            {apiStatus === 'Return' ? 'Return to Applicant' : 'Approve'}
                        </Button>
                    )}
            </Modal.Footer>
        </Modal>

        <Modal
            show={signModal}
            onHide={() => { setSignModal(false) }}
            aria-labelledby="contained-modal-title-vcenter"
            centered
            backdrop={'static'}
        >
            <Modal.Header closeButton={false}>
                <Modal.Title id="contained-modal-title-vcenter">
                    Sign
                </Modal.Title>
            </Modal.Header>
            <Modal.Body style={{ background: '#f5f5f5', width: '100%' }}>
                <SignatureCanvas
                    ref={signRef as React.RefObject<SignatureCanvas>}
                    penColor="#1F3BB3"
                    backgroundColor="white"
                    canvasProps={{
                        className: "sigCanvas",
                        style: {
                            width: '100%', // Ensures the canvas takes up the full width
                            background: '#fff',
                            boxShadow: "0px 0px 10px 3px rgb(189 189 189)",
                            height: '100px'
                        },
                    }}
                />
            </Modal.Body>
            <Modal.Footer>
                <Button onClick={() => {
                    const signature = signRef.current?.toDataURL("image/png");
                    if (signature) setSign(signature);
                    setSignModal(false);
                }}>confirm</Button>
                <Button onClick={() => signRef.current?.clear()}>Clear</Button>
                <Button onClick={() => { setSignModal(false) }}>Close</Button>
            </Modal.Footer>
        </Modal>
    </>);
};

export default EPTWModal;
