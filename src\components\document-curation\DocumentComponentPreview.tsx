import React from "react";
import {
  Heading1,
  Heading2,
  <PERSON>,
  List,
  ListOrdered,
  Quote,
  Minus,
  Image as ImageIcon,
  FileText,
  Link as LinkIcon
} from "lucide-react";
import ImageComponent from '@/components/common/ImageComponent';

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface DocumentComponentPreviewProps {
  component: DocumentComponent;
}

const DocumentComponentPreview: React.FC<DocumentComponentPreviewProps> = ({ component }) => {
  const getComponentIcon = () => {
    const iconProps = { className: "h-4 w-4" };

    switch (component.type) {
      case 'document-header':
        return <Heading1 {...iconProps} />;
      case 'section-header':
        return <Heading2 {...iconProps} />;
      case 'paragraph':
        return <Type {...iconProps} />;
      case 'bullet-list':
        return <List {...iconProps} />;
      case 'numbered-list':
        return <ListOrdered {...iconProps} />;
      case 'quote':
        return <Quote {...iconProps} />;
      case 'separator':
        return <Minus {...iconProps} />;
      case 'image':
        return <ImageIcon {...iconProps} />;
      case 'file-attachment':
        return <FileText {...iconProps} />;
      case 'link':
        return <LinkIcon {...iconProps} />;
      default:
        return <Type {...iconProps} />;
    }
  };

  const getComponentLabel = () => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image/File Upload',
      'file-attachment': 'File Attachment',
      'link': 'Link'
    };
    return labels[component.type] || component.type;
  };

  const renderPreview = () => {
    const content = component.content;

    switch (component.type) {
      case 'document-header':
        return (
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
              {content.text || 'Document Title'}
            </h1>
          </div>
        );

      case 'section-header':
        return (
          <div className="mb-4">
            <h2 className="text-2xl font-semibold text-slate-800 dark:text-slate-200">
              {content.text || 'Section Header'}
            </h2>
          </div>
        );

      case 'paragraph':
        return (
          <div className="mb-4">
            <p className="text-base text-slate-700 dark:text-slate-300 leading-relaxed">
              {content.text || 'This is a paragraph of text content. You can add any text here to describe information, provide instructions, or share details.'}
            </p>
          </div>
        );

      case 'bullet-list':
        return (
          <div className="mb-4">
            <ul className="list-disc list-inside space-y-2 text-slate-700 dark:text-slate-300">
              {content.items && content.items.length > 0 ? (
                content.items.map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))
              ) : (
                <>
                  <li>First bullet point</li>
                  <li>Second bullet point</li>
                  <li>Third bullet point</li>
                </>
              )}
            </ul>
          </div>
        );

      case 'numbered-list':
        return (
          <div className="mb-4">
            <ol className="list-decimal list-inside space-y-2 text-slate-700 dark:text-slate-300">
              {content.items && content.items.length > 0 ? (
                content.items.map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))
              ) : (
                <>
                  <li>First numbered item</li>
                  <li>Second numbered item</li>
                  <li>Third numbered item</li>
                </>
              )}
            </ol>
          </div>
        );

      case 'quote':
        return (
          <div className="mb-4">
            <blockquote className="border-l-4 border-blue-500 pl-4 py-2 bg-slate-50 dark:bg-slate-800 rounded-r">
              <p className="text-slate-700 dark:text-slate-300 italic">
                {content.text || 'This is a quote block. Use it to highlight important information or quotes.'}
              </p>
              {content.author && (
                <cite className="text-sm text-slate-500 dark:text-slate-400 block mt-2">
                  — {content.author}
                </cite>
              )}
            </blockquote>
          </div>
        );

      case 'separator':
        return (
          <div className="mb-6">
            <hr className="border-slate-300 dark:border-slate-600" />
          </div>
        );

      case 'image':
        return (
          <div className="mb-4">
            {(content.filename || content.files) ? (
              <div className="text-center">
                <div className="inline-block border border-gray-200 rounded-lg p-2 bg-gray-50">
                  <ImageComponent
                    fileName={content.filename || content.files}
                    size="32"
                    name={true}
                  />
                </div>
                {content.caption && (
                  <p className="text-sm text-gray-600 mt-2 italic text-center">{content.caption}</p>
                )}
              </div>
            ) : (
              <div className="aspect-video bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
                <div className="text-center">
                  <ImageIcon className="h-12 w-12 text-slate-400 mx-auto mb-2" />
                  <p className="text-sm text-slate-500">Image placeholder</p>
                </div>
              </div>
            )}
          </div>
        );

      case 'file-attachment':
        return (
          <div className="mb-4">
            {(content.filename || content.files) ? (
              <div className="text-center">
                <div className="inline-block border border-gray-200 rounded-lg p-2 bg-gray-50">
                  <ImageComponent
                    fileName={content.filename || content.files}
                    size="32"
                    name={true}
                  />
                </div>
                {content.description && (
                  <p className="text-sm text-gray-600 mt-2 text-center">{content.description}</p>
                )}
              </div>
            ) : (
              <div className="border border-slate-300 dark:border-slate-600 rounded-lg p-4 bg-slate-50 dark:bg-slate-800">
                <div className="flex items-center gap-3">
                  <FileText className="h-8 w-8 text-slate-400" />
                  <div>
                    <p className="font-medium text-slate-700 dark:text-slate-300">File Attachment</p>
                    <p className="text-sm text-slate-500">No file uploaded</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case 'link':
        return (
          <div className="mb-4">
            <a 
              href={content.url || '#'} 
              className="inline-flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              <LinkIcon className="h-4 w-4" />
              {content.text || content.url || 'Link text'}
            </a>
          </div>
        );

      default:
        return (
          <div className="mb-4">
            <div className="text-sm text-slate-500 dark:text-slate-400 italic">
              {getComponentLabel()} preview
            </div>
          </div>
        );
    }
  };

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 overflow-hidden">
      {renderPreview()}
    </div>
  );
};

export default DocumentComponentPreview;
