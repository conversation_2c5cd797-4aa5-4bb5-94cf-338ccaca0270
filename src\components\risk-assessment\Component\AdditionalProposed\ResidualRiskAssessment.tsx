import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface SeverityOption {
  value: string;
  label: string;
}

interface LikelihoodOption {
  value: string;
  label: string;
}

interface SeverityData {
  id: string;
  severity: string;
  personnel: string;
  property: string;
  environment: string;
  serviceLoss: string;
}

interface LevelData {
  level: string;
  descriptor: string;
  detailedDescription: string;
}

interface TableData {
  id: string;
  severity: string;
  rare: string;
  unlikely: string;
  possible: string;
  likely: string;
  almostCertain: string;
}

interface TaskItem {
  severity: string | number;
  likelyhood: string | number;
  [key: number]: any;
}

interface ResidualRiskAssessmentProps {
  item: TaskItem[];
  severity: SeverityOption[];
  severityData: SeverityData[];
  likelyhood: LikelihoodOption[];
  levelData: LevelData[];
  tableData: TableData[];
  severityTable: boolean;
  likelyhoodTable: boolean;
  riskTable: boolean;
  setSeverityTable: (value: boolean) => void;
  setLikelyhoodTable: (value: boolean) => void;
  setRiskTable: (value: boolean) => void;
  onChangeSeverity: (option: { value: string; label: string }, type: string) => void;
  onChangeLikelyhood: (option: { value: string; label: string }, type: string) => void;
  cellClassName: (value: number) => string;
  cellStyle: (data: any, field: string) => string;
  rowClassName: (data: any) => string;
  required: boolean;
}

const ResidualRiskAssessment: React.FC<ResidualRiskAssessmentProps> = ({
  item,
  severity,
  severityData,
  likelyhood,
  levelData,
  tableData,
  severityTable,
  likelyhoodTable,
  riskTable,
  setSeverityTable,
  setLikelyhoodTable,
  setRiskTable,
  onChangeSeverity,
  onChangeLikelyhood,
  cellClassName,
  cellStyle,
  rowClassName,
  required
}) => {
  const findMatrixValue = (idValue: string | number, columnValue: string | number): string => {
    const columnMap: Record<number, keyof TableData> = {
      1: 'rare',
      2: 'unlikely',
      3: 'possible',
      4: 'likely',
      5: 'almostCertain'
    };

    const columnKey = columnMap[Number(columnValue)];
    const row = tableData.find(item => item.id.startsWith(`${idValue}(`));

    if (row && row[columnKey]) {
      return row[columnKey] as string;
    }

    return '0';
  };

  return (
    <div className="space-y-6">
      <div>
        <h5 className="text-lg font-semibold">Residual Risk Assessment</h5>
        <p className="text-sm text-muted-foreground">
          Expected risk based on the implementation of the identified additional controls
        </p>
      </div>

      {/* Severity Section */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
            <div className="md:col-span-2">
              <h6 className="font-semibold">Severity</h6>
              <p className="text-sm text-muted-foreground">
                Degree of harm or impact that could result from a hazardous event or situation
              </p>
            </div>
            <div>
              <Select
                value={String(item[7]?.severity)}
                onValueChange={(value) => {
                  const option = severity.find(s => s.value === value);
                  if (option) onChangeSeverity(option, 'reassessment');
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select severity" />
                </SelectTrigger>
                <SelectContent>
                  {severity.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Collapsible open={severityTable} onOpenChange={setSeverityTable}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="mt-4 p-0 h-auto font-normal">
                Understand Severity Levels
                {severityTable ? (
                  <ChevronUp className="ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Severity Level</TableHead>
                      <TableHead>Descriptor</TableHead>
                      <TableHead>Personnel</TableHead>
                      <TableHead>Equipment / Property</TableHead>
                      <TableHead>Environment</TableHead>
                      <TableHead>Service Loss</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {severityData.map((row, index) => (
                      <TableRow key={index}>
                        <TableCell>{row.id}</TableCell>
                        <TableCell>{row.severity}</TableCell>
                        <TableCell>{row.personnel}</TableCell>
                        <TableCell>{row.property}</TableCell>
                        <TableCell>{row.environment}</TableCell>
                        <TableCell>{row.serviceLoss}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Likelihood Section */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
            <div className="md:col-span-2">
              <h6 className="font-semibold">Likelihood</h6>
              <p className="text-sm text-muted-foreground">
                Frequency with which a hazardous event or situation could happen
              </p>
            </div>
            <div>
              <Select
                value={String(item[7]?.likelyhood)}
                onValueChange={(value) => {
                  const option = likelyhood.find(l => l.value === value);
                  if (option) onChangeLikelyhood(option, 'reassessment');
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select likelihood" />
                </SelectTrigger>
                <SelectContent>
                  {likelyhood.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Collapsible open={likelyhoodTable} onOpenChange={setLikelyhoodTable}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="mt-4 p-0 h-auto font-normal">
                Understand Likelihood Levels
                {likelyhoodTable ? (
                  <ChevronUp className="ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Level</TableHead>
                      <TableHead>Descriptor</TableHead>
                      <TableHead>Detailed Description</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {levelData.map((row, index) => (
                      <TableRow key={index}>
                        <TableCell>{row.level}</TableCell>
                        <TableCell>{row.descriptor}</TableCell>
                        <TableCell>{row.detailedDescription}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Risk Level Section */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
            <div className="md:col-span-2">
              <h6 className="font-semibold">Risk Level</h6>
            </div>
            <div>
              <div className={`p-4 text-center font-semibold rounded-lg border ${cellClassName(Number(item[7]?.severity) * Number(item[7]?.likelyhood))}`}>
                {findMatrixValue(item[7]?.severity, item[7]?.likelyhood)}
              </div>
            </div>
          </div>

          <Collapsible open={riskTable} onOpenChange={setRiskTable}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="mt-4 p-0 h-auto font-normal">
                Understand Risk Levels
                {riskTable ? (
                  <ChevronUp className="ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-4">
              <div className="border rounded-lg overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-32"></TableHead>
                      <TableHead className="w-32"></TableHead>
                      <TableHead className="w-32 text-center">1 Rare</TableHead>
                      <TableHead className="w-32 text-center">2 Unlikely</TableHead>
                      <TableHead className="w-32 text-center">3 Possible</TableHead>
                      <TableHead className="w-32 text-center">4 Likely</TableHead>
                      <TableHead className="w-32 text-center">5 Almost Certain</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {tableData.map((row, index) => (
                      <TableRow key={index}>
                        <TableCell className="text-center">{row.id}</TableCell>
                        <TableCell className="text-center">{row.severity}</TableCell>
                        <TableCell className={`text-center ${cellStyle(row, 'rare')}`}>{row.rare}</TableCell>
                        <TableCell className={`text-center ${cellStyle(row, 'unlikely')}`}>{row.unlikely}</TableCell>
                        <TableCell className={`text-center ${cellStyle(row, 'possible')}`}>{row.possible}</TableCell>
                        <TableCell className={`text-center ${cellStyle(row, 'likely')}`}>{row.likely}</TableCell>
                        <TableCell className={`text-center ${cellStyle(row, 'almostCertain')}`}>{row.almostCertain}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>
    </div>
  );
};

export default ResidualRiskAssessment;
