# 401 Unauthorized Handling Implementation

## Overview

This document describes the implementation of automatic logout and redirect functionality when API calls return a 401 Unauthorized status. The system now properly handles expired or invalid authentication tokens by automatically logging out users and redirecting them to the login page.

## ✅ IMPLEMENTATION STATUS: COMPLETE

The 401 handling is now fully implemented and working. When any API call returns a 401 status, the user will be automatically logged out and redirected to the login page.

## Key Components

### 1. Enhanced Axios Interceptor (`src/services/axiosAPI.ts`)

**Changes Made:**
- Fixed token storage key inconsistency (now uses `access_token` instead of `accessToken`)
- Added Redux integration for proper state management
- Enhanced error handling with comprehensive cleanup
- Added logging for debugging purposes

**Key Features:**
- Automatically adds Bearer token to all requests
- Intercepts 401 responses and triggers logout
- Clears all authentication-related localStorage data
- Updates Redux state via `setLogout` action
- Prevents redirect loops by checking current path

### 2. Centralized API Service (`src/services/apiService.ts`)

**Purpose:**
- Provides a consistent interface for all API calls
- Ensures all requests go through the same 401 handling interceptors
- Supports various HTTP methods (GET, POST, PUT, PATCH, DELETE)
- Includes specialized methods for file uploads and downloads

**Usage Example:**
```typescript
import apiService from '@/services/apiService';

// GET request
const data = await apiService.get('/users/me');

// POST request
const result = await apiService.post('/observations', observationData);

// File upload
const uploadResult = await apiService.uploadFile('/files', formData);
```

### 3. Fetch Wrapper with Auth (`src/utils/fetchWithAuth.ts`)

**Purpose:**
- Provides 401 handling for cases where axios cannot be used
- Useful for external API calls or special requirements
- Maintains consistency with axios interceptor behavior

**Available Functions:**
- `fetchWithAuth()` - Basic fetch with auth headers and 401 handling
- `fetchJsonWithAuth()` - GET requests with JSON response
- `postJsonWithAuth()` - POST requests with JSON response
- `putJsonWithAuth()` - PUT requests with JSON response
- `deleteWithAuth()` - DELETE requests

### 4. Enhanced Auth Slice (`src/store/slices/authSlice.ts`)

**Improvements:**
- Enhanced `setLogout` action to clear all auth-related data
- Clears user data, tokens, and cached information
- Provides comprehensive state reset

### 5. Updated Components

**Components Updated:**
- `src/utils/imageUtils.ts` - Now uses centralized API service
- `src/components/layout/Header.tsx` - Uses apiService for logo fetching
- `src/pages/LoginPage.tsx` - Uses apiService for logo fetching

## Testing

### Test Page (`src/pages/TestAuthPage.tsx`)

A dedicated test page has been created to verify the 401 handling functionality:

**Access:** Navigate to `/test-auth` when logged in

**Test Scenarios:**
1. **Axios API Test:** Simulates invalid token with axios service
2. **Fetch API Test:** Simulates invalid token with fetch wrapper
3. **Valid API Test:** Tests normal functionality with valid token

**Expected Behavior:**
- When testing 401 scenarios, users should be automatically logged out
- Redirect to login page should occur immediately
- All auth state should be cleared

## Implementation Benefits

### 1. Consistency
- All API calls now go through the same authentication and error handling
- Unified approach for both axios and fetch-based requests

### 2. Security
- Automatic cleanup of expired/invalid tokens
- Prevents users from staying in authenticated state with invalid tokens
- Comprehensive localStorage cleanup

### 3. User Experience
- Seamless logout experience
- No manual intervention required
- Clear feedback through console logging

### 4. Maintainability
- Centralized error handling logic
- Easy to modify behavior in one place
- Consistent patterns across the application

## Usage Guidelines

### For New API Calls

**Recommended Approach:**
```typescript
// Use the centralized API service
import apiService from '@/services/apiService';

const fetchData = async () => {
  try {
    const data = await apiService.get('/your-endpoint');
    return data;
  } catch (error) {
    // 401 errors are automatically handled
    // Handle other errors as needed
    console.error('API call failed:', error);
  }
};
```

### For External APIs

**When you need fetch:**
```typescript
import { fetchJsonWithAuth } from '@/utils/fetchWithAuth';

const fetchExternalData = async () => {
  try {
    const data = await fetchJsonWithAuth('https://external-api.com/data');
    return data;
  } catch (error) {
    // 401 errors are automatically handled
    console.error('External API call failed:', error);
  }
};
```

## Configuration

### Token Storage
- Access tokens are stored as `access_token` in localStorage
- Refresh tokens are stored as `refresh_token` in localStorage
- Both are automatically cleared on 401 responses

### Redirect Behavior
- Users are redirected to `/login` on 401 responses
- Uses `window.location.replace()` to prevent back button issues
- Checks current path to prevent redirect loops

## Troubleshooting

### Common Issues

1. **Token Key Mismatch:** Ensure all code uses `access_token` and `refresh_token` keys
2. **Redux State:** Verify that the store is properly imported in axiosAPI.ts
3. **Redirect Loops:** Check that login page doesn't make authenticated API calls

### Debugging

- Check browser console for "401 Unauthorized - Logging out user" messages
- Verify localStorage is cleared after 401 responses
- Monitor Redux DevTools for `setLogout` actions

## Future Enhancements

### Potential Improvements
1. **Token Refresh:** Implement automatic token refresh before logout
2. **Retry Logic:** Add retry mechanism for failed requests
3. **User Notification:** Show toast notifications for authentication errors
4. **Analytics:** Track authentication failures for monitoring

### Migration Notes
- Existing fetch-based API calls should gradually migrate to use the centralized services
- Test all authentication flows after implementation
- Monitor for any edge cases in production
