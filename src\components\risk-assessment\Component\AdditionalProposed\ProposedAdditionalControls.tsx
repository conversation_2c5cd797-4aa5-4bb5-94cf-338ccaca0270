import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Plus, Trash2, Settings, User, Clock, AlertCircle, PlusCircle } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';

interface ControlOption {
  value: string;
  label: string;
}

interface ResponsibilityOption {
  id: string;
  name: string;
  firstName?: string;
}

interface ControlItem {
  current_type: string;
  value: string;
  person: ResponsibilityOption;
  date: Date | null;
}

interface TaskItem {
  option: ControlItem[];
  [key: number]: any;
}

interface ProposedAdditionalControlsProps {
  item: TaskItem[];
  control: ControlOption[];
  responsibility: ResponsibilityOption[];
  onControlAddion: (value: string, index: number) => void;
  onControlAddionText: (value: string, index: number) => void;
  onDeleteConseq: (index: number, type: string) => void;
  onResponsePerson: (person: ResponsibilityOption, index: number) => void;
  onResponseDate: (date: Date | null, index: number) => void;
  addAdditionalControl: () => void;
  required: boolean;
}

const ProposedAdditionalControls: React.FC<ProposedAdditionalControlsProps> = ({
  item,
  control,
  responsibility,
  onControlAddion,
  onControlAddionText,
  onDeleteConseq,
  onResponsePerson,
  onResponseDate,
  addAdditionalControl,
  required
}) => {
  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-green-100 rounded-full">
            <PlusCircle className="w-5 h-5 text-green-600" />
          </div>
          <div>
            <h5 className="text-xl font-semibold text-gray-900">Proposed Additional Controls</h5>
            <p className="text-sm text-gray-600">Define additional control measures to enhance risk management</p>
          </div>
        </div>
      </div>

      {item[6]?.option?.map((controlItem, i) => (
        <div key={i} className="relative bg-gradient-to-br from-white to-green-50/30 border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
          {/* Header with control number */}
          <div className="bg-gradient-to-r from-green-50 to-teal-50 border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex items-center justify-center w-8 h-8 bg-green-100 rounded-full">
                  <Settings className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Additional Control #{i + 1}</h3>
                  <p className="text-sm text-gray-600">Proposed Control Measure</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDeleteConseq(i, 'responsibility')}
                className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full p-3 h-10 w-10 flex items-center justify-center"
              >
                <Trash2 className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Main content */}
          <div className="p-6 space-y-6">
            {/* Control Definition Section */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Control Type */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                    <Settings className="w-4 h-4 text-green-500" />
                    <span>Control Type</span>
                  </Label>
                  <Select
                    value={controlItem.current_type}
                    onValueChange={(value) => onControlAddion(value, i)}
                  >
                    <SelectTrigger className="h-11 border-gray-300 hover:border-gray-400 transition-colors">
                      <SelectValue placeholder="Select control type" />
                    </SelectTrigger>
                    <SelectContent>
                      {control.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Purpose of Control */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4 text-blue-500" />
                    <span>Purpose of Control</span>
                  </Label>
                  <Input
                    value={controlItem.value}
                    onChange={(e) => onControlAddionText(e.target.value, i)}
                    placeholder="Enter purpose of control"
                    className="h-11 border-gray-300 hover:border-gray-400 transition-colors"
                  />
                </div>
              </div>
            </div>

            {/* Assignment Section */}
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-4 flex items-center space-x-2">
                <User className="w-4 h-4 text-purple-500" />
                <span>Assignment & Timeline</span>
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Responsibility */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                    <User className="w-4 h-4 text-purple-500" />
                    <span>Responsible Person</span>
                  </Label>
                  <Select
                    value={controlItem.person?.id}
                    onValueChange={(value) => {
                      const person = responsibility.find(r => r.id === value);
                      if (person) onResponsePerson(person, i);
                    }}
                  >
                    <SelectTrigger className="h-11 border-gray-300 hover:border-gray-400 transition-colors">
                      <SelectValue placeholder="Select responsible person" />
                    </SelectTrigger>
                    <SelectContent>
                      {responsibility.map((person) => (
                        <SelectItem key={person.id} value={person.id}>
                          {person.firstName || person.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Target Date */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-orange-500" />
                    <span>Target Date</span>
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full h-11 justify-start text-left font-normal border-gray-300 hover:border-gray-400 transition-colors",
                          !controlItem.date && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {controlItem.date ? format(controlItem.date, "dd/MM/yyyy") : "Select target date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={controlItem.date}
                        onSelect={(date) => onResponseDate(date || null, i)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}

      {/* Add Control Button */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={addAdditionalControl}
          className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-300 text-green-700 hover:bg-gradient-to-r hover:from-green-100 hover:to-emerald-100 hover:border-green-400 transition-all duration-200 px-6 py-3 h-12"
        >
          <PlusCircle className="h-5 w-5 mr-2" />
          Add Additional Control
        </Button>
      </div>
    </div>
  );
};

export default ProposedAdditionalControls;
