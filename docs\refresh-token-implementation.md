# Refresh Token Implementation

This document describes the implementation of automatic token refresh functionality for handling 401 Unauthorized errors.

## Overview

The application now automatically attempts to refresh expired access tokens when receiving 401 errors from API calls. This provides a seamless user experience by avoiding unnecessary logouts when tokens expire.

## Implementation Details

### 1. Token Refresh Utility (`src/utils/tokenRefresh.ts`)

Contains utility functions for:
- `refreshAccessToken()`: Handles the token refresh process
- `handleTokenRefreshFailure()`: Manages logout when refresh fails
- `isTokenExpired()`: Basic token expiration check (placeholder for future JWT validation)

### 2. Axios Interceptor (`src/services/axiosAPI.ts`)

The response interceptor now:
1. Detects 401 errors on API requests
2. Attempts to refresh the access token using the refresh token
3. Retries the original request with the new access token
4. Falls back to logout if refresh fails

### 3. Fetch Wrapper (`src/utils/fetchWithAuth.ts`)

Enhanced to include the same refresh logic for non-axios API calls.

### 4. Login Process (`src/pages/LoginPage.tsx`)

Updated to store Cognito configuration in localStorage for use during token refresh:
- `COGNITO_USER_DOMAIN`
- `COGNITO_USER_APP_CLIENT_ID`

### 5. Auth Slice (`src/store/slices/authSlice.ts`)

Updated logout action to clear Cognito configuration from localStorage.

## Flow Diagram

```
API Request → 401 Error → Check Refresh Token
                ↓
        Has Refresh Token?
                ↓
            Yes → Refresh Token Request
                ↓
        Refresh Successful?
                ↓
            Yes → Update Tokens → Retry Original Request
                ↓
            No → Logout User → Redirect to Login
```

## Configuration Requirements

For the refresh token functionality to work, the following must be stored in localStorage during login:
- `access_token`: Current access token
- `refresh_token`: Refresh token from Cognito
- `COGNITO_USER_DOMAIN`: Cognito domain URL
- `COGNITO_USER_APP_CLIENT_ID`: Cognito app client ID

## Error Handling

The implementation handles various error scenarios:
1. **Missing refresh token**: Immediate logout
2. **Missing Cognito configuration**: Immediate logout
3. **Refresh request failure**: Logout after failed attempt
4. **Network errors during refresh**: Logout after failed attempt

## Security Considerations

- Refresh tokens are stored in localStorage (consider httpOnly cookies for production)
- Failed refresh attempts immediately clear all tokens
- Only one retry attempt is made to prevent infinite loops
- All sensitive data is cleared on logout

## Usage

The refresh functionality is automatic and transparent to the application code. Simply use the existing API service methods:

```typescript
import apiService from '@/services/apiService';

// This will automatically handle token refresh if needed
const data = await apiService.get('/users/me');
```

## Testing

To test the refresh functionality:
1. Make an API call with an expired access token
2. Verify that the token is automatically refreshed
3. Verify that the original request succeeds with the new token
4. Test failure scenarios (invalid refresh token, network errors)

## Future Enhancements

- Implement JWT token expiration checking to proactively refresh tokens
- Add token refresh queue to handle multiple simultaneous requests
- Consider moving tokens to httpOnly cookies for better security
- Add retry logic with exponential backoff for network failures
