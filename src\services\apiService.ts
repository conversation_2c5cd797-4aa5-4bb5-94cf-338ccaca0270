import API from './axiosAPI';
import { AxiosResponse } from 'axios';

/**
 * Centralized API service that uses the axios instance with 401 handling
 * This ensures all API calls go through the same interceptors
 */
class ApiService {
  /**
   * Generic GET request
   */
  async get<T = any>(url: string, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.get(url, config);
    return response.data;
  }

  /**
   * Generic POST request
   */
  async post<T = any>(url: string, data?: any, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.post(url, data, config);
    return response.data;
  }

  /**
   * Generic PUT request
   */
  async put<T = any>(url: string, data?: any, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.put(url, data, config);
    return response.data;
  }

  /**
   * Generic PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.patch(url, data, config);
    return response.data;
  }

  /**
   * Generic DELETE request
   */
  async delete<T = any>(url: string, config?: any): Promise<T> {
    const response: AxiosResponse<T> = await API.delete(url, config);
    return response.data;
  }

  /**
   * Upload file with FormData
   */
  async uploadFile<T = any>(url: string, formData: FormData): Promise<T> {
    const response: AxiosResponse<T> = await API.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Download file as blob
   */
  async downloadBlob(url: string): Promise<Blob> {
    const response: AxiosResponse<Blob> = await API.get(url, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Get request that returns the full axios response (for cases where you need headers, status, etc.)
   */
  async getResponse<T = any>(url: string, config?: any): Promise<AxiosResponse<T>> {
    return await API.get(url, config);
  }

  /**
   * Post request that returns the full axios response
   */
  async postResponse<T = any>(url: string, data?: any, config?: any): Promise<AxiosResponse<T>> {
    return await API.post(url, data, config);
  }
}

// Export a singleton instance
export const apiService = new ApiService();
export default apiService;
