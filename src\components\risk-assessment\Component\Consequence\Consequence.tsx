import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Trash2, Upload, AlertTriangle, FileImage, Target, AlertCircle } from 'lucide-react';
import ImageComponent from '@/components/common/ImageComponent';

interface ImpactOption {
  label: string;
  value: string;
}

interface ConsequenceItem {
  current_type: string;
  value: string;
  files: string[];
}

interface ConsequenceProps {
  con: ConsequenceItem;
  i: number;
  impactOn: ImpactOption[];
  onImapactOn: (value: string, index: number, type: string) => void;
  onConseqText: (value: string, index: number, type: string) => void;
  onDeleteConseq: (index: number, type: string) => void;
  handleTaskFileChange: (files: File[], index: number, type: string) => void;
  required: boolean;
  type: string;
  handleRemoveImage: (imageIndex: number, consequenceIndex: number, type: string) => void;
}

const Consequence: React.FC<ConsequenceProps> = ({
  con,
  i,
  impactOn,
  onImapactOn,
  onConseqText,
  onDeleteConseq,
  handleTaskFileChange,
  required,
  type,
  handleRemoveImage
}) => {
  const hasImpactError = required === false && con.current_type === '';
  const hasDescriptionError = required === false && con.value === '';
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const files = Array.from(e.dataTransfer.files);
      // Filter for image files only
      const imageFiles = files.filter(file =>
        file.type.startsWith('image/') &&
        ['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)
      );

      if (imageFiles.length > 0) {
        handleTaskFileChange(imageFiles, i, 'consequence');
      }
    }
  };

  console.log(con.files);

  return (
    <div className="relative bg-gradient-to-br from-white to-orange-50/30 border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
      {/* Header with consequence number and status indicator */}
      <div className="bg-gradient-to-r from-orange-50 to-red-50 border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-8 h-8 bg-orange-100 rounded-full">
              <AlertTriangle className="w-4 h-4 text-orange-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Consequence #{i + 1}</h3>
              <p className="text-sm text-gray-600">Potential Impact Assessment</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDeleteConseq(i, 'consequence')}
            className="text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full p-3 h-10 w-10 flex items-center justify-center"
          >
            <Trash2 className="h-5 w-5" />
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="p-6 space-y-6">
        {/* Impact Assessment Section */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Impact Type */}
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                <Target className="w-4 h-4 text-orange-500" />
                <span>Impact On</span>
                {hasImpactError && <AlertCircle className="w-4 h-4 text-red-500" />}
              </Label>
              <Select
                value={con.current_type}
                onValueChange={(value) => onImapactOn(value, i, 'consequence')}
              >
                <SelectTrigger className={`h-11 ${hasImpactError ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'} transition-colors`}>
                  <SelectValue placeholder="Select impact type" />
                </SelectTrigger>
                <SelectContent>
                  {impactOn.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {hasImpactError && (
                <p className="text-xs text-red-600 flex items-center space-x-1">
                  <AlertCircle className="w-3 h-3" />
                  <span>Please select an impact type</span>
                </p>
              )}
            </div>

            {/* Spacer for better layout */}
            <div></div>
          </div>

          {/* Description Section */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
              <FileImage className="w-4 h-4 text-purple-500" />
              <span>Consequence Description</span>
              {hasDescriptionError && <AlertCircle className="w-4 h-4 text-red-500" />}
            </Label>
            <Textarea
              value={con.value}
              onChange={(e) => onConseqText(e.target.value, i, 'consequence')}
              placeholder="Provide a detailed description of the potential consequences..."
              className={`min-h-[100px] resize-none ${hasDescriptionError ? 'border-red-300 bg-red-50' : 'border-gray-300 hover:border-gray-400'} transition-colors`}
              rows={4}
            />
            {hasDescriptionError && (
              <p className="text-xs text-red-600 flex items-center space-x-1">
                <AlertCircle className="w-3 h-3" />
                <span>Please provide a consequence description</span>
              </p>
            )}
          </div>
        </div>

        {/* Image Upload Section */}
        <div className="space-y-4">
          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 border border-orange-200">
            <Label htmlFor="imageUploads" className="text-sm font-medium text-gray-700 flex items-center space-x-2 mb-3">
              <FileImage className="w-4 h-4 text-orange-500" />
              <span>Supporting Images</span>
            </Label>
            <p className="text-sm text-gray-600 mb-4 leading-relaxed">
              {type === 'hazard'
                ? 'Upload images to illustrate these points. These images will be used to visually communicate the risks and their potential consequences to relevant personnel.'
                : 'Upload relevant images to visually represent the identified consequences for this sub-activity. These images may also be utilized in other applicable modules to support effective risk communication.'
              }
            </p>

            <div
              className={`relative border-2 border-dashed rounded-xl p-8 transition-all duration-300 cursor-pointer group ${
                isDragOver
                  ? 'border-orange-400 bg-orange-50 scale-[1.02]'
                  : 'border-gray-300 bg-white hover:border-orange-300 hover:bg-orange-25'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => document.getElementById(`file-upload-${i}`)?.click()}
            >
              <div className="flex flex-col items-center justify-center space-y-4">
                <div className={`p-4 rounded-full transition-colors ${
                  isDragOver ? 'bg-orange-100' : 'bg-gray-100 group-hover:bg-orange-100'
                }`}>
                  <Upload className={`h-8 w-8 transition-colors ${
                    isDragOver ? 'text-orange-600' : 'text-gray-400 group-hover:text-orange-500'
                  }`} />
                </div>

                <div className="text-center space-y-2">
                  <p className={`text-lg font-medium transition-colors ${
                    isDragOver ? 'text-orange-700' : 'text-gray-700'
                  }`}>
                    {isDragOver ? 'Drop files here' : 'Upload Consequence Images'}
                  </p>
                  <p className="text-sm text-gray-500">
                    Drag and drop files here, or click to browse
                  </p>
                  <div className="flex items-center justify-center space-x-4 text-xs text-gray-400">
                    <span>Max 5 files</span>
                    <span>•</span>
                    <span>Up to 100MB each</span>
                    <span>•</span>
                    <span>JPEG, PNG only</span>
                  </div>
                </div>

                <Input
                  type="file"
                  multiple
                  accept="image/jpeg,image/png,image/jpg"
                  onChange={(e) => {
                    if (e.target.files && e.target.files.length > 0) {
                      const files = Array.from(e.target.files);
                      handleTaskFileChange(files, i, 'consequence');
                      e.target.value = ''; // Reset input
                    }
                  }}
                  className="hidden"
                  id={`file-upload-${i}`}
                />

                {!isDragOver && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="border-orange-300 text-orange-600 hover:bg-orange-50 hover:border-orange-400"
                    onClick={(e) => {
                      e.stopPropagation();
                      document.getElementById(`file-upload-${i}`)?.click();
                    }}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Browse Files
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Uploaded Images Gallery */}
          {con.files && con.files.length > 0 && (
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700 flex items-center space-x-2">
                <FileImage className="w-4 h-4 text-green-500" />
                <span>Uploaded Images ({con.files.length})</span>
              </Label>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {con.files.map((item, m) => (
                  <div key={m} className="group relative">
                    <div className="relative bg-white border border-gray-200 rounded-lg p-3 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
                      <div className="aspect-square rounded-md overflow-hidden bg-gray-50">
                        <ImageComponent fileName={item} size={'100'} name={true} />
                      </div>

                      {/* Delete button with improved styling */}
                      <Button
                        variant="destructive"
                        size="sm"
                        className="absolute -top-0 -right-0 h-8 w-8 p-0 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-red-500 hover:bg-red-600 flex items-center justify-center"
                        onClick={() => handleRemoveImage(m, i, 'consequence')}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>

                      {/* Image overlay on hover */}
                      {/* <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-lg" /> */}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Consequence;
