import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Title, DialogDescription } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { X, FileText, Download, Printer } from "lucide-react";
import DocumentComponentPreview from "./DocumentComponentPreview";

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface DocumentPreviewProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  components: DocumentComponent[];
  documentTitle?: string;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({
  open,
  onOpenChange,
  components,
  documentTitle = "Document Preview"
}) => {
  const handlePrint = () => {
    window.print();
  };

  const handleDownload = () => {
    // This would typically generate a PDF or export functionality
    console.log("Download functionality would be implemented here");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl p-0 overflow-hidden h-[90vh] max-h-[800px]">
        <DialogHeader className="p-6 border-b bg-white dark:bg-slate-800">
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <div>
                <DialogTitle className="text-xl font-semibold">Document Preview</DialogTitle>
                <DialogDescription className="text-sm text-slate-600 dark:text-slate-400">
                  Preview how your document will appear when published
                </DialogDescription>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePrint}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                Print
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleDownload}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={() => onOpenChange(false)}
                className="h-8 w-8"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>

        {/* Document Preview Content */}
        <div className="flex-1 bg-slate-50 dark:bg-slate-900">
          <ScrollArea className="h-[calc(90vh-120px)] max-h-[680px]">
            <div className="p-8">
              {/* Document Paper Container */}
              <div className="max-w-4xl mx-auto bg-white dark:bg-slate-800 shadow-lg rounded-lg overflow-hidden">
                {/* Document Header */}
                <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-6 border-b border-slate-200 dark:border-slate-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                        {documentTitle}
                      </h1>
                      <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                        Generated on {new Date().toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-slate-500 dark:text-slate-400">
                        {components.length} component{components.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Document Content */}
                <div className="p-8">
                  {components.length > 0 ? (
                    <div className="space-y-6">
                      {components
                        .sort((a, b) => a.position - b.position)
                        .map((component) => (
                          <DocumentComponentPreview 
                            key={component.id} 
                            component={component} 
                          />
                        ))}
                    </div>
                  ) : (
                    <div className="text-center py-16">
                      <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-700 dark:to-slate-800 rounded-full flex items-center justify-center">
                        <FileText className="h-12 w-12 text-slate-400" />
                      </div>
                      <h3 className="text-xl font-semibold text-slate-700 dark:text-slate-300 mb-2">
                        No Content to Preview
                      </h3>
                      <p className="text-slate-500 dark:text-slate-400">
                        Add components to your document to see the preview
                      </p>
                    </div>
                  )}
                </div>

                {/* Document Footer */}
                <div className="bg-slate-50 dark:bg-slate-900 p-4 border-t border-slate-200 dark:border-slate-700">
                  <div className="flex justify-between items-center text-xs text-slate-500 dark:text-slate-400">
                    <div>
                      Document created with Acuizen Workhub
                    </div>
                    <div>
                      Page 1 of 1
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentPreview;
