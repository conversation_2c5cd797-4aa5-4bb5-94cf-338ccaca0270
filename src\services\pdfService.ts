import { Observation, Attachment } from '@/types/observation';
import { format } from 'date-fns';
import { fetchDataUrlForImage } from '@/utils/imageUtils';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { ObservationAction } from '@/services/api';

interface PDFGenerationOptions {
  observation: Observation;
  logoUrl?: string;
  companyName?: string;
  actions?: ObservationAction[];
}

export class PDFService {
  private static async generateHTML(options: PDFGenerationOptions): Promise<string> {
    const { observation, logoUrl, companyName = 'AcuiZen' } = options;

    // Process attachments to get data URLs
    const processedAttachments: string[] = [];
    if (observation.attachments && observation.attachments.length > 0) {
      for (const attachment of observation.attachments) {
        try {
          const fileName = typeof attachment === 'string' ? attachment : attachment.name;
          if (fileName) {
            const dataUrl = await fetchDataUrlForImage(fileName);
            if (dataUrl) {
              processedAttachments.push(dataUrl);
            }
          }
        } catch (error) {
          console.warn('Failed to process attachment:', error);
        }
      }
    }

    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Observation Report - ${observation.id}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 3px solid #0A5A8F;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 60px;
            height: 60px;
            object-fit: contain;
        }

        .company-info h1 {
            color: #0A5A8F;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .company-info p {
            color: #666;
            font-size: 14px;
        }

        .report-info {
            text-align: right;
        }

        .report-title {
            font-size: 20px;
            font-weight: 600;
            color: #0A5A8F;
            margin-bottom: 5px;
        }

        .generated-date {
            color: #666;
            font-size: 12px;
        }

        .section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #0A5A8F;
            border-bottom: 2px solid #E5E7EB;
            padding-bottom: 8px;
            margin-bottom: 15px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
            background: #F9FAFB;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #0A5A8F;
        }

        .info-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            margin-bottom: 5px;
        }

        .info-value {
            color: #111827;
            font-size: 16px;
            word-wrap: break-word;
        }

        .full-width {
            grid-column: span 2;
        }

        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-new { background: #FEF3C7; color: #92400E; }
        .status-open { background: #FED7AA; color: #9A3412; }
        .status-in-progress { background: #DBEAFE; color: #1E40AF; }
        .status-completed { background: #D1FAE5; color: #065F46; }
        .status-closed { background: #D1FAE5; color: #065F46; }
        .status-default { background: #F3F4F6; color: #374151; }

        .description-section {
            background: #F9FAFB;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #0A5A8F;
        }

        .description-text {
            font-size: 16px;
            line-height: 1.7;
            color: #374151;
        }

        .attachments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .attachment-item {
            border: 2px solid #E5E7EB;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .attachment-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
        }

        .attachment-caption {
            padding: 10px;
            background: #F9FAFB;
            font-size: 12px;
            color: #6B7280;
            text-align: center;
        }

        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #E5E7EB;
            text-align: center;
        }

        .footer-text {
            color: #6B7280;
            font-size: 12px;
            font-style: italic;
        }

        .confidential-notice {
            background: #FEF2F2;
            border: 1px solid #FECACA;
            border-radius: 6px;
            padding: 12px;
            margin-top: 10px;
        }

        .confidential-text {
            color: #B91C1C;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        @media print {
            body { print-color-adjust: exact; }
            .container { padding: 20px; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="logo-section">
                ${logoUrl ? `<img src="${logoUrl}" alt="Company Logo" class="logo">` : ''}
                <div class="company-info">
                    <h1>${companyName}</h1>
                    <p>Safety Observation Report</p>
                </div>
            </div>
            <div class="report-info">
                <div class="report-title">Observation Report</div>
                <div class="generated-date">Generated: ${format(new Date(), 'PPP')}</div>
            </div>
        </div>

        <!-- Basic Information -->
        <div class="section">
            <h2 class="section-title">Basic Information</h2>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Observation ID</div>
                    <div class="info-value">${observation.id}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        <span class="status-badge ${this.getStatusClass(observation.status)}">
                            ${observation.status}
                        </span>
                    </div>
                </div>
                <div class="info-item full-width">
                    <div class="info-label">Location</div>
                    <div class="info-value">${observation.location}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Category</div>
                    <div class="info-value">${observation.category}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Type</div>
                    <div class="info-value">${observation.type}</div>
                </div>
                <div class="info-item full-width">
                    <div class="info-label">Action/Condition</div>
                    <div class="info-value">${observation.actionCondition}</div>
                </div>
            </div>
        </div>

        <!-- Description -->
        <div class="section">
            <h2 class="section-title">Description</h2>
            <div class="description-section">
                <div class="description-text">${observation.description}</div>
            </div>
        </div>

        <!-- People Involved -->
        <div class="section">
            <h2 class="section-title">People Involved</h2>
            <div class="info-grid">
                <div class="info-item">
                    <div class="info-label">Reported By</div>
                    <div class="info-value">${observation.reportedBy}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Reported Date</div>
                    <div class="info-value">${format(observation.reportedDate, 'PPP')}</div>
                </div>
                ${observation.actionAssignee ? `
                <div class="info-item">
                    <div class="info-label">Action Assignee</div>
                    <div class="info-value">${observation.actionAssignee}</div>
                </div>
                ` : ''}
                ${observation.reviewedBy ? `
                <div class="info-item">
                    <div class="info-label">Reviewed By</div>
                    <div class="info-value">${observation.reviewedBy}</div>
                </div>
                ` : ''}
                ${observation.dueDate ? `
                <div class="info-item">
                    <div class="info-label">Due Date</div>
                    <div class="info-value">${format(observation.dueDate, 'PPP')}</div>
                </div>
                ` : ''}
            </div>
        </div>

        <!-- Attachments -->
        ${processedAttachments.length > 0 ? `
        <div class="section">
            <h2 class="section-title">Attachments</h2>
            <div class="attachments-grid">
                ${processedAttachments.map((attachment, index) => `
                    <div class="attachment-item">
                        <img src="${attachment}" alt="Attachment ${index + 1}" class="attachment-image">
                        <div class="attachment-caption">Attachment ${index + 1}</div>
                    </div>
                `).join('')}
            </div>
        </div>
        ` : ''}

        <!-- Footer -->
        <div class="footer">
            <div class="footer-text">
                This is an official safety observation record generated on ${format(new Date(), 'PPP')}
            </div>
            <div class="confidential-notice">
                <div class="confidential-text">
                    CONFIDENTIAL - This document contains sensitive safety information. Please maintain confidentiality.
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;

    return html;
  }

  private static getStatusClass(status: string): string {
    switch (status.toLowerCase()) {
      case 'new': return 'status-new';
      case 'open': return 'status-open';
      case 'in progress': return 'status-in-progress';
      case 'action in progress': return 'status-in-progress';
      case 'completed': return 'status-completed';
      case 'closed': return 'status-closed';
      default: return 'status-default';
    }
  }

  static async generatePDF(options: PDFGenerationOptions): Promise<Uint8Array> {
    try {
      // Create a temporary container for the HTML content
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'absolute';
      tempContainer.style.left = '-9999px';
      tempContainer.style.top = '-9999px';
      tempContainer.style.width = '800px';
      tempContainer.style.background = 'white';
      tempContainer.style.padding = '40px';
      tempContainer.style.fontFamily = 'Segoe UI, Tahoma, Geneva, Verdana, sans-serif';

      // Generate HTML content
      const html = await this.generateHTML(options);
      tempContainer.innerHTML = html;

      // Add to document temporarily
      document.body.appendChild(tempContainer);

      // Wait for images to load
      const images = tempContainer.querySelectorAll('img');
      await Promise.all(Array.from(images).map(img => {
        return new Promise((resolve) => {
          if (img.complete) {
            resolve(true);
          } else {
            img.onload = () => resolve(true);
            img.onerror = () => resolve(true);
          }
        });
      }));

      // Generate canvas from HTML
      const canvas = await html2canvas(tempContainer, {
        scale: 2,
        logging: false,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 800,
        height: tempContainer.scrollHeight
      });

      // Remove temporary container
      document.body.removeChild(tempContainer);

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 210; // A4 width in mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Add image to PDF, handling multiple pages if needed
      let heightLeft = imgHeight;
      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= 297; // A4 height in mm

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= 297;
      }

      return pdf.output('arraybuffer');

    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error('Failed to generate PDF');
    }
  }

  static async downloadPDF(options: PDFGenerationOptions, filename?: string): Promise<void> {
    try {
      const pdfArrayBuffer = await this.generatePDF(options);

      // Create blob and download
      const blob = new Blob([pdfArrayBuffer], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);

      const link = document.createElement('a');
      link.href = url;
      link.download = filename || `Observation_Report_${options.observation.id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up
      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('Error downloading PDF:', error);
      throw error;
    }
  }

  // Alternative method that creates a cleaner HTML structure for better PDF rendering
  static createPDFTemplate(options: PDFGenerationOptions): HTMLElement {
    const { observation, logoUrl, companyName = 'AcuiZen' } = options;

    const container = document.createElement('div');
    container.className = 'pdf-container';
    container.style.cssText = `
      width: 800px;
      background: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      padding: 40px;
      box-sizing: border-box;
    `;

    container.innerHTML = `
      <style>
        .pdf-container * { box-sizing: border-box; }
        .pdf-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 3px solid #0A5A8F;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .pdf-logo-section { display: flex; align-items: center; gap: 15px; }
        .pdf-logo { width: 60px; height: 60px; object-fit: contain; }
        .pdf-company-info h1 { color: #0A5A8F; font-size: 24px; font-weight: 700; margin: 0 0 5px 0; }
        .pdf-company-info p { color: #666; font-size: 14px; margin: 0; }
        .pdf-report-info { text-align: right; }
        .pdf-report-title { font-size: 20px; font-weight: 600; color: #0A5A8F; margin-bottom: 5px; }
        .pdf-generated-date { color: #666; font-size: 12px; }
        .pdf-section { margin-bottom: 30px; }
        .pdf-section-title {
          font-size: 18px;
          font-weight: 600;
          color: #0A5A8F;
          border-bottom: 2px solid #E5E7EB;
          padding-bottom: 8px;
          margin-bottom: 15px;
        }
        .pdf-info-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px; margin-bottom: 20px; }
        .pdf-info-item {
          background: #F9FAFB;
          padding: 15px;
          border-radius: 8px;
          border-left: 4px solid #0A5A8F;
        }
        .pdf-info-label { font-weight: 600; color: #374151; font-size: 14px; margin-bottom: 5px; }
        .pdf-info-value { color: #111827; font-size: 16px; word-wrap: break-word; }
        .pdf-full-width { grid-column: span 2; }
        .pdf-status-badge {
          display: inline-block;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
        .pdf-description-section {
          background: #F9FAFB;
          padding: 20px;
          border-radius: 8px;
          border-left: 4px solid #0A5A8F;
        }
        .pdf-description-text { font-size: 16px; line-height: 1.7; color: #374151; }
        .pdf-attachments-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 20px;
          margin-top: 15px;
        }
        .pdf-attachment-item {
          border: 2px solid #E5E7EB;
          border-radius: 8px;
          overflow: hidden;
          background: white;
        }
        .pdf-attachment-image { width: 100%; height: 150px; object-fit: cover; display: block; }
        .pdf-attachment-caption {
          padding: 10px;
          background: #F9FAFB;
          font-size: 12px;
          color: #6B7280;
          text-align: center;
        }
        .pdf-footer {
          margin-top: 40px;
          padding-top: 20px;
          border-top: 2px solid #E5E7EB;
          text-align: center;
        }
        .pdf-footer-text { color: #6B7280; font-size: 12px; font-style: italic; }
        .pdf-confidential-notice {
          background: #FEF2F2;
          border: 1px solid #FECACA;
          border-radius: 6px;
          padding: 12px;
          margin-top: 10px;
        }
        .pdf-confidential-text {
          color: #B91C1C;
          font-size: 12px;
          font-weight: 600;
          text-align: center;
        }
      </style>

      <div class="pdf-header">
        <div class="pdf-logo-section">
          ${logoUrl ? `<img src="${logoUrl}" alt="Company Logo" class="pdf-logo">` : ''}
          <div class="pdf-company-info">
            <h1>${companyName}</h1>
            <p>Safety Observation Report</p>
          </div>
        </div>
        <div class="pdf-report-info">
          <div class="pdf-report-title">Observation Report</div>
          <div class="pdf-generated-date">Generated: ${format(new Date(), 'PPP')}</div>
        </div>
      </div>

      <div class="pdf-section">
        <h2 class="pdf-section-title">Basic Information</h2>
        <div class="pdf-info-grid">
          <div class="pdf-info-item">
            <div class="pdf-info-label">Observation ID</div>
            <div class="pdf-info-value">${observation.id}</div>
          </div>
          <div class="pdf-info-item">
            <div class="pdf-info-label">Status</div>
            <div class="pdf-info-value">
              <span class="pdf-status-badge" style="${this.getStatusStyle(observation.status)}">
                ${observation.status}
              </span>
            </div>
          </div>
          <div class="pdf-info-item pdf-full-width">
            <div class="pdf-info-label">Location</div>
            <div class="pdf-info-value">${observation.location}</div>
          </div>
          <div class="pdf-info-item">
            <div class="pdf-info-label">Category</div>
            <div class="pdf-info-value">${observation.category}</div>
          </div>
          <div class="pdf-info-item">
            <div class="pdf-info-label">Type</div>
            <div class="pdf-info-value">${observation.type}</div>
          </div>
          <div class="pdf-info-item pdf-full-width">
            <div class="pdf-info-label">Action/Condition</div>
            <div class="pdf-info-value">${observation.actionCondition}</div>
          </div>
        </div>
      </div>

      <div class="pdf-section">
        <h2 class="pdf-section-title">Description</h2>
        <div class="pdf-description-section">
          <div class="pdf-description-text">${observation.description}</div>
        </div>
      </div>

      <div class="pdf-section">
        <h2 class="pdf-section-title">People Involved</h2>
        <div class="pdf-info-grid">
          <div class="pdf-info-item">
            <div class="pdf-info-label">Reported By</div>
            <div class="pdf-info-value">${observation.reportedBy}</div>
          </div>
          <div class="pdf-info-item">
            <div class="pdf-info-label">Reported Date</div>
            <div class="pdf-info-value">${format(observation.reportedDate, 'PPP')}</div>
          </div>
          ${observation.actionAssignee ? `
          <div class="pdf-info-item">
            <div class="pdf-info-label">Action Assignee</div>
            <div class="pdf-info-value">${observation.actionAssignee}</div>
          </div>
          ` : ''}
          ${observation.reviewedBy ? `
          <div class="pdf-info-item">
            <div class="pdf-info-label">Reviewed By</div>
            <div class="pdf-info-value">${observation.reviewedBy}</div>
          </div>
          ` : ''}
          ${observation.dueDate ? `
          <div class="pdf-info-item">
            <div class="pdf-info-label">Due Date</div>
            <div class="pdf-info-value">${format(observation.dueDate, 'PPP')}</div>
          </div>
          ` : ''}
        </div>
      </div>

      <div class="pdf-footer">
        <div class="pdf-footer-text">
          This is an official safety observation record generated on ${format(new Date(), 'PPP')}
        </div>
        <div class="pdf-confidential-notice">
          <div class="pdf-confidential-text">
            CONFIDENTIAL - This document contains sensitive safety information. Please maintain confidentiality.
          </div>
        </div>
      </div>
    `;

    return container;
  }

  private static getStatusStyle(status: string): string {
    switch (status.toLowerCase()) {
      case 'new': return 'background: #FEF3C7; color: #92400E;';
      case 'open': return 'background: #FED7AA; color: #9A3412;';
      case 'in progress': return 'background: #DBEAFE; color: #1E40AF;';
      case 'action in progress': return 'background: #DBEAFE; color: #1E40AF;';
      case 'completed': return 'background: #D1FAE5; color: #065F46;';
      case 'closed': return 'background: #D1FAE5; color: #065F46;';
      default: return 'background: #F3F4F6; color: #374151;';
    }
  }
}