import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Upload, X, Loader2, <PERSON><PERSON><PERSON>, <PERSON>otateCcw } from 'lucide-react';
import { format, addHours } from 'date-fns';
import { cn } from '@/lib/utils';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import AllFilterLocation from '@/components/observations/AllFilterLocation';
import FileUploadComponent from '@/components/common/FileUploadComponent';
import ImageComponent from '@/components/common/ImageComponent';
import SignatureCanvas from 'react-signature-canvas';

// API endpoints
const PERMIT_REPORTS = `${API_BASE_URL}/permit-reports`;
const FILE_URL = `${API_BASE_URL}/files`;
const GET_USER_ROLE_BY_MODE = `${API_BASE_URL}/users/get_users`;

// Work type options
const WORK_TYPE_OPTIONS = [
  { value: 'High-Risk Hazard', label: 'High-Risk Hazard Permit' },
  { value: 'Routine', label: 'Routine Work Activity' },
  { value: 'Non Routine', label: 'Non Routine Work Activity' }
];

// Form validation schema
const permitFormSchema = z.object({
  permitWorkType: z.string().min(1, "Permit work type is required"),
  permitType: z.array(z.string()).min(1, "Permit work name is required"),
  permitStartDate: z.date({
    required_error: "Start date is required",
  }),
  permitEndDate: z.date({
    required_error: "End date is required",
  }),
  workDescription: z.string().min(1, "Work description is required"),
  nameOfSiteSupervisor: z.string().min(1, "Site supervisor name is required"),
  noOfWorkers: z.number().min(1, "Number of workers must be at least 1"),
  workOrderNo: z.string().optional(),
  applicantContactNo: z.string().min(1, "Applicant contact number is required"),
  supervisorContactNo: z.string().min(1, "Supervisor contact number is required"),
  supportingDocuments: z.array(z.string()).min(2, "At least 2 supporting documents are required"),
  reviewerId: z.string().optional(),
  assessorId: z.string().optional(),
  riskAssessmentId: z.string().optional(),
  permitRiskControl: z.array(z.any()).optional(),
  // Location fields
  locationOneId: z.string().optional(),
  locationTwoId: z.string().optional(),
  locationThreeId: z.string().optional(),
  locationFourId: z.string().optional(),
  locationFiveId: z.string().optional(),
  locationSixId: z.string().optional(),
});

type PermitFormData = z.infer<typeof permitFormSchema>;

// Interfaces
interface PermitTypeOption {
  value: string;
  label: string;
  id: string;
  controls: RiskControl[];
}

interface RiskControl {
  value: string;
  description: string;
  currentType: string;
  current_type?: string; // For backward compatibility
  method: string;
  files: string[];
  permitType: string;
  remarks: string;
  evidence: string[];
}

interface UserOption {
  value: string;
  label: string;
  firstName: string;
  lastName?: string;
}

interface ApiUser {
  id: string;
  firstName: string;
  lastName?: string;
  type: string;
}

interface LocationData {
  locationOneId: string;
  locationTwoId: string;
  locationThreeId: string;
  locationFourId: string;
  locationFiveId: string;
  locationSixId: string;
}

interface CreatePermitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  reapplyAction?: {
    id: string;
    maskId: string;
    actionToBeTaken: string;
    actionType?: string;
    applicationId?: string;
    submittedBy: {
      firstName: string;
      lastName?: string;
    };
    applicationDetails: {
      dueDate: string;
    };
    created: string;
  } | null;
  permitDetails?: any; // Permit data for reapply
}

const CreatePermitModal: React.FC<CreatePermitModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  reapplyAction = null,
  permitDetails = null,
}) => {
  const { toast } = useToast();
  const user = useSelector((state: RootState) => state.auth.user);
  const signRef = useRef<SignatureCanvas>(null);

  // State
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [permitTypeOptions, setPermitTypeOptions] = useState<PermitTypeOption[]>([]);
  const [reviewers, setReviewers] = useState<UserOption[]>([]);
  const [assessors, setAssessors] = useState<UserOption[]>([]);
  const [riskControls, setRiskControls] = useState<RiskControl[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [locationData, setLocationData] = useState<LocationData>({
    locationOneId: '',
    locationTwoId: '',
    locationThreeId: '',
    locationFourId: '',
    locationFiveId: '',
    locationSixId: ''
  });

  // Form setup
  const form = useForm<PermitFormData>({
    resolver: zodResolver(permitFormSchema),
    defaultValues: {
      permitWorkType: "High-Risk Hazard", // Default to High-Risk Hazard as per test.js
      permitType: [],
      supportingDocuments: [],
      noOfWorkers: 1,
      permitRiskControl: [],
    },
  });

  const watchedWorkType = form.watch('permitWorkType');
  const watchedStartDate = form.watch('permitStartDate');

  // Helper function to update risk controls in both state and form
  const updateRiskControls = useCallback((updatedControls: RiskControl[]) => {
    setRiskControls(updatedControls);
    form.setValue('permitRiskControl', updatedControls);
  }, [form]);

  // Current time constraints - memoized to prevent re-renders
  const now = useMemo(() => new Date(), []);
  const maxStartDate = useMemo(() => addHours(now, 24), [now]);

  // Simplified date/time validation - more user-friendly than the complex test.js logic

  // Date change handlers - Exact from test.js
  const handleStartDateChange = useCallback((date: Date | null) => {
    // If the user clears the date (or the date is null), handle that case:
    form.setValue('permitStartDate', date);
    form.setValue('permitEndDate', undefined); // Clear end date when start date changes
  }, [form]);

  const handleEndDateChange = useCallback((date: Date | null) => {
    form.setValue('permitEndDate', date);
  }, [form]);

  // Simplified validation - more user-friendly
  // The complex validation from test.js was too restrictive for editing existing dates

  // Fetch permit type options based on work type
  const fetchPermitTypeOptions = useCallback(async (workType: string) => {
    try {
      let endpoint = '';
      switch (workType) {
        case 'High-Risk Hazard':
          endpoint = 'high-risk-hazard-list';
          break;
        case 'Routine':
          endpoint = 'routine-list';
          break;
        case 'Non Routine':
          endpoint = 'non-routine-list';
          break;
        default:
          return;
      }

      const response = await apiService.get(`/${endpoint}`);
      const options = response.map((item: { hazardName: string; riskAssessmentId: string; controls?: RiskControl[] }) => ({
        value: item.hazardName,
        label: item.hazardName,
        id: item.riskAssessmentId,
        controls: item.controls || []
      }));
      setPermitTypeOptions(options);
    } catch (error) {
      console.error('Error fetching permit type options:', error);
      toast({
        title: "Error",
        description: "Failed to fetch permit type options.",
        variant: "destructive"
      });
    }
  }, [toast]);

  // Fetch reviewers based on location
  const fetchReviewers = useCallback(async () => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: locationData.locationOneId || "",
        locationTwoId: locationData.locationTwoId || "",
        locationThreeId: locationData.locationThreeId || "",
        locationFourId: locationData.locationFourId || "",
        mode: 'eptwReviewer'
      });

      if (response) {
        const data = response.map((item: ApiUser) => ({
          label: item.firstName,
          value: item.id,
          firstName: item.firstName,
          lastName: item.lastName
        }));
        setReviewers(data);
      }
    } catch (error) {
      console.error("Error fetching reviewers:", error);
    }
  }, [locationData.locationOneId, locationData.locationTwoId, locationData.locationThreeId, locationData.locationFourId]);

  // Fetch assessors based on location
  const fetchAssessors = useCallback(async () => {
    try {
      const response = await apiService.post(GET_USER_ROLE_BY_MODE, {
        locationOneId: locationData.locationOneId || "",
        locationTwoId: locationData.locationTwoId || "",
        locationThreeId: locationData.locationThreeId || "",
        locationFourId: locationData.locationFourId || "",
        mode: 'eptwAssessor'
      });

      if (response) {
        const data = response.map((item: ApiUser) => ({
          label: item.firstName,
          value: item.id,
          firstName: item.firstName,
          lastName: item.lastName
        }));
        setAssessors(data);
      }
    } catch (error) {
      console.error("Error fetching assessors:", error);
    }
  }, [locationData.locationOneId, locationData.locationTwoId, locationData.locationThreeId, locationData.locationFourId]);

  // Effect to fetch permit types when work type changes
  useEffect(() => {
    if (watchedWorkType) {
      fetchPermitTypeOptions(watchedWorkType);
      // Reset permit type selection when work type changes
      form.setValue('permitType', []);
      form.setValue('permitRiskControl', []);
      setRiskControls([]);
    }
  }, [watchedWorkType, fetchPermitTypeOptions, form]);

  // Effect to set default work type on mount
  useEffect(() => {
    if (isOpen && !form.getValues('permitWorkType')) {
      form.setValue('permitWorkType', 'High-Risk Hazard');
    }
  }, [isOpen, form]);

  // Effect to fetch users when location changes - based on user type like test.js
  useEffect(() => {
    if (isOpen && locationData.locationOneId) {
      if (user?.type === 'External') {
        fetchReviewers();
      } else {
        fetchAssessors();
      }
    }
  }, [isOpen, locationData.locationOneId, user?.type, fetchReviewers, fetchAssessors]);

  // Handle permit type selection and generate risk controls
  const handlePermitTypeChange = useCallback((selectedTypes: string[]) => {
    if (!selectedTypes || selectedTypes.length === 0) {
      setRiskControls([]);
      form.setValue('permitRiskControl', []);
      return;
    }

    // Only allow one selection (following test.js pattern)
    if (selectedTypes.length > 1) {
      toast({
        title: "Selection Error",
        description: "Only one permit work name can be selected.",
        variant: "destructive"
      });
      return;
    }

    const selectedOption = permitTypeOptions.find(option => option.value === selectedTypes[0]);

    if (selectedOption) {
      // Generate risk controls from selected permit type (following test.js structure)
      const controls: RiskControl[] = selectedOption.controls.map(control => ({
        value: "",
        description: control.value || control.description,
        currentType: control.current_type || control.currentType,
        method: control.method || "",
        files: control.files || [],
        permitType: selectedOption.label,
        remarks: "",
        evidence: []
      }));

      updateRiskControls(controls);

      // Update form with risk assessment ID
      form.setValue('riskAssessmentId', selectedOption.id);
    } else {
      setRiskControls([]);
      form.setValue('permitRiskControl', []);
    }
  }, [permitTypeOptions, form, toast, updateRiskControls]);

  // File upload handler for supporting documents
  const handleSupportingDocumentUpload = useCallback((fileNames: string[]) => {
    // FileUploadComponent handles the upload internally, we just receive the uploaded file names
    setUploadedFiles(fileNames);
    form.setValue('supportingDocuments', fileNames);
  }, [form]);

  // File upload handler for evidence
  const handleEvidenceUpload = useCallback((fileNames: string[], controlIndex: number) => {
    // FileUploadComponent handles the upload internally, we just receive the uploaded file names
    const updatedControls = [...riskControls];
    updatedControls[controlIndex].evidence = fileNames;
    updateRiskControls(updatedControls);
  }, [riskControls, updateRiskControls]);

  // Signature upload handler
  const uploadSignature = useCallback(async (): Promise<string> => {
    if (!signRef.current || signRef.current.isEmpty()) {
      throw new Error('Signature is required');
    }

    const filename = `${new Date().getTime()}_permit_signature.png`;
    const signatureDataURL = signRef.current.getCanvas().toDataURL("image/png");

    // Convert data URL to file
    const response = await fetch(signatureDataURL);
    const blob = await response.blob();
    const file = new File([blob], filename, { type: 'image/png' });

    const formData = new FormData();
    formData.append('file', file);

    try {
      const uploadResponse = await apiService.post(FILE_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (uploadResponse && uploadResponse.files && uploadResponse.files[0]) {
        return uploadResponse.files[0].originalname;
      } else {
        throw new Error("Signature upload failed.");
      }
    } catch (error) {
      console.error("Signature upload error:", error);
      throw error;
    }
  }, []);

  // Form validation
  const validateForm = useCallback((): boolean => {
    const formData = form.getValues();

    // Check if signature is provided
    if (!signRef.current || signRef.current.isEmpty()) {
      toast({
        title: "Validation Error",
        description: "Signature is required.",
        variant: "destructive"
      });
      return false;
    }

    // Check if minimum 2 supporting documents are uploaded (following test.js validation)
    if (!formData.supportingDocuments || formData.supportingDocuments.length < 2) {
      toast({
        title: "Validation Error",
        description: "Minimum 2 supporting documents must be uploaded.",
        variant: "destructive"
      });
      return false;
    }

    // Check if all risk controls have responses and required remarks
    for (let i = 0; i < riskControls.length; i++) {
      const control = riskControls[i];
      if (!control.value) {
        toast({
          title: "Validation Error",
          description: `Please provide a response for permit work item ${i + 1}.`,
          variant: "destructive"
        });
        return false;
      }

      // Require remarks for "No" and "Not Applicable" responses
      if ((control.value === "No" || control.value === "Not Applicable") && !control.remarks.trim()) {
        toast({
          title: "Validation Error",
          description: `Please provide remarks for permit work item ${i + 1} when selecting "${control.value}".`,
          variant: "destructive"
        });
        return false;
      }
    }

    // Check if reviewer/assessor is selected - exact logic from test.js
    if (user?.type === 'External') {
      if (!formData.reviewerId) {
        toast({
          title: "Validation Error",
          description: "Please select a reviewer.",
          variant: "destructive"
        });
        return false;
      }
    }
    if (user?.type === 'Internal') {
      if (!formData.assessorId) {
        toast({
          title: "Validation Error",
          description: "Please select an assessor.",
          variant: "destructive"
        });
        return false;
      }
    }

    return true;
  }, [form, riskControls, toast, user?.type]);

  // Form submission handler
  const handleSubmit = useCallback(async () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Upload signature first
      const uploadedSignature = await uploadSignature();

      // Get form data
      const formData = form.getValues();

      // Prepare permit data for submission (following test.js pattern)
      const permitData = {
        // Spread all form data first (like test.js does)
        ...formData,

        // Override specific fields with proper formatting
        permitStartDate: formData.permitStartDate.toISOString(),
        permitEndDate: formData.permitEndDate.toISOString(),
        noOfWorkers: Number(formData.noOfWorkers),
        workOrderNo: formData.workOrderNo || '',
        riskAssessmentId: formData.riskAssessmentId || '',

        // Risk controls (following test.js structure)
        permitRiskControl: riskControls.map(control => ({
          remarks: control.remarks,
          evidence: control.evidence,
          description: control.description,
          currentType: control.currentType,
          files: control.files,
          method: control.method,
          permitType: control.permitType,
          value: control.value
        })),

        // Applicant status with signature (following test.js pattern)
        applicantStatus: {
          signature: uploadedSignature,
          status: true,
          comments: '',
          signedDate: new Date().toISOString()
        },

        // Additional fields
        applicantId: user?.id,
        status: ''
      };

      // Submit to API
      const response = await apiService.post(PERMIT_REPORTS, permitData);

      if (response) {
        toast({
          title: "Success",
          description: "Permit submitted successfully!",
        });

        // Reset form and close modal
        form.reset();
        setRiskControls([]);
        setUploadedFiles([]);
        setLocationData({
          locationOneId: '',
          locationTwoId: '',
          locationThreeId: '',
          locationFourId: '',
          locationFiveId: '',
          locationSixId: ''
        });
        signRef.current?.clear();

        onSuccess();
        onClose();
      }
    } catch (error) {
      console.error('Error submitting permit:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit permit. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, uploadSignature, form, riskControls, user, toast, onSuccess, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {reapplyAction
              ? `Reapply Permit - Action ${reapplyAction.maskId}`
              : 'Electronic Permit to Work (ePTW) Application'
            }
          </DialogTitle>
          {reapplyAction && (
            <div className="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
              <p className="text-sm text-orange-800">
                <strong>Reapply Mode:</strong> You are reapplying for permit based on action {reapplyAction.maskId}
              </p>
              <p className="text-sm text-orange-700 mt-1">
                {reapplyAction.actionToBeTaken}
              </p>
            </div>
          )}
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">
              Instructions to Permit Applicant:
            </h4>
            <p className="text-sm text-blue-800">
              Please ensure all required fields are filled out accurately and thoroughly. 
              This information is critical to assessing and managing safety risks associated 
              with potentially hazardous work activities. Complete and precise details will 
              support effective risk management and help protect everyone on-site.
            </p>
          </div>

          <Form {...form}>
            <form className="space-y-6">
              {/* Work Type Selection */}
              <FormField
                control={form.control}
                name="permitWorkType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Permit Work Type *</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select Permit Work Type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {WORK_TYPE_OPTIONS.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Permit Work Name Selection */}
              {watchedWorkType && (
                <FormField
                  control={form.control}
                  name="permitType"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Permit Work Name *</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          const newValue = [value]; // Convert to array for consistency with test.js
                          field.onChange(newValue);
                          handlePermitTypeChange(newValue);
                        }}
                        value={field.value?.[0] || ""} // Get first item from array
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Permit Work Name" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {permitTypeOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Date and Time Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="permitStartDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Permit Start Date and Time *</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          value={field.value ? format(field.value, "yyyy-MM-dd'T'HH:mm") : ""}
                          onChange={(e) => {
                            if (e.target.value) {
                              const date = new Date(e.target.value);
                              // Basic validation - just check if it's not in the past and within 24 hours
                              const isNotInPast = date >= now;
                              const isWithin24Hours = date <= maxStartDate;

                              if (isNotInPast && isWithin24Hours) {
                                handleStartDateChange(date);
                              } else {
                                let errorMessage = "Please select a valid date and time.";
                                if (!isNotInPast) {
                                  errorMessage = "Start date/time cannot be in the past.";
                                } else if (!isWithin24Hours) {
                                  errorMessage = "Start date/time must be within 24 hours from now.";
                                }

                                toast({
                                  title: "Invalid Date/Time",
                                  description: errorMessage,
                                  variant: "destructive"
                                });
                              }
                            } else {
                              handleStartDateChange(null);
                            }
                          }}
                          min={format(now, "yyyy-MM-dd'T'HH:mm")}
                          max={format(maxStartDate, "yyyy-MM-dd'T'HH:mm")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="permitEndDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Permit End Date and Time *</FormLabel>
                      <FormControl>
                        <Input
                          type="datetime-local"
                          value={field.value ? format(field.value, "yyyy-MM-dd'T'HH:mm") : ""}
                          onChange={(e) => {
                            if (e.target.value) {
                              const date = new Date(e.target.value);
                              // Basic validation for end date
                              if (watchedStartDate) {
                                const isAfterStart = date >= watchedStartDate;
                                const isWithin24Hours = date <= addHours(watchedStartDate, 24);

                                if (isAfterStart && isWithin24Hours) {
                                  handleEndDateChange(date);
                                } else {
                                  let errorMessage = "Please select a valid end date/time.";
                                  if (!isAfterStart) {
                                    errorMessage = "End date/time must be after start date/time.";
                                  } else if (!isWithin24Hours) {
                                    errorMessage = "End date/time must be within 24 hours of start date/time.";
                                  }

                                  toast({
                                    title: "Invalid Date/Time",
                                    description: errorMessage,
                                    variant: "destructive"
                                  });
                                }
                              } else {
                                handleEndDateChange(date);
                              }
                            } else {
                              handleEndDateChange(null);
                            }
                          }}
                          disabled={!watchedStartDate}
                          min={watchedStartDate ? format(watchedStartDate, "yyyy-MM-dd'T'HH:mm") : ""}
                          max={watchedStartDate ? format(addHours(watchedStartDate, 24), "yyyy-MM-dd'T'HH:mm") : ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Work Description */}
              <FormField
                control={form.control}
                name="workDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work Description *</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe the work to be performed..."
                        className="resize-none"
                        rows={3}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Location Selection */}
              <div>
                <Label className="text-sm font-medium">Location *</Label>
                <div className="mt-2">
                  <AllFilterLocation
                    handleFilter={(
                      locationOneId: string,
                      locationTwoId?: string,
                      locationThreeId?: string,
                      locationFourId?: string,
                      locationFiveId?: string,
                      locationSixId?: string
                    ) => {
                      const data = {
                        locationOneId,
                        locationTwoId: locationTwoId || '',
                        locationThreeId: locationThreeId || '',
                        locationFourId: locationFourId || '',
                        locationFiveId: locationFiveId || '',
                        locationSixId: locationSixId || ''
                      };
                      setLocationData(data);
                      // Update form values for location
                      form.setValue('locationOneId', locationOneId || '');
                      form.setValue('locationTwoId', locationTwoId || '');
                      form.setValue('locationThreeId', locationThreeId || '');
                      form.setValue('locationFourId', locationFourId || '');
                      form.setValue('locationFiveId', locationFiveId || '');
                      form.setValue('locationSixId', locationSixId || '');
                    }}
                    getLocation={locationData}
                  />
                </div>
              </div>

              {/* Supervisor Details */}
              <FormField
                control={form.control}
                name="nameOfSiteSupervisor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Responsible Site / Job Supervisor *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supervisor name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Workers and Work Order */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="noOfWorkers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Number of Workers *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="1"
                          placeholder="Enter number of workers"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="workOrderNo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work Order / Job Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter work order number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="applicantContactNo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Applicant Contact Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter contact number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="supervisorContactNo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supervisor Contact Number *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter supervisor contact" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Supporting Documents Upload */}
              <div className="space-y-4">
                <div>
                  <Label className="text-sm font-medium">
                    Add/Upload images of the work location and authorized workers list *
                  </Label>
                  <p className="text-xs text-gray-600 mt-1">
                    (Minimum 2 attachments are mandatory)
                  </p>
                </div>

                <FileUploadComponent
                  onFileUpload={handleSupportingDocumentUpload}
                  fieldName="supportingDocuments"
                  description="Attach supporting images / documents"
                  accept="image/*,application/pdf"
                  acceptedTypes={['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']}
                  multiple={true}
                  maxFiles={5}
                  maxFileSize={104857600} // 100MB
                  initialFiles={uploadedFiles}
                />

                {/* Display uploaded files */}
                {/* {uploadedFiles.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium mb-2 block">Uploaded Documents</Label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {uploadedFiles.map((fileName, index) => (
                        <div key={index} className="relative">
                          <div className="border rounded-lg p-2 shadow-sm">
                            <ImageComponent
                              fileName={fileName}
                              size="100"
                              name={true}
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                              onClick={() => {
                                setUploadedFiles(prev => prev.filter((_, i) => i !== index));
                                const currentDocs = form.getValues('supportingDocuments');
                                form.setValue('supportingDocuments', currentDocs.filter((_, i) => i !== index));
                              }}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )} */}
              </div>

              {/* Risk Control Items - Grouped by Permit Type */}
              {riskControls.length > 0 && (
                <div className="space-y-6">
                  {Object.entries(
                    riskControls.reduce((acc: Record<string, Array<RiskControl & { controlIndex: number }>>, control, index) => {
                      const permitType = control.permitType || 'General';
                      if (!acc[permitType]) acc[permitType] = [];
                      acc[permitType].push({ ...control, controlIndex: index });
                      return acc;
                    }, {})
                  ).map(([permitType, controls]) => (
                    <div key={permitType} className="space-y-4">
                      <h3 className="text-lg font-semibold text-blue-900 border-b pb-2">
                        {permitType}
                      </h3>
                      {controls.map((control) => (
                        <Card key={control.controlIndex} className="p-4">
                          <CardHeader className="pb-3">
                            <CardTitle className="text-base">
                              {control.controlIndex + 1}. {control.description}
                            </CardTitle>
                            <p className="text-sm text-gray-600">
                              <strong>Control Type:</strong> {control.currentType}
                            </p>
                          </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Yes/No/Not Applicable Selection */}
                        <div>
                          <Label className="text-sm font-medium mb-2 block">Response *</Label>
                          <div className="flex gap-2">
                            {['Yes', 'No', 'Not Applicable'].map((option) => (
                              <Button
                                key={option}
                                type="button"
                                variant={control.value === option ? "default" : "outline"}
                                className={cn(
                                  "flex-1",
                                  control.value === option && option === 'Yes' && "bg-green-500 hover:bg-green-600",
                                  control.value === option && option === 'No' && "bg-red-500 hover:bg-red-600",
                                  control.value === option && option === 'Not Applicable' && "bg-yellow-500 hover:bg-yellow-600"
                                )}
                                onClick={() => {
                                  const updatedControls = [...riskControls];
                                  updatedControls[control.controlIndex].value = option;
                                  updateRiskControls(updatedControls);
                                }}
                              >
                                {option}
                              </Button>
                            ))}
                          </div>
                        </div>

                        {/* Remarks */}
                        <div>
                          <Label className="text-sm font-medium">Remarks</Label>
                          <Textarea
                            placeholder="Enter remarks..."
                            value={control.remarks}
                            onChange={(e) => {
                              const updatedControls = [...riskControls];
                              updatedControls[control.controlIndex].remarks = e.target.value;
                              updateRiskControls(updatedControls);
                            }}
                            rows={2}
                          />
                        </div>

                        {/* Evidence Upload */}
                        <div>
                          <Label className="text-sm font-medium">Evidence</Label>
                          <FileUploadComponent
                            onFileUpload={(fileNames) => handleEvidenceUpload(fileNames, control.controlIndex)}
                            fieldName={`evidence_${control.controlIndex}`}
                            description="Attach evidence files"
                            accept="image/*,application/pdf"
                            acceptedTypes={['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']}
                            multiple={true}
                            maxFiles={5}
                            maxFileSize={104857600}
                            initialFiles={control.evidence}
                          />

                          {/* Display uploaded evidence */}
                          {/* {control.evidence && control.evidence.length > 0 && (
                            <div className="mt-2 grid grid-cols-3 gap-2">
                              {control.evidence.map((fileName, evidenceIndex) => (
                                <div key={evidenceIndex} className="relative">
                                  <div className="border rounded p-1">
                                    <ImageComponent
                                      fileName={fileName}
                                      size="60"
                                      name={false}
                                    />
                                    <Button
                                      type="button"
                                      variant="destructive"
                                      size="sm"
                                      className="absolute -top-1 -right-1 h-4 w-4 rounded-full p-0"
                                      onClick={() => {
                                        const updatedControls = [...riskControls];
                                        updatedControls[control.controlIndex].evidence = updatedControls[control.controlIndex].evidence.filter((_, i) => i !== evidenceIndex);
                                        updateRiskControls(updatedControls);
                                      }}
                                    >
                                      <X className="h-2 w-2" />
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )} */}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ))}
            </div>
          )}

              {/* Signature Section */}
              <Card className="p-6">
                <CardHeader className="pb-4">
                  <CardTitle className="text-center">Applicant Signature</CardTitle>
                  <p className="text-sm text-center text-gray-600 italic">
                    I confirm that all work activities are complete, the site is fully restored
                    to a safe condition, and all tools, equipment, and materials have been
                    secured and accounted for.
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="border-2 border-gray-300 rounded-lg shadow-lg">
                      <SignatureCanvas
                        ref={signRef}
                        penColor="#1F3BB3"
                        backgroundColor="white"
                        canvasProps={{
                          width: 450,
                          height: 120,
                          className: "signature-canvas"
                        }}
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => signRef.current?.clear()}
                      >
                        <RotateCcw className="h-4 w-4 mr-1" />
                        Clear
                      </Button>
                    </div>
                    <p className="text-sm font-medium">{user?.firstName || 'User'}</p>
                  </div>
                </CardContent>
              </Card>

              {/* Reviewer/Assessor Selection - Exact condition from test.js */}
              {/* Note: user.type should be set to 'External' for external users during login */}
              {user?.type === 'External' ? (
                <FormField
                  control={form.control}
                  name="reviewerId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reviewer *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Reviewer" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {reviewers.map((reviewer) => (
                            <SelectItem key={reviewer.value} value={reviewer.value}>
                              {reviewer.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              ) : (
                <FormField
                  control={form.control}
                  name="assessorId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Assessor *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select Assessor" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {assessors.map((assessor) => (
                            <SelectItem key={assessor.value} value={assessor.value}>
                              {assessor.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </form>
          </Form>
        </div>

        <DialogFooter className="flex justify-between">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Submit Permit
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreatePermitModal;
