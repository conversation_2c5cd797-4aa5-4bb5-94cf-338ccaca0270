import React, { useState, useEffect, useMemo } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { format } from 'date-fns';
import { User, Calendar, FileText, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import apiService from '@/services/apiService';

interface ActionLogModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  inspectionData: any;
  totalActionData: any[];
}

interface User {
  id: string;
  firstName: string;
  lastName?: string;
}

const ActionLogModal: React.FC<ActionLogModalProps> = ({
  open,
  onOpenChange,
  inspectionData,
  totalActionData
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const { accessToken } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (open && accessToken) {
      fetchUsers();
    }
  }, [open, accessToken]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/users');
      setUsers(response);
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const getName = (id: string): string => {
    const user = users.find(user => user.id === id);
    return user?.firstName || 'Unknown';
  };

  const getActionTypeDisplay = (actionType: string): string => {
    switch (actionType) {
      case 'perform_task':
        return 'Take Action';
      case 'verify_task':
        return 'Verify Action';
      case 'reperform_task':
        return 'Re-perform Action';
      case 'approve':
        return 'Approve Action';
      default:
        return actionType || 'Unknown Action';
    }
  };

  const getStatusBadge = (action: any) => {
    let status = '';
    let variant: 'default' | 'secondary' | 'destructive' | 'outline' = 'default';

    switch (action.lastActionType) {
      case 'perform_task':
        status = action.lastStatus === 'Completed' ? 'Assigned' : 'Pending Assignment';
        variant = 'outline';
        break;
      case 'reperform_task':
        status = 'Re-Assigned';
        variant = 'secondary';
        break;
      case 'verify_task':
        if (action.lastStatus === 'Completed') {
          status = 'Verified & Closed';
          variant = 'default';
        } else {
          status = 'Implemented - Pending Verification';
          variant = 'outline';
        }
        break;
      case 'approve':
        status = 'Verified & Closed';
        variant = 'default';
        break;
      default:
        status = 'Unknown Status';
        variant = 'secondary';
        break;
    }

    return <Badge variant={variant}>{status}</Badge>;
  };

  const getAssigneeInfo = (action: any): string => {
    const assigneeId = Array.isArray(action.lastAssignedToId) 
      ? action.lastAssignedToId[0] 
      : action.lastAssignedToId;

    switch (action.lastActionType) {
      case 'perform_task':
        return `Action Assignee: ${getName(assigneeId)}`;
      case 'verify_task':
        return `Action to be verified by: ${getName(assigneeId)}`;
      case 'reperform_task':
        return `Action Re-assigned to: ${getName(assigneeId)}`;
      case 'approve':
        return `Action Verified By: ${getName(assigneeId)}`;
      default:
        return `Assigned to: ${getName(assigneeId)}`;
    }
  };

  const renderActionDetails = (action: any, actionNumber: number) => {
    return action.data.map((item: any, index: number) => (
      <Card key={index} className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            {item.actionType === 'perform_task' && (
              <>
                <FileText className="h-4 w-4" />
                Assigned Action - IA {actionNumber.toFixed(1)}
              </>
            )}
            {item.actionType === 'verify_task' && (
              <>
                <CheckCircle className="h-4 w-4" />
                Action Verification - IA {actionNumber.toFixed(1)}
              </>
            )}
            {item.actionType === 'reperform_task' && (
              <>
                <AlertCircle className="h-4 w-4" />
                Action Re-assignment - IA {actionNumber.toFixed(1)}
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              {item.actionToBeTaken && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-700">Action to be Taken:</p>
                  <p className="text-sm text-gray-600">{item.actionToBeTaken}</p>
                </div>
              )}
              {item.actionTaken && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-700">Action Taken:</p>
                  <p className="text-sm text-gray-600">{item.actionTaken}</p>
                </div>
              )}
              {item.comments && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-700">Comments:</p>
                  <p className="text-sm text-gray-600">{item.comments}</p>
                </div>
              )}
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-700">
                  {item.status === 'Initiated' ? 'Action Assignee:' : 'Action Taken By:'}
                </p>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-600">
                    {item.assignedToId && getName(
                      Array.isArray(item.assignedToId) ? item.assignedToId[0] : item.assignedToId
                    )}
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-700">Date:</p>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <p className="text-sm text-gray-600">
                    {item.created ? format(new Date(item.created), 'dd-MM-yyyy') : 'N/A'}
                  </p>
                </div>
              </div>
              {item.dueDate && (
                <div className="space-y-1">
                  <p className="text-sm font-medium text-gray-700">Due Date:</p>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <p className="text-sm text-gray-600">
                      {format(new Date(item.dueDate), 'dd-MM-yyyy')}
                    </p>
                  </div>
                </div>
              )}
              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-700">Status:</p>
                <Badge variant={item.status === 'Completed' ? 'default' : 'outline'}>
                  {item.status || 'Pending'}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    ));
  };

  const processedActions = useMemo(() => {
    // Ensure totalActionData is an array
    if (!Array.isArray(totalActionData)) {
      console.warn('totalActionData is not an array:', totalActionData);
      return [];
    }

    let actionCounter = 0.0;
    return totalActionData
      .map(action => {
        if (action.firstActionType === 'perform_task') {
          actionCounter += 1.0;
          return { ...action, actionNumber: actionCounter };
        }
        return null;
      })
      .filter(action => action !== null);
  }, [totalActionData]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Action Log - {inspectionData?.maskId || 'Inspection'}
          </DialogTitle>
        </DialogHeader>

        <div className="mt-4">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
          ) : processedActions.length > 0 ? (
            <Accordion type="single" collapsible className="space-y-2">
              {processedActions.map((action, index) => (
                <AccordionItem key={index} value={`action-${index}`} className="border rounded-lg">
                  <AccordionTrigger className="px-4 py-3 hover:no-underline">
                    <div className="flex items-center justify-between w-full mr-4">
                      <div className="flex items-center gap-3">
                        <span className="font-medium">IA {action.actionNumber.toFixed(1)}</span>
                        {getStatusBadge(action)}
                      </div>
                      <div className="text-sm text-gray-600">
                        {getAssigneeInfo(action)}
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    {renderActionDetails(action, action.actionNumber)}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No actions found for this inspection.</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ActionLogModal;
