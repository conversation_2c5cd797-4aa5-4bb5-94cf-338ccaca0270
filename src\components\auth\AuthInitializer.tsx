import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setLogin, setUser, setTokens, setLoginConfig } from '@/store/slices/authSlice';
import { fetchUserDetails, fetchLoginConfig } from '@/services/api';

interface AuthInitializerProps {
  children: React.ReactNode;
}

const AuthInitializer = ({ children }: AuthInitializerProps) => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Check if tokens exist in localStorage
    const accessToken = localStorage.getItem('access_token');
    const refreshToken = localStorage.getItem('refresh_token');

    if (accessToken && refreshToken) {
      // If tokens exist, set the authenticated state and tokens
      dispatch(setLogin());
      dispatch(setTokens({ accessToken, refreshToken }));

      // Fetch user details and login config
      const loadUserDetails = async () => {
        try {
          const userDetails = await fetchUserDetails(accessToken);
          dispatch(setUser(userDetails));
        } catch (error) {
          console.error('Failed to fetch user details during initialization:', error);
        }
      };

      const loadLoginConfig = async () => {
        try {
          const loginConfig = await fetchLoginConfig();
          dispatch(setLoginConfig(loginConfig));
        } catch (error) {
          console.error('Failed to fetch login config during initialization:', error);
        }
      };

      loadUserDetails();
      loadLoginConfig();
    }
  }, [dispatch]);

  return <>{children}</>;
};

export default AuthInitializer;
