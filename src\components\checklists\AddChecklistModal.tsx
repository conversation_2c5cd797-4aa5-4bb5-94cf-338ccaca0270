import { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';

// API endpoints
const ADMINDROPDOWNS = `${API_BASE_URL}/dropdowns`;

interface AddChecklistModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  accessToken: string | null;
  editingChecklist?: any;
  isEditMode?: boolean;
}

interface NewChecklist {
  name: string;
  customId: string;
  category: string;
}

interface OptionType {
  label: string;
  value: string;
}

const AddChecklistModal = ({
  isOpen,
  onClose,
  onSuccess,
  accessToken,
  editingChecklist,
  isEditMode = false
}: AddChecklistModalProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [categories, setCategories] = useState<OptionType[]>([]);

  const [newChecklist, setNewChecklist] = useState<NewChecklist>({
    name: '',
    customId: '',
    category: '',
  });

  // Fetch categories from dropdown API
  const fetchDropdownData = useCallback(async (maskId: string, setState: React.Dispatch<React.SetStateAction<OptionType[]>>) => {
    try {
      const uriString = {
        where: { maskId },
        include: [{ relation: "dropdownItems" }],
      };
      const url = `${ADMINDROPDOWNS}?filter=${encodeURIComponent(JSON.stringify(uriString))}`;
      const response = await apiService.get(url);
      const data = response[0]?.dropdownItems.map((item: any) => ({
        label: item.name,
        value: item.name,
      })) || [];
      setState(data);
    } catch (error) {
      console.error(`Error fetching ${maskId} list:`, error);
    }
  }, [accessToken]);

  // Initialize form data when editing
  useEffect(() => {
    if (isEditMode && editingChecklist && isOpen) {
      setNewChecklist({
        name: editingChecklist.name || '',
        customId: editingChecklist.customId || '',
        category: editingChecklist.category || '',
      });
      setShowValidationErrors(false);
    } else if (!isEditMode && isOpen) {
      // Reset form for new checklist
      setNewChecklist({
        name: '',
        customId: '',
        category: '',
      });
      setShowValidationErrors(false);
    }
  }, [isEditMode, editingChecklist, isOpen]);

  // Fetch categories when modal opens
  useEffect(() => {
    if (isOpen && accessToken) {
      fetchDropdownData('ins_category', setCategories);
    }
  }, [isOpen, accessToken, fetchDropdownData]);

  const handleClose = () => {
    setNewChecklist({
      name: '',
      customId: '',
      category: '',
    });
    setShowValidationErrors(false);
    setShowConfirmDialog(false);
    onClose();
  };

  const validateForm = () => {
    return newChecklist.name.trim() !== '' &&
           newChecklist.category.trim() !== '';
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      setShowValidationErrors(true);
      return;
    }
    setShowConfirmDialog(true);
  };

  const handleConfirmedSubmit = async () => {
    if (!accessToken) {
      toast({
        title: "Error",
        description: "Missing authentication",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    setShowConfirmDialog(false);

    try {
      const payload = {
        ...newChecklist
      };

      if (isEditMode && editingChecklist?.id) {
        await apiService.patch(`/checklists/${editingChecklist.id}`, payload);
        toast({
          title: "Success",
          description: "Checklist updated successfully",
        });
      } else {
        await apiService.post('/checklists', payload);
        toast({
          title: "Success",
          description: "Checklist created successfully",
        });
      }

      onSuccess();
      handleClose();
    } catch (error) {
      console.error('Error saving checklist:', error);
      toast({
        title: "Error",
        description: "Failed to save checklist. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Edit Checklist' : 'Add New Checklist'}</DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Checklist Name */}
          <div className="space-y-2">
            <Label className={showValidationErrors && !newChecklist.name ? "text-red-500" : ""}>
              Checklist Name *
            </Label>
            <Input
              value={newChecklist.name}
              onChange={(e) => setNewChecklist(prev => ({ ...prev, name: e.target.value }))}
              placeholder="Enter checklist name"
              className={showValidationErrors && !newChecklist.name ? "border-red-500" : ""}
            />
            {showValidationErrors && !newChecklist.name && (
              <p className="text-red-500 text-sm">Checklist name is required</p>
            )}
          </div>



          {/* Category */}
          <div className="space-y-2">
            <Label className={showValidationErrors && !newChecklist.category ? "text-red-500" : ""}>
              Category *
            </Label>
            <Select
              value={newChecklist.category}
              onValueChange={(value) => setNewChecklist(prev => ({ ...prev, category: value }))}
            >
              <SelectTrigger className={showValidationErrors && !newChecklist.category ? "border-red-500" : ""}>
                <SelectValue placeholder="Select Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {showValidationErrors && !newChecklist.category && (
              <p className="text-red-500 text-sm">Category is required</p>
            )}
          </div>

          {/* Mask ID */}


          {/* Custom ID */}
          <div className="space-y-2">
            <Label>Custom ID</Label>
            <Input
              value={newChecklist.customId}
              onChange={(e) => setNewChecklist(prev => ({ ...prev, customId: e.target.value }))}
              placeholder="Enter custom ID"
            />
          </div>

          {/* Curator ID */}

        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={handleClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
            <AlertDialogTrigger asChild>
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? (isEditMode ? 'Updating...' : 'Creating...') : (isEditMode ? 'Update Checklist' : 'Create Checklist')}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>
                  {isEditMode ? 'Save Changes?' : 'Create New Checklist?'}
                </AlertDialogTitle>
                <AlertDialogDescription>
                  {isEditMode
                    ? "Changes will be saved permanently. This action cannot be undone."
                    : "A new checklist will be created with the provided information."
                  }
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleConfirmedSubmit} disabled={isSubmitting}>
                  {isEditMode ? 'Save Changes' : 'Create Checklist'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddChecklistModal;
