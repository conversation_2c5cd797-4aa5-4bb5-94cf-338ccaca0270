import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import apiService from '@/services/apiService';
import { API_BASE_URL } from '@/constants/index';
import ImageComponent from '@/components/common/ImageComponent';
import {
  Edit,
  Save,
  X,
  Plus,
  Minus,
  FileText,
  Image as ImageIcon,
  Link,
  Type
} from 'lucide-react';

// API Configuration
const FILE_URL = `${API_BASE_URL}/files`;

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface EditableComponentRendererProps {
  component: DocumentComponent;
  onUpdate: (id: string, content: any) => void;
  onRemove: (id: string) => void;
  isEditing?: boolean;
  onSave?: (content: any) => void;
  onCancel?: () => void;
}

const EditableComponentRenderer: React.FC<EditableComponentRendererProps> = ({
  component,
  onUpdate,
  onRemove,
  isEditing: externalIsEditing = false,
  onSave,
  onCancel,
}) => {
  const [internalIsEditing, setInternalIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(component.content);
  const [isUploading, setIsUploading] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const { toast } = useToast();

  const isEditing = externalIsEditing || internalIsEditing;

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  const handleSave = () => {
    if (onSave) {
      onSave(editContent);
    } else {
      onUpdate(component.id, editContent);
      setInternalIsEditing(false);
    }
  };

  const handleCancel = () => {
    setEditContent(component.content);
    if (onCancel) {
      onCancel();
    } else {
      setInternalIsEditing(false);
    }
  };

  const handleDoubleClick = () => {
    if (!externalIsEditing) {
      setInternalIsEditing(true);
    }
  };

  // File upload function following the established pattern
  const handleFileUpload = async (file: File, isImage: boolean = false) => {
    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiService.post(FILE_URL, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });

      if (response?.files?.[0]?.originalname) {
        const uploadedFileName = response.files[0].originalname;

        // Update content with uploaded file information
        const updatedContent = {
          ...editContent,
          filename: uploadedFileName,
          originalName: file.name,
          fileType: file.type,
          size: `${(file.size / 1024).toFixed(1)} KB`,
          uploadedAt: new Date().toISOString()
        };

        // For images, we'll use the filename to display via presigned URL
        if (isImage) {
          updatedContent.src = uploadedFileName; // Store filename, not data URL
          updatedContent.alt = updatedContent.alt || file.name;
        }

        setEditContent(updatedContent);

        toast({
          title: "Success",
          description: `${isImage ? 'Image' : 'File'} uploaded successfully`,
        });
      } else {
        throw new Error('Upload failed - no filename returned');
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast({
        title: "Error",
        description: `Failed to upload ${isImage ? 'image' : 'file'}`,
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const getComponentIcon = (type: string) => {
    switch (type) {
      case 'document-header':
      case 'section-header':
        return <Type className="h-4 w-4" />;
      case 'paragraph':
        return <FileText className="h-4 w-4" />;
      case 'image':
        return <ImageIcon className="h-4 w-4" />;
      case 'link':
        return <Link className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'file-attachment': 'File Attachment',
      'link': 'Link'
    };
    return labels[type] || type;
  };

  const renderEditMode = () => {
    const { type, content } = component;

    switch (type) {
      case 'document-header':
      case 'section-header':
        return (
          <div className="space-y-2">
            <Input
              value={editContent.text || ''}
              onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
              placeholder="Enter header text..."
              className="font-semibold"
            />
          </div>
        );

      case 'paragraph':
        return (
          <Textarea
            ref={textareaRef}
            value={editContent.text || ''}
            onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
            placeholder="Enter paragraph content..."
            className="min-h-[100px] resize-none"
          />
        );

      case 'bullet-list':
      case 'numbered-list':
        return (
          <div className="space-y-2">
            {editContent.items?.map((item: string, index: number) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={item}
                  onChange={(e) => {
                    const newItems = [...editContent.items];
                    newItems[index] = e.target.value;
                    setEditContent({ ...editContent, items: newItems });
                  }}
                  placeholder={`Item ${index + 1}`}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const newItems = editContent.items.filter((_: any, i: number) => i !== index);
                    setEditContent({ ...editContent, items: newItems });
                  }}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const newItems = [...(editContent.items || []), 'New item'];
                setEditContent({ ...editContent, items: newItems });
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
        );

      case 'quote':
        return (
          <div className="space-y-2">
            <Textarea
              value={editContent.text || ''}
              onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
              placeholder="Enter quote text..."
              className="min-h-[80px]"
            />
            <Input
              value={editContent.author || ''}
              onChange={(e) => setEditContent({ ...editContent, author: e.target.value })}
              placeholder="Author (optional)"
            />
          </div>
        );

      case 'link':
        return (
          <div className="space-y-2">
            <Input
              value={editContent.text || ''}
              onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
              placeholder="Link text"
            />
            <Input
              value={editContent.url || ''}
              onChange={(e) => setEditContent({ ...editContent, url: e.target.value })}
              placeholder="URL"
            />
          </div>
        );

      case 'image':
        return (
          <div className="space-y-2">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleFileUpload(file, true);
                  }
                }}
                disabled={isUploading}
                className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"
              />
              <p className="text-xs text-gray-500 mt-2">
                {isUploading ? 'Uploading file...' : 'Upload any file (images, documents, etc.)'}
              </p>
            </div>
            <Input
              value={editContent.alt || ''}
              onChange={(e) => setEditContent({ ...editContent, alt: e.target.value })}
              placeholder="Alt text"
            />
            <Input
              value={editContent.caption || ''}
              onChange={(e) => setEditContent({ ...editContent, caption: e.target.value })}
              placeholder="Caption (optional)"
            />
            {editContent.filename && (
              <div className="text-xs text-gray-600 space-y-1">
                <div>File: {editContent.originalName || editContent.filename}</div>
                <div>Uploaded: {editContent.filename}</div>
                <div>Size: {editContent.size}</div>
              </div>
            )}
          </div>
        );

      case 'file-attachment':
        return (
          <div className="space-y-2">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleFileUpload(file, false);
                  }
                }}
                disabled={isUploading}
                className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-green-50 file:text-green-700 hover:file:bg-green-100 disabled:opacity-50"
              />
              <p className="text-xs text-gray-500 mt-2">
                {isUploading ? 'Uploading file...' : 'Upload any file (PDF, DOC, XLS, etc.)'}
              </p>
            </div>
            <Input
              value={editContent.description || ''}
              onChange={(e) => setEditContent({ ...editContent, description: e.target.value })}
              placeholder="File description (optional)"
            />
            {editContent.filename && (
              <div className="text-xs text-gray-600 space-y-1">
                <div>File: {editContent.originalName || editContent.filename}</div>
                <div>Uploaded: {editContent.filename}</div>
                <div>Type: {editContent.fileType}</div>
                <div>Size: {editContent.size}</div>
              </div>
            )}
          </div>
        );

      default:
        return (
          <Textarea
            ref={textareaRef}
            value={JSON.stringify(editContent, null, 2)}
            onChange={(e) => {
              try {
                setEditContent(JSON.parse(e.target.value));
              } catch {
                // Invalid JSON, keep as string for now
              }
            }}
            placeholder="Edit content (JSON format)"
            className="min-h-[100px] font-mono text-sm"
          />
        );
    }
  };

  const renderViewMode = () => {
    const { type, content } = component;

    switch (type) {
      case 'document-header':
        return (
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">{content.text || 'Document Title'}</h1>
          </div>
        );

      case 'section-header':
        return (
          <div className="mb-4">
            <h2 className="text-2xl font-semibold text-gray-800">{content.text || 'Section Header'}</h2>
          </div>
        );

      case 'paragraph':
        return (
          <div className="mb-4">
            <p className="text-gray-700 leading-relaxed">{content.text || 'Enter your paragraph content here...'}</p>
          </div>
        );

      case 'bullet-list':
        return (
          <div className="mb-4">
            <ul className="list-disc list-inside space-y-1">
              {(content.items || []).map((item: string, index: number) => (
                <li key={index} className="text-gray-700">{item}</li>
              ))}
            </ul>
          </div>
        );

      case 'numbered-list':
        return (
          <div className="mb-4">
            <ol className="list-decimal list-inside space-y-1">
              {(content.items || []).map((item: string, index: number) => (
                <li key={index} className="text-gray-700">{item}</li>
              ))}
            </ol>
          </div>
        );

      case 'quote':
        return (
          <div className="mb-4">
            <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600">
              "{content.text || 'Enter your quote here...'}"
              {content.author && <cite className="block mt-2 text-sm">— {content.author}</cite>}
            </blockquote>
          </div>
        );

      case 'separator':
        return (
          <div className="mb-4">
            <hr className="border-gray-300" />
          </div>
        );

      case 'link':
        return (
          <div className="mb-4">
            <a 
              href={content.url || '#'} 
              target={content.target || '_blank'}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {content.text || 'Link text'}
            </a>
          </div>
        );

      case 'image':
        return (
          <div className="mb-4">
            {content.filename ? (
              <div className="w-48 mx-auto">
                <div className="border border-gray-200 rounded-lg p-3 bg-gray-50 flex flex-col items-center">
                  <ImageComponent
                    fileName={content.filename}
                    size="32"
                    name={true}
                  />
                </div>
                {content.caption && (
                  <p className="text-sm text-gray-600 mt-2 italic text-center">{content.caption}</p>
                )}
                {content.alt && (
                  <p className="text-xs text-gray-500 mt-1 text-center">Alt: {content.alt}</p>
                )}
                <div className="text-xs text-gray-500 mt-1 text-center">
                  {content.originalName && content.originalName !== content.filename && (
                    <span>Original: {content.originalName} • </span>
                  )}
                  {content.size && <span>Size: {content.size}</span>}
                </div>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded p-8 text-center">
                <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No file uploaded</p>
              </div>
            )}
          </div>
        );

      case 'file-attachment':
        return (
          <div className="mb-4">
            {content.filename ? (
              <div className="w-48 mx-auto">
                <div className="border border-gray-200 rounded-lg p-3 bg-gray-50 flex flex-col items-center">
                  <ImageComponent
                    fileName={content.filename}
                    size="32"
                    name={true}
                  />
                </div>
                {content.description && (
                  <p className="text-sm text-gray-600 mt-2 text-center">{content.description}</p>
                )}
                <div className="text-xs text-gray-500 mt-1 text-center">
                  {content.originalName && content.originalName !== content.filename && (
                    <span>Original: {content.originalName} • </span>
                  )}
                  {content.fileType && <span>Type: {content.fileType} • </span>}
                  {content.size && <span>Size: {content.size}</span>}
                  {content.uploadedAt && (
                    <span> • Uploaded: {new Date(content.uploadedAt).toLocaleDateString()}</span>
                  )}
                </div>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded p-8 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No file uploaded</p>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="mb-4 p-4 bg-gray-100 border border-gray-300 rounded">
            <div className="flex items-center gap-2 text-gray-600 mb-2">
              {getComponentIcon(type)}
              <span className="text-sm font-medium">{getComponentLabel(type)}</span>
            </div>
            <pre className="text-xs text-gray-500 whitespace-pre-wrap">
              {JSON.stringify(content, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    <div className="relative group bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4 hover:shadow-md transition-all duration-200">
      {/* Component Header */}
      <div className="flex items-center justify-between mb-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="flex items-center gap-2">
          {getComponentIcon(component.type)}
          <Badge variant="secondary" className="text-xs">
            {getComponentLabel(component.type)}
          </Badge>
        </div>
        
        <div className="flex gap-1">
          {isEditing ? (
            <>
              <Button size="sm" onClick={handleSave} className="h-6 w-6 p-0">
                <Save className="h-3 w-3" />
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel} className="h-6 w-6 p-0">
                <X className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => setInternalIsEditing(true)}
                className="h-6 w-6 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onRemove(component.id)}
                className="h-6 w-6 p-0 hover:bg-red-50 hover:border-red-200"
              >
                <X className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Component Content */}
      <div onDoubleClick={handleDoubleClick} className={isEditing ? '' : 'cursor-pointer'}>
        {isEditing ? renderEditMode() : renderViewMode()}
      </div>

      {/* Edit Hint */}
      {!isEditing && (
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-5 rounded-lg pointer-events-none">
          <div className="bg-white shadow-lg rounded px-2 py-1 text-xs text-gray-600">
            Double-click to edit
          </div>
        </div>
      )}
    </div>
  );
};

export default EditableComponentRenderer;
