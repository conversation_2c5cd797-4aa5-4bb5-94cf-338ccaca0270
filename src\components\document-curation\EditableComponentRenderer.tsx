import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Edit,
  Save,
  X,
  Plus,
  Minus,
  FileText,
  Image as ImageIcon,
  Link,
  Type
} from 'lucide-react';

interface DocumentComponent {
  id: string;
  type: string;
  content: any;
  position: number;
}

interface EditableComponentRendererProps {
  component: DocumentComponent;
  onUpdate: (id: string, content: any) => void;
  onRemove: (id: string) => void;
}

const EditableComponentRenderer: React.FC<EditableComponentRendererProps> = ({
  component,
  onUpdate,
  onRemove
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(component.content);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isEditing && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isEditing]);

  const handleSave = () => {
    onUpdate(component.id, editContent);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditContent(component.content);
    setIsEditing(false);
  };

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const getComponentIcon = (type: string) => {
    switch (type) {
      case 'document-header':
      case 'section-header':
        return <Type className="h-4 w-4" />;
      case 'paragraph':
        return <FileText className="h-4 w-4" />;
      case 'image':
        return <ImageIcon className="h-4 w-4" />;
      case 'link':
        return <Link className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getComponentLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'document-header': 'Document Header',
      'section-header': 'Section Header',
      'paragraph': 'Paragraph',
      'bullet-list': 'Bullet List',
      'numbered-list': 'Numbered List',
      'quote': 'Quote Block',
      'separator': 'Separator',
      'image': 'Image',
      'file-attachment': 'File Attachment',
      'link': 'Link'
    };
    return labels[type] || type;
  };

  const renderEditMode = () => {
    const { type, content } = component;

    switch (type) {
      case 'document-header':
      case 'section-header':
        return (
          <div className="space-y-2">
            <Input
              value={editContent.text || ''}
              onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
              placeholder="Enter header text..."
              className="font-semibold"
            />
          </div>
        );

      case 'paragraph':
        return (
          <Textarea
            ref={textareaRef}
            value={editContent.text || ''}
            onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
            placeholder="Enter paragraph content..."
            className="min-h-[100px] resize-none"
          />
        );

      case 'bullet-list':
      case 'numbered-list':
        return (
          <div className="space-y-2">
            {editContent.items?.map((item: string, index: number) => (
              <div key={index} className="flex gap-2">
                <Input
                  value={item}
                  onChange={(e) => {
                    const newItems = [...editContent.items];
                    newItems[index] = e.target.value;
                    setEditContent({ ...editContent, items: newItems });
                  }}
                  placeholder={`Item ${index + 1}`}
                />
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    const newItems = editContent.items.filter((_: any, i: number) => i !== index);
                    setEditContent({ ...editContent, items: newItems });
                  }}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button
              size="sm"
              variant="outline"
              onClick={() => {
                const newItems = [...(editContent.items || []), 'New item'];
                setEditContent({ ...editContent, items: newItems });
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Item
            </Button>
          </div>
        );

      case 'quote':
        return (
          <div className="space-y-2">
            <Textarea
              value={editContent.text || ''}
              onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
              placeholder="Enter quote text..."
              className="min-h-[80px]"
            />
            <Input
              value={editContent.author || ''}
              onChange={(e) => setEditContent({ ...editContent, author: e.target.value })}
              placeholder="Author (optional)"
            />
          </div>
        );

      case 'link':
        return (
          <div className="space-y-2">
            <Input
              value={editContent.text || ''}
              onChange={(e) => setEditContent({ ...editContent, text: e.target.value })}
              placeholder="Link text"
            />
            <Input
              value={editContent.url || ''}
              onChange={(e) => setEditContent({ ...editContent, url: e.target.value })}
              placeholder="URL"
            />
          </div>
        );

      case 'image':
        return (
          <div className="space-y-2">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              <input
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                      setEditContent({
                        ...editContent,
                        src: event.target?.result as string,
                        filename: file.name,
                        size: `${(file.size / 1024).toFixed(1)} KB`
                      });
                    };
                    reader.readAsDataURL(file);
                  }
                }}
                className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
              />
              <p className="text-xs text-gray-500 mt-2">Upload an image file (JPG, PNG, GIF)</p>
            </div>
            <Input
              value={editContent.alt || ''}
              onChange={(e) => setEditContent({ ...editContent, alt: e.target.value })}
              placeholder="Alt text"
            />
            <Input
              value={editContent.caption || ''}
              onChange={(e) => setEditContent({ ...editContent, caption: e.target.value })}
              placeholder="Caption (optional)"
            />
            {editContent.filename && (
              <div className="text-xs text-gray-600">
                File: {editContent.filename} ({editContent.size})
              </div>
            )}
          </div>
        );



      default:
        return (
          <Textarea
            ref={textareaRef}
            value={JSON.stringify(editContent, null, 2)}
            onChange={(e) => {
              try {
                setEditContent(JSON.parse(e.target.value));
              } catch {
                // Invalid JSON, keep as string for now
              }
            }}
            placeholder="Edit content (JSON format)"
            className="min-h-[100px] font-mono text-sm"
          />
        );
    }
  };

  const renderViewMode = () => {
    const { type, content } = component;

    switch (type) {
      case 'document-header':
        return (
          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gray-900">{content.text || 'Document Title'}</h1>
          </div>
        );

      case 'section-header':
        return (
          <div className="mb-4">
            <h2 className="text-2xl font-semibold text-gray-800">{content.text || 'Section Header'}</h2>
          </div>
        );

      case 'paragraph':
        return (
          <div className="mb-4">
            <p className="text-gray-700 leading-relaxed">{content.text || 'Enter your paragraph content here...'}</p>
          </div>
        );

      case 'bullet-list':
        return (
          <div className="mb-4">
            <ul className="list-disc list-inside space-y-1">
              {(content.items || []).map((item: string, index: number) => (
                <li key={index} className="text-gray-700">{item}</li>
              ))}
            </ul>
          </div>
        );

      case 'numbered-list':
        return (
          <div className="mb-4">
            <ol className="list-decimal list-inside space-y-1">
              {(content.items || []).map((item: string, index: number) => (
                <li key={index} className="text-gray-700">{item}</li>
              ))}
            </ol>
          </div>
        );

      case 'quote':
        return (
          <div className="mb-4">
            <blockquote className="border-l-4 border-blue-500 pl-4 italic text-gray-600">
              "{content.text || 'Enter your quote here...'}"
              {content.author && <cite className="block mt-2 text-sm">— {content.author}</cite>}
            </blockquote>
          </div>
        );

      case 'separator':
        return (
          <div className="mb-4">
            <hr className="border-gray-300" />
          </div>
        );

      case 'link':
        return (
          <div className="mb-4">
            <a 
              href={content.url || '#'} 
              target={content.target || '_blank'}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              {content.text || 'Link text'}
            </a>
          </div>
        );

      case 'image':
        return (
          <div className="mb-4">
            {content.src ? (
              <div>
                <img 
                  src={content.src} 
                  alt={content.alt || ''} 
                  className="max-w-full h-auto rounded border"
                />
                {content.caption && (
                  <p className="text-sm text-gray-600 mt-2 italic">{content.caption}</p>
                )}
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded p-8 text-center">
                <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No image URL provided</p>
              </div>
            )}
          </div>
        );

      case 'file-attachment':
        return (
          <div className="mb-4">
            {content.filename ? (
              <div className="border border-gray-300 rounded-lg p-4 bg-gray-50">
                <div className="flex items-center gap-3">
                  <FileText className="h-8 w-8 text-blue-600" />
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{content.filename}</h4>
                    {content.description && (
                      <p className="text-sm text-gray-600">{content.description}</p>
                    )}
                    <div className="flex gap-4 text-xs text-gray-500 mt-1">
                      {content.fileType && <span>Type: {content.fileType}</span>}
                      {content.size && <span>Size: {content.size}</span>}
                    </div>
                  </div>
                  <Button
                    size="sm"
                    onClick={() => {
                      if (content.src) {
                        const link = document.createElement('a');
                        link.href = content.src;
                        link.download = content.filename;
                        link.click();
                      }
                    }}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Download
                  </Button>
                </div>
              </div>
            ) : (
              <div className="border-2 border-dashed border-gray-300 rounded p-8 text-center">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No file uploaded</p>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="mb-4 p-4 bg-gray-100 border border-gray-300 rounded">
            <div className="flex items-center gap-2 text-gray-600 mb-2">
              {getComponentIcon(type)}
              <span className="text-sm font-medium">{getComponentLabel(type)}</span>
            </div>
            <pre className="text-xs text-gray-500 whitespace-pre-wrap">
              {JSON.stringify(content, null, 2)}
            </pre>
          </div>
        );
    }
  };

  return (
    <div className="relative group bg-white dark:bg-slate-800 rounded-lg border border-slate-200 dark:border-slate-700 p-4 hover:shadow-md transition-all duration-200">
      {/* Component Header */}
      <div className="flex items-center justify-between mb-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
        <div className="flex items-center gap-2">
          {getComponentIcon(component.type)}
          <Badge variant="secondary" className="text-xs">
            {getComponentLabel(component.type)}
          </Badge>
        </div>
        
        <div className="flex gap-1">
          {isEditing ? (
            <>
              <Button size="sm" onClick={handleSave} className="h-6 w-6 p-0">
                <Save className="h-3 w-3" />
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel} className="h-6 w-6 p-0">
                <X className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <>
              <Button 
                size="sm" 
                variant="outline" 
                onClick={() => setIsEditing(true)}
                className="h-6 w-6 p-0"
              >
                <Edit className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => onRemove(component.id)}
                className="h-6 w-6 p-0 hover:bg-red-50 hover:border-red-200"
              >
                <X className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Component Content */}
      <div onDoubleClick={handleDoubleClick} className={isEditing ? '' : 'cursor-pointer'}>
        {isEditing ? renderEditMode() : renderViewMode()}
      </div>

      {/* Edit Hint */}
      {!isEditing && (
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-5 rounded-lg pointer-events-none">
          <div className="bg-white shadow-lg rounded px-2 py-1 text-xs text-gray-600">
            Double-click to edit
          </div>
        </div>
      )}
    </div>
  );
};

export default EditableComponentRenderer;
