import { FILE_DOWNLOAD, GET_BLOB, FILE_DOWNLOAD_WITH_S3 } from '../constants/index';
import apiService from '../services/apiService';

/**
 * Fetches the full image URL using the file name
 * @param fileName The name of the file to fetch
 * @returns The presigned URL for the file
 */
export const fetchFullImageUrl = async (fileName: string): Promise<string> => {
  try {
    const response = await apiService.get(FILE_DOWNLOAD(fileName));
    return response;
  } catch (error) {
    console.error('Failed to fetch image URL:', error);
    return null;
  }
};

/**
 * Fetches the full image URL for a hazard image using bucket and file name
 * @param bucket The S3 bucket name
 * @param fileName The name of the file to fetch
 * @returns The presigned URL for the file
 */
export const fetchFullHazardImageUrl = async (bucket: string, fileName: string): Promise<string> => {
  try {
    const response = await apiService.get(FILE_DOWNLOAD_WITH_S3(bucket, fileName));
    return response;
  } catch (error) {
    console.error('Failed to fetch image URL:', error);
    return null;
  }
};

/**
 * Fetches the data URL for an image using the presigned URL
 * @param url The presigned URL for the image
 * @returns A data URL for the image
 */
export const fetchDataUrlForImage = async (url: string): Promise<string> => {
  try {
    const blob = await apiService.post(
      GET_BLOB,
      { presignedUrl: url },
      {
        responseType: 'blob', // Ensure the response is returned as binary data
      }
    );

    // Convert the blob to a Data URL
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string); // reader.result is the Data URL
      reader.onerror = reject;
      reader.readAsDataURL(blob); // Read the blob as a Data URL
    });
  } catch (error) {
    console.error('Error fetching Data URL:', error);
    throw error;
  }
};
