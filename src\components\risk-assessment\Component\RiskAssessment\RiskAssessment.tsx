import React from 'react';
import Severity from '@/components/risk-assessment/Component/RiskAssessment/Severity';
import Likelihood from '@/components/risk-assessment/Component/RiskAssessment/Likelihood';
import RiskLevel from '@/components/risk-assessment/Component/RiskAssessment/RiskLevel';
import AcceptableRisk from '@/components/risk-assessment/Component/RiskAssessment/AcceptableRisk';

interface SeverityOption {
  value: string;
  label: string;
}

interface LikelihoodOption {
  value: string;
  label: string;
}

interface SeverityData {
  id: string;
  severity: string;
  personnel: string;
  property: string;
  environment: string;
  serviceLoss: string;
}

interface LevelData {
  level: string;
  descriptor: string;
  detailedDescription: string;
}

interface TableData {
  id: string;
  severity: string;
  rare: string;
  unlikely: string;
  possible: string;
  likely: string;
  almostCertain: string;
}

interface TaskItem {
  [key: number]: any;
}

interface RiskAssessmentProps {
  item: TaskItem[];
  severity: SeverityOption[];
  severityData: SeverityData[];
  required: boolean;
  onChangeSeverity: (option: { value: string; label: string }, type?: string) => void;
  likelyhood: LikelihoodOption[];
  levelData: LevelData[];
  onChangeLikelyhood: (option: { value: string; label: string }, type?: string) => void;
  rowClassName: (data: any) => string;
  tableData: TableData[];
  cellClassName: (value: number) => string;
  cellStyle: (data: any, field: string) => string;
  onChangeReAss: (value: boolean) => void;
}

const RiskAssessment: React.FC<RiskAssessmentProps> = ({
  item,
  severity,
  severityData,
  required,
  onChangeSeverity,
  likelyhood,
  levelData,
  onChangeLikelyhood,
  rowClassName,
  tableData,
  cellClassName,
  cellStyle,
  onChangeReAss
}) => {
  return (
    <div className="space-y-6">
      <div>
        <h6 className="text-lg font-semibold">Estimate the Risk of this Sub Activity</h6>
        <p className="text-sm text-muted-foreground mb-4">
          For this Sub Activity, assess the Severity & Likelihood of the <strong>identified consequences</strong>, considering the <strong>current controls</strong> in place
        </p>
        <ul className="list-disc list-inside space-y-2 text-sm text-muted-foreground mb-4">
          <li>Use the tables below as a guide</li>
          <li>
            Assess how well the preventative controls reduce the chances of an event occurring and how mitigative controls limit the impact if an event does occur.
          </li>
          <li>
            Ensure that your assessment reflects the worst-case scenario, based on existing controls and for each impacted category (People, Environment etc.)
          </li>
        </ul>
        <p className="text-sm text-muted-foreground">
          This assessment will be used to determine the Risk Level for this sub-activity
        </p>
      </div>

      <Severity
        severity={severity}
        severityData={severityData}
        required={required}
        onChangeSeverity={onChangeSeverity}
        item={item}
      />

      <Likelihood
        likelyhood={likelyhood}
        levelData={levelData}
        required={required}
        onChangeLikelyhood={onChangeLikelyhood}
        item={item}
        rowClassName={rowClassName}
      />

      <RiskLevel
        item={item}
        tableData={tableData}
        cellClassName={cellClassName}
        cellStyle={cellStyle}
      />

      <AcceptableRisk
        item={item}
        onChangeReAss={onChangeReAss}
      />
    </div>
  );
};

export default RiskAssessment;
