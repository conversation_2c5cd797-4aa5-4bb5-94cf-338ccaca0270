import React from 'react';
import { format } from 'date-fns';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Clock, 
  User, 
  FileText, 
  Calendar,
  AlertCircle,
  XCircle,
  Eye,
  RefreshCw,
  MessageSquare
} from 'lucide-react';
import { ObservationAction } from '@/services/api';
import ImageComponent from '@/components/common/ImageComponent';

interface ActionHistorySectionProps {
  actions: ObservationAction[];
}

const ActionHistorySection: React.FC<ActionHistorySectionProps> = ({ actions }) => {
  if (!actions || actions.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Action History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-sm">No actions have been taken on this observation yet.</p>
        </CardContent>
      </Card>
    );
  }

  const getActionIcon = (actionType: string, status: string) => {
    switch (actionType) {
      case 'review':
        return <Eye className="h-4 w-4" />;
      case 'take_action':
        return <CheckCircle className="h-4 w-4" />;
      case 'verify_action':
        return status === 'Completed' ? <CheckCircle className="h-4 w-4" /> : <XCircle className="h-4 w-4" />;
      case 'reperform_action':
        return <RefreshCw className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const getActionTitle = (actionType: string) => {
    switch (actionType) {
      case 'review':
        return 'Review Action';
      case 'take_action':
        return 'Take Action';
      case 'verify_action':
        return 'Verify Action';
      case 'reperform_action':
        return 'Re-perform Action';
      default:
        return 'Action';
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'Pending': { variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800' },
      'In Progress': { variant: 'default' as const, color: 'bg-blue-100 text-blue-800' },
      'Completed': { variant: 'default' as const, color: 'bg-green-100 text-green-800' },
      'Returned': { variant: 'destructive' as const, color: 'bg-red-100 text-red-800' },
      'Submitted': { variant: 'default' as const, color: 'bg-purple-100 text-purple-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig['Pending'];
    
    return (
      <Badge className={config.color}>
        {status}
      </Badge>
    );
  };

  const formatUserName = (user: { firstName: string; lastName?: string } | undefined) => {
    if (!user) return 'Unknown User';
    return `${user.firstName} ${user.lastName || ''}`.trim();
  };

  // Sort actions by creation date
  const sortedActions = [...actions].sort((a, b) => 
    new Date(a.created).getTime() - new Date(b.created).getTime()
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Action History ({actions.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {sortedActions.map((action, index) => (
          <div key={action.id} className="relative">
            {/* Timeline connector */}
            {index < sortedActions.length - 1 && (
              <div className="absolute left-6 top-12 w-0.5 h-full bg-border" />
            )}
            
            <div className="flex gap-4">
              {/* Action Icon */}
              <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                {getActionIcon(action.actionType, action.status)}
              </div>
              
              {/* Action Content */}
              <div className="flex-1 space-y-3">
                {/* Action Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <h4 className="font-semibold text-sm">
                      {getActionTitle(action.actionType)}
                    </h4>
                    {getStatusBadge(action.status)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {format(new Date(action.created), 'PPp')}
                  </div>
                </div>

                {/* Action Details */}
                <div className="bg-muted/30 rounded-lg p-4 space-y-3">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {action.submittedBy && (
                      <div className="flex items-center gap-2">
                        <User className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">Submitted by:</span>
                        <span className="text-xs font-medium">
                          {formatUserName(action.submittedBy)}
                        </span>
                      </div>
                    )}
                    
                    {action.dueDate && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-xs text-muted-foreground">Due Date:</span>
                        <span className="text-xs font-medium">
                          {format(new Date(action.dueDate), 'PPP')}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Action Type Specific Details */}
                  {action.actionType === 'review' && (
                    <div className="space-y-2">
                      {action.actionToBeTaken && (
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Action to be Taken:</span>
                          <p className="text-sm mt-1 p-2 bg-blue-50 border border-blue-200 rounded">
                            {action.actionToBeTaken}
                          </p>
                        </div>
                      )}
                      {action.actionOwner && (
                        <div className="flex items-center gap-2">
                          <User className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">Assigned to:</span>
                          <span className="text-xs font-medium">
                            {formatUserName(action.actionOwner)}
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                  {(action.actionType === 'take_action' || action.actionType === 'reperform_action') && (
                    <div className="space-y-2">
                      {action.actionTaken && (
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Action Taken:</span>
                          <p className="text-sm mt-1 p-2 bg-green-50 border border-green-200 rounded">
                            {action.actionTaken}
                          </p>
                        </div>
                      )}
                      {action.reviewer && (
                        <div className="flex items-center gap-2">
                          <User className="h-3 w-3 text-muted-foreground" />
                          <span className="text-xs text-muted-foreground">Reviewer:</span>
                          <span className="text-xs font-medium">
                            {formatUserName(action.reviewer)}
                          </span>
                        </div>
                      )}
                      {action.evidence && action.evidence.length > 0 && (
                        <div>
                          <span className="text-xs font-medium text-muted-foreground">Evidence ({action.evidence.length}):</span>
                          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                            {action.evidence.map((evidenceFile, idx) => (
                              <div key={idx} className="relative">
                                <ImageComponent
                                  fileName={evidenceFile}
                                  className="w-full h-20 object-cover rounded border"
                                  alt={`Evidence ${idx + 1}`}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {action.actionType === 'verify_action' && (
                    <div className="space-y-2">
                      {action.comments && (
                        <div>
                          <span className="text-xs font-medium text-muted-foreground flex items-center gap-1">
                            <MessageSquare className="h-3 w-3" />
                            Comments:
                          </span>
                          <p className="text-sm mt-1 p-2 bg-gray-50 border border-gray-200 rounded">
                            {action.comments}
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Description */}
                  {action.description && (
                    <div>
                      <span className="text-xs font-medium text-muted-foreground">Description:</span>
                      <p className="text-sm mt-1 text-muted-foreground">
                        {action.description}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            {index < sortedActions.length - 1 && <Separator className="mt-6" />}
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default ActionHistorySection;
