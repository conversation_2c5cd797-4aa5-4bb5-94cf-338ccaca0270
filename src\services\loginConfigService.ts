import { LoginConfig } from '@/store/slices/authSlice';

const API_BASE_URL = 'https://client-api.acuizen.com';

/**
 * Fetches the login configuration from the API
 * @returns The login configuration
 */
export const getLoginConfig = async (): Promise<LoginConfig> => {
  try {
    const response = await fetch(`${API_BASE_URL}/login-configs`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch login configuration: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching login configuration:', error);
    throw error;
  }
};
