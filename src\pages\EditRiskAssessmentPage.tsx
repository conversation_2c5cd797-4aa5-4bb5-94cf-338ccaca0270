import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import RiskAssessmentForm from '@/components/risk-assessment/TestRiskAssessment';
import HazardAssessmentForm from '@/components/risk-assessment/HazardAssessmentForm';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import apiService from '@/services/apiService';

interface RiskAssessmentData {
  id: string;
  type: 'Routine' | 'Non Routine' | 'High-Risk Hazard';
  status: 'Pending' | 'Draft' | 'Published';
  // Add other properties as needed
  [key: string]: any;
}

const EditRiskAssessmentPage: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { id } = useParams<{ id: string }>();
  const [riskAssessmentData, setRiskAssessmentData] = useState<RiskAssessmentData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchRiskAssessment = async () => {
      if (!id) {
        toast({
          title: "Error",
          description: "Risk assessment ID is required",
          variant: "destructive",
        });
        navigate('/risk-assessment');
        return;
      }

      try {
        const data = await apiService.get(`/risk-assessments/${id}`);
        setRiskAssessmentData(data);
      } catch (error) {
        console.error('Error fetching risk assessment:', error);
        toast({
          title: "Error",
          description: "Failed to load risk assessment data",
          variant: "destructive",
        });
        navigate('/risk-assessment');
      } finally {
        setLoading(false);
      }
    };

    fetchRiskAssessment();
  }, [id, navigate, toast]);

  const handleCancel = () => {
    navigate('/risk-assessment');
  };

  const getType = (): 'routine' | 'nonroutine' | 'highrisk' => {
    if (!riskAssessmentData) return 'routine';
    
    switch (riskAssessmentData.type) {
      case 'Non Routine':
        return 'nonroutine';
      case 'High-Risk Hazard':
        return 'highrisk';
      default:
        return 'routine';
    }
  };

  const getPageTitle = () => {
    if (!riskAssessmentData) return 'Edit Risk Assessment';
    
    switch (riskAssessmentData.type) {
      case 'Routine':
        return 'Edit Routine Risk Assessment';
      case 'Non Routine':
        return 'Edit Non-Routine Risk Assessment';
      case 'High-Risk Hazard':
        return 'Edit High-Risk Hazard Assessment';
      default:
        return 'Edit Risk Assessment';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading risk assessment...</span>
        </div>
      </div>
    );
  }

  if (!riskAssessmentData) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">Risk Assessment Not Found</h2>
          <p className="text-muted-foreground mb-4">The requested risk assessment could not be found.</p>
          <Button onClick={handleCancel}>
            Back to Risk Assessment
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="sticky top-0 z-10 bg-background border-b border-border/50 p-4 flex items-center">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleCancel}
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Risk Assessment
        </Button>
        <h1 className="text-xl font-bold">{getPageTitle()}</h1>
      </div>

      {riskAssessmentData?.type === 'High-Risk Hazard' ? (
        <HazardAssessmentForm
          domain="edit"
          data={riskAssessmentData}
        />
      ) : (
        <RiskAssessmentForm
          type={getType()}
          domain="edit"
          data={riskAssessmentData}
        />
      )}
    </div>
  );
};

export default EditRiskAssessmentPage;
