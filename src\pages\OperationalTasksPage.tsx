import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Clipboard, ClipboardCheck, Plus, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

const OperationalTasksPage = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Operational Tasks"
          description="Manage task assignments, tracking, and completion for daily operational activities"
        />
        <Button className="flex items-center gap-1" onClick={() => 
          toast({ 
            title: "Create Task", 
            description: "Task creation functionality will be implemented soon." 
          })
        }>
          <Plus className="h-4 w-4" /> Create Task
        </Button>
      </div>

      <Tabs defaultValue="my-tasks" className="w-full">
        <TabsList className="grid w-full md:w-[400px] grid-cols-2">
          <TabsTrigger value="my-tasks">My Tasks</TabsTrigger>
          <TabsTrigger value="all-tasks">All Tasks</TabsTrigger>
        </TabsList>
        
        <TabsContent value="my-tasks" className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search my tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Daily Equipment Check</CardTitle>
                <CardDescription>Due: Tomorrow</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Perform routine inspection of equipment in Area B.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xs bg-amber-100 text-amber-800 px-2 py-1 rounded-full">
                    Medium Priority
                  </span>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <ClipboardCheck className="h-3 w-3" /> Mark Complete
                  </Button>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Safety Procedure Review</CardTitle>
                <CardDescription>Due: Today</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Review updated safety procedures for chemical handling.
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full">
                    High Priority
                  </span>
                  <Button variant="outline" size="sm" className="flex items-center gap-1">
                    <ClipboardCheck className="h-3 w-3" /> Mark Complete
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="all-tasks" className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search all tasks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Task Management</h2>
            <p className="text-muted-foreground">
              This is a placeholder for the Operational Tasks service content. 
              The actual implementation will include a comprehensive task management system.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default OperationalTasksPage;
