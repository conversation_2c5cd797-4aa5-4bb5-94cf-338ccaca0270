import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Save, Eye, Upload } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import Sidebar from "@/components/checklist-curation/Sidebar";
import DroppableArea from "@/components/checklist-curation/DroppableArea";
import MobilePreview from "@/components/checklist-curation/MobilePreview";
import { DroppedItem, ContentComponent } from "@/types/draggable";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import apiService from "@/services/apiService";

const ChecklistCurationPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { toast } = useToast();
  const { accessToken } = useSelector((state: RootState) => state.auth);

  const [items, setItems] = useState<DroppedItem[]>([]);
  const [, setIsDragging] = useState(false);
  const [checklistData, setChecklistData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [invalidComponentIds, setInvalidComponentIds] = useState<string[]>([]);

  // Helper function to convert API component data to DroppedItem format
  const convertApiComponentToDroppedItem = (apiComponent: {
    id?: string;
    type: string;
    position?: number;
    data: any;
  }): DroppedItem => {
    return {
      id: apiComponent.id || `component-${Date.now()}-${Math.random()}`,
      type: apiComponent.type,
      data: {
        ...apiComponent.data,
        type: apiComponent.type as any,
        position: apiComponent.position || 0
      } as ContentComponent
    };
  };

  // Load checklist data on component mount
  useEffect(() => {
    const loadChecklistData = async () => {
      if (id) {
        try {
          // Fetch checklist details from API
          const checklistResponse = await apiService.get(`/checklists/${id}`);

          setChecklistData({
            id: checklistResponse.id,
            name: checklistResponse.name,
            category: checklistResponse.category,
            version: checklistResponse.version,
            description: checklistResponse.description,
            status: checklistResponse.status
          });

          // If checklist has components/curation data, preload them
          if (checklistResponse.value && checklistResponse.value.components) {
            const loadedComponents = checklistResponse.value.components.map((component: {
              id?: string;
              type: string;
              position?: number;
              data: any;
            }) =>
              convertApiComponentToDroppedItem(component)
            );

            // Sort components by position
            loadedComponents.sort((a: DroppedItem, b: DroppedItem) => (a.data.position || 0) - (b.data.position || 0));

            setItems(loadedComponents);

            toast({
              title: "Checklist Loaded",
              description: `Loaded ${loadedComponents.length} components from saved checklist.`,
            });
          }

        } catch (error) {
          console.error("Error loading checklist:", error);
          toast({
            title: "Error",
            description: "Failed to load checklist data",
            variant: "destructive"
          });
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    loadChecklistData();
  }, [id, toast]);

  const handleDragStart = () => {
    setIsDragging(true);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleAddItem = (item: DroppedItem) => {
    setItems((prev) => [...prev, item]);
    toast({
      title: "Component Added",
      description: `${item.type} component has been added.`,
    });
  };

  const handleRemoveItem = (id: string) => {
    setItems((prev) => prev.filter((item) => item.id !== id));
    toast({
      title: "Component Removed",
      description: "Component has been removed.",
    });
  };

  const handleUpdateItem = (id: string, data: ContentComponent) => {
    setItems((prev) =>
      prev.map((item) => (item.id === id ? { ...item, data } : item))
    );
    toast({
      title: "Component Updated",
      description: "Changes have been saved.",
    });
  };

  const handleReorderItems = (sourceIndex: number, targetIndex: number) => {
    setItems((prev) => {
      if (sourceIndex === -1 || targetIndex === -1 || sourceIndex === targetIndex) return prev;

      const newItems = [...prev];
      const [movedItem] = newItems.splice(sourceIndex, 1);
      newItems.splice(targetIndex, 0, movedItem);

      return newItems.map((item, index) => ({
        ...item,
        data: { ...item.data, position: index }
      }));
    });

    toast({
      title: "Components Reordered",
      description: "The order has been updated.",
    });
  };

  // Helper function to get display text for components
  const getComponentDisplayText = (item: DroppedItem): string => {
    const component = item.data;
    switch (component.type) {
      case 'header':
      case 'section-header':
        return component.text || 'Untitled';
      case 'text-body':
        return component.content ? component.content.substring(0, 30) + '...' : 'Empty';
      case 'sign':
      case 'date':
        return component.label || 'Untitled';
      case 'checkpoint':
        return component.text || 'Untitled';
      case 'checkpoint-group':
        return component.title || 'Untitled Group';
      default:
        return 'Unknown Component';
    }
  };

  // Validation functions for different component types
  const validateComponent = (item: DroppedItem): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    const component = item.data;

    // Common validations
    if (!component.type) {
      errors.push("Component type is required");
    }

    // Type-specific validations
    switch (component.type) {
      case 'header':
        if (!component.text || component.text.trim() === '' || component.text === 'New Header') {
          errors.push("Header text is required");
        }
        break;

      case 'section-header':
        if (!component.text || component.text.trim() === '' || component.text === 'New Section') {
          errors.push("Section header text is required");
        }
        break;

      case 'text-body':
        if (!component.content || component.content.trim() === '' || component.content === 'Enter your text content here...') {
          errors.push("Text body content is required");
        }
        break;

      case 'sign':
        if (!component.label || component.label.trim() === '') {
          errors.push("Signature label is required");
        }
        break;

      case 'date':
        if (!component.label || component.label.trim() === '') {
          errors.push("Date field label is required");
        }
        break;

      case 'text-input':
        if (!component.label || component.label.trim() === '') {
          errors.push("Text input field label is required");
        }
        break;

      case 'checkpoint':
        if (!component.text || component.text.trim() === '') {
          errors.push("Checkpoint text is required");
        }
        break;

      case 'checkpoint-group':
        if (!component.title || component.title.trim() === '') {
          errors.push("Checkpoint group title is required");
        }
        if (!component.checkpoints || component.checkpoints.length === 0) {
          errors.push("Checkpoint group must have at least 1 checkpoint");
        }
        if (component.checkpoints) {
          component.checkpoints.forEach((checkpoint, index) => {
            if (!checkpoint.text || checkpoint.text.trim() === '') {
              errors.push(`Checkpoint ${index + 1} text is required`);
            }
          });
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const validateAndGenerateJson = () => {
    // Check if checklist is empty
    if (items.length === 0) {
      toast({
        title: "Empty Checklist",
        description: "Cannot save or publish an empty checklist. Please add at least one component.",
        variant: "destructive"
      });
      return null;
    }

    // Validate all components
    const validationResults = items.map(item => ({
      item,
      validation: validateComponent(item)
    }));

    const invalidComponents = validationResults.filter(result => !result.validation.isValid);

    if (invalidComponents.length > 0) {
      // Track invalid component IDs for visual highlighting
      const invalidIds = invalidComponents.map(result => result.item.id);
      setInvalidComponentIds(invalidIds);

      // Show validation errors with component identification
      const errorMessages = invalidComponents.map((result) => {
        const componentLabel = result.item.data.type.replace('-', ' ').toUpperCase();
        const componentText = getComponentDisplayText(result.item);
        return `${componentLabel} "${componentText}" (Position ${result.item.data.position + 1}): ${result.validation.errors.join(', ')}`;
      });

      // Scroll to first invalid component
      setTimeout(() => {
        const firstInvalidElement = document.querySelector(`[data-component-id="${invalidIds[0]}"]`);
        if (firstInvalidElement) {
          firstInvalidElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);

      toast({
        title: `Validation Failed (${invalidComponents.length} ${invalidComponents.length === 1 ? 'component' : 'components'})`,
        description: `Please fix the following errors:\n${errorMessages.slice(0, 2).join('\n')}${errorMessages.length > 2 ? `\n...and ${errorMessages.length - 2} more` : ''}`,
        variant: "destructive"
      });
      return null;
    }

    // Clear invalid component IDs if validation passes
    setInvalidComponentIds([]);

    // Generate JSON output
    const checklistJson = {
      metadata: {
        name: checklistData?.name || "Untitled Checklist",
        version: "1.0",
        createdAt: new Date().toISOString(),
        totalComponents: items.length,
        modes: {
          communicate: items.filter(item => ['header', 'section-header', 'text-body'].includes(item.data.type)).length,
          feedback: items.filter(item => !['header', 'section-header', 'text-body'].includes(item.data.type)).length
        }
      },
      components: items.map(item => ({
        id: item.id,
        type: item.data.type,
        position: item.data.position,
        data: item.data,
        validation: {
          isValid: true,
          lastValidated: new Date().toISOString()
        }
      }))
    };

    return checklistJson;
  };

  const handleSaveChecklist = async () => {
    try {
      const checklistJson = validateAndGenerateJson();
      if (!checklistJson) return;

      // Log the JSON to console for debugging
      console.log("Checklist JSON:", JSON.stringify(checklistJson, null, 2));

      // Save checklist to API using ApiService PATCH method
      if (id) {
        const result = await apiService.patch(`/checklists/${id}`, { value: checklistJson });
        console.log("API Response:", result);
      }

      toast({
        title: "Checklist Saved",
        description: `Your checklist has been saved successfully with ${items.length} components.`,
      });

    } catch (error) {
      console.error("Save error:", error);
      toast({
        title: "Error",
        description: "Failed to save checklist. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handlePublishChecklist = async () => {
    try {
      const checklistJson = validateAndGenerateJson();
      if (!checklistJson) return;

      // Log the JSON to console for debugging
      console.log("Publishing Checklist JSON:", JSON.stringify(checklistJson, null, 2));

      // Publish checklist to API using ApiService PATCH method with status Published
      if (id) {
        const result = await apiService.patch(`/checklists/${id}`, {
          status: "Published",
          value: checklistJson
        });
        console.log("Publish API Response:", result);
      }

      toast({
        title: "Checklist Published",
        description: `Your checklist has been published successfully with ${items.length} components.`,
      });

    } catch (error) {
      console.error("Publish error:", error);
      toast({
        title: "Error",
        description: "Failed to publish checklist. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleGoBack = () => {
    navigate("/apps/inspection");
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-lg font-medium text-slate-600 dark:text-slate-400">Loading checklist...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Fixed Header */}
      <div className="flex-shrink-0 bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700 shadow-lg">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGoBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Checklists
              </Button>
              <div className="h-6 w-px bg-slate-300 dark:bg-slate-600"></div>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">📋</span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-slate-800 dark:text-slate-200">
                    Curate Checklist
                  </h1>
                  <p className="text-sm text-slate-600 dark:text-slate-400">
                    {checklistData?.name || "Untitled Checklist"}
                  </p>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2 px-3 py-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300">
                  {items.length} components
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
                onClick={() => setPreviewOpen(true)}
              >
                <Eye className="h-4 w-4" />
                Preview
              </Button>
              <Button
                variant="outline"
                onClick={handleSaveChecklist}
                disabled={items.length === 0}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                Save
              </Button>
              <Button
                onClick={handlePublishChecklist}
                disabled={items.length === 0}
                className="flex items-center gap-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Upload className="h-4 w-4" />
                Publish
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex flex-1 overflow-hidden" onDragEnd={handleDragEnd}>
        <Sidebar
          onDragStart={handleDragStart}
        />

        <DroppableArea
          items={items}
          onAddItem={handleAddItem}
          onRemoveItem={handleRemoveItem}
          onUpdateItem={handleUpdateItem}
          onReorderItems={handleReorderItems}
          invalidComponentIds={invalidComponentIds}
        />
      </div>

      {/* Mobile Preview */}
      <MobilePreview
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        items={items}
      />
    </div>
  );
};

export default ChecklistCurationPage;
